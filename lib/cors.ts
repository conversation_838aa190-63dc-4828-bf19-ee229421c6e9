// lib/cors.ts
import Cors from 'cors';

// Initialize the cors middleware
const cors = Cors({
    origin: '*', // Replace '*' with your specific origin(s) for better security
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
});

// Helper method to wait for a middleware to execute before continuing
export function runMiddleware(req: any, res: any, fn: (arg0: any, arg1: any, arg2: (result: any) => void) => void) {
    return new Promise((resolve, reject) => {
        fn(req, res, (result) => {
            if (result instanceof Error) {
                return reject(result);
            }
            return resolve(result);
        });
    });
}

export default cors;
