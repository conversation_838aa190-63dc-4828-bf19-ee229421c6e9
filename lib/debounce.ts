// function debounce(func: (...args: any[]) => void, timeout = 2000) {
//     let timer: NodeJS.Timeout;
//     return (...args: any[]) => {
//         clearTimeout(timer);
//         timer = setTimeout(() => { func.apply(this, args); }, timeout);
//     };
// }


export function debounce(func: (...args: any[]) => void, timeout = 2000) {
    let timer: NodeJS.Timeout;
    return function (this: any, ...args: any[]) {
        clearTimeout(timer);
        timer = setTimeout(() => { func.apply(this, args); }, timeout);
    };
}