// import PDFDocument from 'pdfkit';
// import { Payment } from '@/app/types/payment';

// export async function createPDF(payment: Payment) {
//     return new Promise<Buffer>((resolve, reject) => {
//         const doc = new PDFDocument();
//         const buffers: Uint8Array[] = [];

//         doc.on('data', buffers.push.bind(buffers));
//         doc.on('end', () => {
//             const pdfData = Buffer.concat(buffers);
//             resolve(pdfData);
//         });

//         doc.fontSize(20).text('Payslip', { align: 'center' });
//         doc.moveDown();
//         doc.fontSize(14).text(`Employee: ${payment.firstName} ${payment.lastName}`);
//         doc.text(`Payment ID: ${payment._id}`);
//         doc.text(`Final Payment: ${payment.final_amount}`);
//         doc.text(`Status: ${payment.status}`);

//         doc.end();
//     });
// }
