import DailyAttendancePayment from '@/app/models/DailyAttendancePayment'; // Import the new model

interface AttendanceRecord {
    _id: string;
    employee: {
        _id: string;
        firstName: string;
        lastName: string;
    };
    salary: string;
    date: Date;
}



export async function saveAttendancePayment(attendanceRecord: AttendanceRecord) {
    const paymentRecord = new DailyAttendancePayment({
        employee: {
            _id: attendanceRecord.employee._id,
            firstName: attendanceRecord.employee.firstName,
            lastName: attendanceRecord.employee.lastName,
        },
        salary: attendanceRecord.salary,
        date: `${attendanceRecord.date.toDateString()}, ${attendanceRecord.date.getDay()}, ${attendanceRecord.date.getFullYear()}`,
        attendanceId: attendanceRecord._id,
        status: 'pending', // Set initial status
    });

    await paymentRecord.save();
    return paymentRecord;
}
