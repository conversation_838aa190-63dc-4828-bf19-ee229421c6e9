// app/utils/aggregateAttendance.ts

import { Attendance } from "@/app/types/attendance"
import { MonthlyAttendance } from "@/app/types/monthlyAttendance"

export function aggregateMonthlyAttendance(
  attendanceRecords: Attendance[]
): MonthlyAttendance[] {
  const attendanceMap: Record<string, MonthlyAttendance> = {}

  attendanceRecords.forEach((record) => {
    const month = record.date.substring(0, 7) // Extract month in YYYY-MM format
    const key = `${record.employeeId}-${month}`

    if (!attendanceMap[key]) {
      attendanceMap[key] = {
        employeeId: record.employeeId,
        firstname: record.firstname,
        lastname: record.lastname,
        month,
        presentDays: 0,
        absentDays: 0,
        lateDays: 0,
        excusedDays: 0,
      }
    }

    const monthlyRecord = attendanceMap[key]
    if (record.status === "present") {
      monthlyRecord.presentDays += 1
    } else if (record.status === "absent") {
      monthlyRecord.absentDays += 1
    } else if (record.status === "late") {
      monthlyRecord.lateDays += 1
    } else if (record.status === "excused") {
      monthlyRecord.excusedDays += 1
    }
  })

  return Object.values(attendanceMap)
}
