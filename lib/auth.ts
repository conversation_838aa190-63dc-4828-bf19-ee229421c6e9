// utils/auth.ts
import { jwtDecode } from 'jwt-decode';
import { UserRole } from '../context/types';

interface User {
    role: UserRole;
    // add other user properties if necessary
}

export const getUserFromToken = (token: string): User | null => {
    try {
        return jwtDecode<User>(token);
    } catch (error) {
        console.error('Error decoding token:', error);
        return null;
    }
};
