// // // lib/fetchData.ts
// // import { useQuery } from "@tanstack/react-query";

// // // Fetch function for employees
// // const fetchEmployees = async () => {
// //     const response = await fetch("/api/employees/getAllEmployees");
// //     if (!response.ok) {
// //         throw new Error("Failed to fetch employees");
// //     }
// //     return response.json();
// // };

// // // Fetch function for payments
// // const fetchPayments = async () => {
// //     const response = await fetch("/api/payments/getAllPayments");
// //     if (!response.ok) {
// //         throw new Error("Failed to fetch payments");
// //     }
// //     return response.json();
// // };

// // // Fetch function for departments
// // const fetchDepartments = async () => {
// //     const response = await fetch("/api/departments/getAllDepartments");
// //     if (!response.ok) {
// //         throw new Error("Failed to fetch departments");
// //     }
// //     return response.json();
// // };

// // // Fetch function for approved payments
// // const fetchApprovedPayments = async () => {
// //     const response = await fetch("/api/payments/getApprovedPayments");
// //     if (!response.ok) {
// //         throw new Error("Failed to fetch approved payments");
// //     }
// //     return response.json();
// // };

// // // Fetch function for loans
// // const fetchLoans = async () => {
// //     const response = await fetch("/api/loans/getAllLoans");
// //     if (!response.ok) {
// //         throw new Error("Failed to fetch loans");
// //     }
// //     return response.json();
// // };

// // // Hook to get employees with caching
// // export const useEmployees = () => {
// //     return useQuery({
// //         queryKey: ["employees"],
// //         queryFn: fetchEmployees,
// //         staleTime: 1000 * 60 * 5, // 5 minutes
// //     });
// // };

// // // Hook to get payments with caching
// // export const usePayments = () => {
// //     return useQuery({
// //         queryKey: ["payments"],
// //         queryFn: fetchPayments,
// //         staleTime: 1000 * 60 * 5, // 5 minutes
// //     });
// // };

// // // Hook to get departments with caching
// // export const useDepartments = () => {
// //     return useQuery({
// //         queryKey: ["departments"],
// //         queryFn: fetchDepartments,
// //         staleTime: 1000 * 60 * 5, // 5 minutes
// //     });
// // };

// // // Hook to get approved payments with caching
// // export const useApprovedPayments = () => {
// //     return useQuery({
// //         queryKey: ["approvedPayments"],
// //         queryFn: fetchApprovedPayments,
// //         staleTime: 1000 * 60 * 5, // 5 minutes
// //     });
// // };

// // // Hook to get loans with caching
// // export const useLoans = () => {
// //     return useQuery({
// //         queryKey: ["loans"],
// //         queryFn: fetchLoans,
// //         staleTime: 1000 * 60 * 5, // 5 minutes
// //     });
// // };



// // lib/fetchData.ts
// import { useQuery } from "@tanstack/react-query";

// // Base fetch function
// export const fetchData = async (url: string, errorMessage: string) => {
//     const response = await fetch(url);
//     if (!response.ok) {
//         throw new Error(errorMessage);
//     }
//     return response.json();
// };

// // Fetch function for employees
// export const fetchEmployees = () => fetchData("/api/employees/getAllEmployees", "Failed to fetch employees");

// // Fetch function for payments
// export const fetchPayments = () => fetchData("/api/payments/getAllPayments", "Failed to fetch payments");

// // Fetch function for departments
// export const fetchDepartments = () => fetchData("/api/departments/getAllDepartments", "Failed to fetch departments");

// // Fetch function for approved payments
// export const fetchApprovedPayments = () => fetchData("/api/payments/getApprovedPayments", "Failed to fetch approved payments");

// // Fetch function for loans
// export const fetchLoans = () => fetchData("/api/loans/getAllLoans", "Failed to fetch loans");

// // Hook to get employees with caching
// export const useEmployees = () => {
//     return useQuery({
//         queryKey: ["employees"],
//         queryFn: fetchEmployees,
//         staleTime: 1000 * 60 * 5, // 5 minutes
//     });
// };

// // Hook to get payments with caching
// export const usePayments = () => {
//     return useQuery({
//         queryKey: ["payments"],
//         queryFn: fetchPayments,
//         staleTime: 1000 * 60 * 5, // 5 minutes
//     });
// };

// // Hook to get departments with caching
// export const useDepartments = () => {
//     return useQuery({
//         queryKey: ["departments"],
//         queryFn: fetchDepartments,
//         staleTime: 1000 * 60 * 5, // 5 minutes
//     });
// };

// // Hook to get approved payments with caching
// export const useApprovedPayments = () => {
//     return useQuery({
//         queryKey: ["approvedPayments"],
//         queryFn: fetchApprovedPayments,
//         staleTime: 1000 * 60 * 5, // 5 minutes
//     });
// };

// // Hook to get loans with caching
// export const useLoans = () => {
//     return useQuery({
//         queryKey: ["loans"],
//         queryFn: fetchLoans,
//         staleTime: 1000 * 60 * 5, // 5 minutes
//     });
// };




// lib/fetchData.ts
import { useQuery } from "@tanstack/react-query";

// Base fetch function
export const fetchData = async (url: string, errorMessage: string) => {
    const response = await fetch(url);
    if (!response.ok) {
        throw new Error(errorMessage);
    }
    return response.json();
};


// // Base fetch function
// export const fetchData = async (url: string, errorMessage: string) => {
//     const response = await fetch(url);
//     if (!response.ok) {
//         throw new Error(errorMessage);
//     }
//     return response.json();
// };

// Fetch combined employee and attendance data
export const fetchCombinedEmployeeAttendance = () => fetchData("/api/attendance/getCombinedEmployeeAttendance", "Failed to fetch combined employee and attendance data");



// Fetch function for employees
export const fetchEmployees = () => fetchData("/api/employees/getAllEmployees", "Failed to fetch employees");

// Fetch function for payments
export const fetchPayments = () => fetchData("/api/payments/getAllPayments", "Failed to fetch payments");

// Fetch function for departments
export const fetchDepartments = () => fetchData("/api/departments/getAllDepartments", "Failed to fetch departments");

// Fetch function for approved payments
export const fetchApprovedPayments = () => fetchData("/api/payments/getApprovedPayments", "Failed to fetch approved payments");

// Fetch function for loans
export const fetchLoans = () => fetchData("/api/loans/getAllLoans", "Failed to fetch loans");

// Hook to get employees with caching
export const useEmployees = () => {
    return useQuery({
        queryKey: ["employees"],
        queryFn: fetchEmployees,
        staleTime: 1000 * 60 * 5, // 5 minutes
    });
};

// Hook to get payments with caching
export const usePayments = () => {
    return useQuery({
        queryKey: ["payments"],
        queryFn: fetchPayments,
        staleTime: 1000 * 60 * 5, // 5 minutes
    });
};

// Hook to get departments with caching
export const useDepartments = () => {
    return useQuery({
        queryKey: ["departments"],
        queryFn: fetchDepartments,
        staleTime: 1000 * 60 * 5, // 5 minutes
    });
};


// Hook to get combined data with caching
export const useCombinedEmployeeAttendance = () => {
    return useQuery({
        queryKey: ["combinedEmployeeAttendance"],
        queryFn: fetchCombinedEmployeeAttendance,
        staleTime: 1000 * 60 * 5, // 5 minutes
    });
};


// Hook to get approved payments with caching
export const useApprovedPayments = () => {
    return useQuery({
        queryKey: ["approvedPayments"],
        queryFn: fetchApprovedPayments,
        staleTime: 1000 * 60 * 5, // 5 minutes
    });
};

// Hook to get loans with caching
export const useLoans = () => {
    return useQuery({
        queryKey: ["loans"],
        queryFn: fetchLoans,
        staleTime: 1000 * 60 * 5, // 5 minutes
    });
};


