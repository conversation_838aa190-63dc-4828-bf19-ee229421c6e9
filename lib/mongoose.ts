import mongoose from 'mongoose';

const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');
}

interface Cached {
  conn: typeof mongoose | null;
  promise: Promise<typeof mongoose> | null;
}

let cached: Cached = (global as any).mongoose;

if (!cached) {
  cached = (global as any).mongoose = { conn: null, promise: null };
}

async function dbConnect(): Promise<typeof mongoose> {
  if (cached.conn) {
    return cached.conn;
  }

  if (!cached.promise) {
    const opts = {
      bufferCommands: false,
    };

    cached.promise = mongoose.connect(MONGODB_URI!, opts).then((mongoose) => {
      return mongoose;
    });
  }

  cached.conn = await cached.promise;
  return cached.conn;
}

export default dbConnect;



// import mongoose from 'mongoose';

// const MONGODB_URI = process.env.MONGODB_URI;

// if (!MONGODB_URI) {
//   throw new Error('Please define the MONGODB_URI environment variable inside .env.local');
// }

// interface Cached {
//   conn: typeof mongoose | null;
//   promise: Promise<typeof mongoose> | null;
// }

// let cached: Cached = (global as any).mongoose;

// if (!cached) {
//   cached = (global as any).mongoose = { conn: null, promise: null };
// }

// async function dbConnect(): Promise<typeof mongoose> {
//   if (cached.conn) {
//     return cached.conn;
//   }

//   if (!cached.promise) {
//     const opts = {
//       bufferCommands: false,
//       useNewUrlParser: true,
//       useUnifiedTopology: true,
//       serverSelectionTimeoutMS: 50000, // Increase the timeout to 50 seconds
//     };

//     console.log('Attempting to connect to MongoDB');

//     cached.promise = mongoose.connect(MONGODB_URI!, opts).then((mongoose) => {
//       console.log('Mongoose connected to MongoDB');
//       return mongoose;
//     }).catch((err) => {
//       console.error('Error connecting to MongoDB:', err);
//       throw err;
//     });
//   }

//   try {
//     cached.conn = await cached.promise;
//   } catch (err) {
//     console.error('Failed to establish a cached connection:', err);
//     throw err;
//   }

//   mongoose.connection.on('connected', () => {
//     console.log('Mongoose connected to the database');
//   });

//   mongoose.connection.on('error', (err) => {
//     console.error(`Mongoose connection error: ${err}`);
//   });

//   mongoose.connection.on('disconnected', () => {
//     console.log('Mongoose disconnected from the database');
//   });

//   return cached.conn;
// }

// export default dbConnect;
