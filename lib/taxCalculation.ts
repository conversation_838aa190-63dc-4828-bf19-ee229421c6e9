// // utils/taxCalculation.ts
// export function calculateTax(amount: number): number {
//     let tax = 0;
//     if (amount > 75000) {
//         tax += (amount - 75000) * 0.30;
//         amount = 75000;
//     }
//     if (amount > 35000) {
//         tax += (amount - 35000) * 0.15;
//     }
//     return tax;
// }


// // utils/taxCalculation.ts
// export function calculateTax(amount: number): number {
//     let tax = 0;
//     if (amount > 75000) {
//         tax += (amount - 75000) * 0.30;
//         amount = 75000;
//     }
//     if (amount > 35000) {
//         tax += (amount - 35000) * 0.15;
//     }
//     return tax;
// }



// lib/taxCalculation.ts

// Function to calculate tax based on the amount
export function calculateTax(amount: number): number {
    let tax = 0;

    if (amount > 2050000) {
        tax += (amount - 2050000) * 0.35;
        amount = 2050000;
    }
    if (amount > 350000) {
        tax += (amount - 350000) * 0.30;
        amount = 350000;
    }
    if (amount > 150000) {
        tax += (amount - 150000) * 0.25;
    }
    // No tax for the first 150,000
    return tax;
}

// Function to calculate the number of working days in a given month and year
export function calculateWorkingDays(month: number, year: number): number {
    const daysInMonth = new Date(year, month, 0).getDate();
    let workingDays = 0;

    for (let day = 1; day <= daysInMonth; day++) {
        const currentDate = new Date(year, month - 1, day);
        const dayOfWeek = currentDate.getDay();
        if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Exclude Sundays (0) and Saturdays (6)
            workingDays++;
        }
    }

    return workingDays;
}

// Function to calculate the total loan deductions based on provided loan deduction data
export function calculateLoanDeductions(loanDeduction: any): number {
    const predefinedLoanDeductionsTotal = Object.values(loanDeduction).reduce((total: number, value) => {
        if (typeof value === 'string') return total + parseFloat(value || '0');
        if (Array.isArray(value)) {
            return total + value.reduce((subTotal: number, obj) => subTotal + parseFloat(obj.amount || '0'), 0);
        }
        return total;
    }, 0);

    return predefinedLoanDeductionsTotal;
}
