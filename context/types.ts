
//   // context/types.ts
// export type UserRole = 'admin' | 'author' | 'editor' | 'accountant' | 'technician' | 'sales' | 'manager' | 'teacher' | 'headteacher' | 'marketing';

// // types.ts
// export type UserRole = 'admin' | 'ceoadmin' | 'accountant' | 'hrmanager' | 'fieldsupervisor';

export type UserRole = 'admin' | 'ceoadmin' | 'accountant' | 'accountSupervisor' | 'hrmanager' | 'fieldsupervisor' | 'author' | 'editor' | 'sales' | 'manager' | 'marketing';

export interface User {
    username: string;
    role: UserRole;
    // add other user properties if necessary
}


