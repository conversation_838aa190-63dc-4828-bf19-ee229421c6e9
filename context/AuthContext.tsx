

// context/AuthContext.tsx
"use client"
import { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { jwtDecode } from 'jwt-decode'; // Fixing the import here
import Cookies from 'js-cookie';
import { UserRole } from './types';

interface User {
  role: UserRole;
  // add other user properties if necessary
}

interface AuthContextProps {
  isAuthenticated: boolean;
  user: User | null;
  login: (token: string) => void;
  logout: () => void;
  getUser: () => User | null; // Add this method to AuthContext
}

const AuthContext = createContext<AuthContextProps | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [authState, setAuthState] = useState(() => {
    const token = Cookies.get('token');
    if (token) {
      return { isAuthenticated: true, user: jwtDecode<User>(token) };
    } else {
      return { isAuthenticated: false, user: null };
    }
  });

  const login = (token: string) => {
    Cookies.set('token', token);
    setAuthState({ isAuthenticated: true, user: jwtDecode<User>(token) });
  };

  const logout = () => {
    Cookies.remove('token');
    setAuthState({ isAuthenticated: false, user: null });
  };

  const getUser = () => {
    const token = Cookies.get('token');
    if (token) {
      return jwtDecode<User>(token);
    }
    return null;
  };

  return (
    <AuthContext.Provider value={{ ...authState, login, logout, getUser }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

