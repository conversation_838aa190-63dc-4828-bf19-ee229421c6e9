// // hooks/useRequireAuth.ts
// "use client"
// import { useEffect } from 'react';
// import { useRouter } from 'next/navigation';
// import { useAuth } from '@/context/AuthContext';

// const useRequireAuth = (roleRequired: string) => {
//   const { isAuthenticated, user } = useAuth();
//   const router = useRouter();

//   useEffect(() => {
//     if (!isAuthenticated || user?.role !== roleRequired) {
//       // router.push('/login');
//       router.push('/auth');
//     }
//   }, [isAuthenticated, user, router, roleRequired]);

//   return { isAuthenticated, user };
// };

// export default useRequireAuth;


import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';

const useRequireAuth = (roleRequired: string) => {
  const { isAuthenticated, user } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth');
    } else if (user?.role !== roleRequired) {
      router.push('/403');
    }
  }, [isAuthenticated, user, roleRequired, router]);

  return { isAuthenticated, user };
};

export default useRequireAuth;
