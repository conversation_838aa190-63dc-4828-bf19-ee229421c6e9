// // context/withAuth.tsx
// "use client";
// import React, { ComponentType, useEffect } from 'react';
// import { useRouter } from 'next/navigation';
// import { useAuth } from '@/context/AuthContext';
// import { UserRole } from '@/context/types';

// interface RouteConfig {
//   path: string;
//   roles: UserRole[];
// }

// const withAuth = <P extends object>(
//   WrappedComponent: ComponentType<P>,
//   allowedRoles: UserRole[]
// ): React.FC<P> => {
//   const WithAuthentication: React.FC<P> = (props: P) => {
//     const router = useRouter();
//     const { isAuthenticated, user } = useAuth();

//     useEffect(() => {
//       if (!isAuthenticated || (allowedRoles.length > 0 && !allowedRoles.includes(user?.role!))) {
//         router.replace('/login');
//       }
//     }, [isAuthenticated, user, router, allowedRoles]);

//     return isAuthenticated && (allowedRoles.length === 0 || allowedRoles.includes(user?.role!)) ? (
//       <WrappedComponent {...props} />
//     ) : null;
//   };

//   WithAuthentication.displayName = `withAuth(${WrappedComponent.displayName || WrappedComponent.name})`;

//   return WithAuthentication;
// };

// export default withAuth;


import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '@/context/AuthContext';
import { UserRole } from './types';

const withAuth = (Component: React.ComponentType, allowedRoles: UserRole[] = []) => {
  const withAuth = (Component: React.ComponentType, allowedRoles: UserRole[] = []) => {
    const WithAuthComponent: React.FC<any> = (props) => {
      const { isAuthenticated, user } = useAuth();
      const router = useRouter();

      useEffect(() => {
        if (!isAuthenticated || (allowedRoles.length > 0 && !allowedRoles.includes(user?.role!))) {
          router.replace('/login');
        }
      }, [isAuthenticated, user, router, allowedRoles]);

      if (!isAuthenticated || (allowedRoles.length > 0 && !allowedRoles.includes(user?.role!))) {
        return null; // Optionally, return a loading spinner or a message
      }

      return <Component {...props} />;
    };

    WithAuthComponent.displayName = `WithAuth(${Component.displayName || Component.name})`;

    return WithAuthComponent;
  };
};

export default withAuth;




// import { useAuth } from '@/context/AuthContext';
// import { useRouter } from 'next/router';
// import React, { useEffect } from 'react';

// const withAuth = (WrappedComponent: React.FC) => {
//   return (props: any) => {
//     const { isAuthenticated } = useAuth();
//     const router = useRouter();

//     useEffect(() => {
//       if (!isAuthenticated) {
//         router.push('/login');
//       }
//     }, [isAuthenticated, router]);

//     if (!isAuthenticated) {
//       return null; // or a loading spinner
//     }

//     return <WrappedComponent {...props} />;
//   };
// };

// export default withAuth;