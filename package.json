{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.1.3", "@hookform/resolvers": "^3.7.0", "@pdfme/generator": "^4.2.2", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@tanstack/react-query": "^5.51.1", "@tanstack/react-table": "^8.19.2", "@types/bcryptjs": "^2.4.6", "axios": "^1.7.2", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cors": "^2.8.5", "date-fns": "^3.6.0", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lottie-react": "^2.4.0", "lucide-react": "^0.400.0", "mongodb": "^5.9.2", "mongoose": "^8.4.5", "next": "14.2.4", "next-auth": "^5.0.0-beta.19", "next-themes": "^0.3.0", "pdfkit": "^0.15.0", "puppeteer": "^22.13.1", "react": "^18", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-hook-form": "^7.52.1", "react-icons": "^5.2.1", "react-pdf-html": "^2.0.5", "react-query": "^3.39.3", "recharts": "^2.12.7", "styled-components": "^6.1.12", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "ws": "^8.18.0", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.6", "@types/next-auth": "^3.15.0", "@types/node": "^20.14.10", "@types/pdfkit": "^0.13.4", "@types/react": "^18.3.3", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.5.3"}}