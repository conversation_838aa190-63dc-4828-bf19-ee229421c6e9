// import { NextResponse } from 'next/server';
// import type { NextRequest } from 'next/server';

// export function middleware(req: NextRequest) {
//   const token = req.cookies.get('token');

//   if (!token && req.nextUrl.pathname.startsWith('/dashboard')) {
//     return NextResponse.redirect(new URL('/auth', req.url));
//   }
// }

// export const config = {
//   matcher: ['/dashboard/:path*'],
// };


// // middleware.ts
// import { NextResponse } from 'next/server';
// import type { NextRequest } from 'next/server';
// import jwt from 'jsonwebtoken';

// export async function middleware(request: NextRequest) {
//   const token = request.cookies.get('token');

//   if (!token) {
//     return NextResponse.redirect(new URL('/login', request.url));
//   }

//   try {
//     const decoded = jwt.verify(token, process.env.JWT_SECRET) as { role: string };

//     const role = decoded.role;
//     const pathname = request.nextUrl.pathname;

//     if (role === 'admin' || role === 'ceoadmin') {
//       return NextResponse.next();
//     } else if (role === 'accountant' && ['/dashboard/approvedloans', '/dashboard/attendancepayments'].includes(pathname)) {
//       return NextResponse.next();
//     } else if (role === 'hrmanager' && pathname === '/dashboard/anualleave') {
//       return NextResponse.next();
//     } else if (role === 'fieldsupervisor' && pathname === '/dashboard/alltasks') {
//       return NextResponse.next();
//     } else {
//       return NextResponse.redirect(new URL('/403', request.url));
//     }
//   } catch (err) {
//     return NextResponse.redirect(new URL('/login', request.url));
//   }
// }

// export const config = {
//   matcher: ['/dashboard/:path*'],
// };


// // middleware.ts
// import { NextResponse } from 'next/server';
// import type { NextRequest } from 'next/server';
// import jwt, { JwtPayload } from 'jsonwebtoken';

// interface DecodedToken extends JwtPayload {
//   role: string;
// }

// export async function middleware(request: NextRequest) {
//   const token = request.cookies.get('token');

//   if (!token) {
//     return NextResponse.redirect(new URL('/login', request.url));
//   }

//   try {
//     const decoded = jwt.verify(token, process.env.JWT_SECRET) as unknown as DecodedToken;

//     const role = decoded.role;
//     const pathname = request.nextUrl.pathname;

//     if (role === 'admin' || role === 'ceoadmin') {
//       return NextResponse.next();
//     } else if (role === 'accountant' && ['/dashboard/approvedloans', '/dashboard/attendancepayments'].includes(pathname)) {
//       return NextResponse.next();
//     } else if (role === 'hrmanager' && pathname === '/dashboard/anualleave') {
//       return NextResponse.next();
//     } else if (role === 'fieldsupervisor' && pathname === '/dashboard/alltasks') {
//       return NextResponse.next();
//     } else {
//       return NextResponse.redirect(new URL('/403', request.url));
//     }
//   } catch (err) {
//     return NextResponse.redirect(new URL('/login', request.url));
//   }
// }

// export const config = {
//   matcher: ['/dashboard/:path*'],
// };




// // middleware.ts
// import { NextResponse } from 'next/server';
// import type { NextRequest } from 'next/server';
// import jwt, { JwtPayload } from 'jsonwebtoken';

// interface DecodedToken extends JwtPayload {
//   role: string;
// }

// export async function middleware(request: NextRequest) {
//   const tokenCookie = request.cookies.get('token');

//   if (!tokenCookie) {
//     return NextResponse.redirect(new URL('/auth', request.url));
//   }

//   const token = tokenCookie.value;

//   try {
//     const decoded = jwt.verify(token, process.env.JWT_SECRET!) as unknown as DecodedToken;

//     const role = decoded.role;
//     const pathname = request.nextUrl.pathname;

//     if (role === 'admin' || role === 'ceoadmin') {
//       return NextResponse.next();
//     } else if (role === 'accountant' && ['/dashboard/approvedloans', '/dashboard/attendancepayments'].includes(pathname)) {
//       return NextResponse.next();
//     } else if (role === 'hrmanager' && pathname === '/dashboard/anualleave') {
//       return NextResponse.next();
//     } else if (role === 'fieldsupervisor' && pathname === '/dashboard/alltasks') {
//       return NextResponse.next();
//     } else {
//       return NextResponse.redirect(new URL('/403', request.url));
//     }
//   } catch (err) {
//     return NextResponse.redirect(new URL('/auth', request.url));
//   }
// }

// export const config = {
//   matcher: ['/dashboard/:path*'],
// };




// // middleware.ts
// import { NextResponse } from 'next/server';
// import type { NextRequest } from 'next/server';
// import jwt, { JwtPayload } from 'jsonwebtoken';

// interface DecodedToken extends JwtPayload {
//   role: string;
// }

// export async function middleware(request: NextRequest) {
//   const tokenCookie = request.cookies.get('token');

//   console.log('Token Cookie:', tokenCookie);

//   if (!tokenCookie) {
//     return NextResponse.redirect(new URL('/auth', request.url));
//   }

//   const token = tokenCookie.value;

//   try {
//     const decoded = jwt.verify(token, process.env.JWT_SECRET!) as unknown as DecodedToken;

//     console.log('Decoded Token:', decoded);

//     const role = decoded.role;
//     const pathname = request.nextUrl.pathname;

//     console.log('Role:', role);
//     console.log('Pathname:', pathname);

//     if (role === 'admin' || role === 'ceoadmin') {
//       return NextResponse.next();
//     } else if (role === 'accountant' && ['/dashboard/approvedloans', '/dashboard/attendancepayments'].includes(pathname)) {
//       return NextResponse.next();
//     } else if (role === 'hrmanager' && pathname === '/dashboard/anualleave') {
//       return NextResponse.next();
//     } else if (role === 'fieldsupervisor' && pathname === '/dashboard/alltasks') {
//       return NextResponse.next();
//     } else {
//       return NextResponse.redirect(new URL('/403', request.url));
//     }
//   } catch (err) {
//     console.log('JWT Verification Error:', err);
//     return NextResponse.redirect(new URL('/auth', request.url));
//   }
// }

// export const config = {
//   matcher: ['/dashboard/:path*'],
// };



// // middleware.ts
// import { NextResponse } from 'next/server';
// import type { NextRequest } from 'next/server';
// import jwt, { JwtPayload } from 'jsonwebtoken';

// interface DecodedToken extends JwtPayload {
//   role: string;
// }

// export async function middleware(request: NextRequest) {
//   const tokenCookie = request.cookies.get('token');

//   if (!tokenCookie) {
//     return NextResponse.redirect(new URL('/auth', request.url));
//   }

//   const token = tokenCookie.value;

//   try {
//     const decoded = jwt.verify(token, process.env.JWT_SECRET!) as unknown as DecodedToken;

//     const role = decoded.role;
//     const pathname = request.nextUrl.pathname;

//     if (role === 'admin' || role === 'ceoadmin') {
//       return NextResponse.next();
//     } else if (role === 'accountant' && ['/dashboard/approvedloans', '/dashboard/attendancepayments'].includes(pathname)) {
//       return NextResponse.next();
//     } else if (role === 'hrmanager' && pathname === '/dashboard/anualleave') {
//       return NextResponse.next();
//     } else if (role === 'fieldsupervisor' && pathname === '/dashboard/alltasks') {
//       return NextResponse.next();
//     } else {
//       return NextResponse.redirect(new URL('/403', request.url));
//     }
//   } catch (err) {
//     return NextResponse.redirect(new URL('/auth', request.url));
//   }
// }

// export const config = {
//   matcher: ['/dashboard/:path*'],
// };



// // middleware.ts
// import { NextResponse } from 'next/server';
// import type { NextRequest } from 'next/server';
// import jwt, { JwtPayload } from 'jsonwebtoken';
// import { UserRole } from './context/types';

// interface DecodedToken extends JwtPayload {
//   role: UserRole;
// }

// export async function middleware(request: NextRequest) {
//   const tokenCookie = request.cookies.get('token');

//   if (!tokenCookie) {
//     return NextResponse.redirect(new URL('/auth', request.url));
//   }

//   const token = tokenCookie.value;

//   try {
//     const decoded = jwt.verify(token, process.env.JWT_SECRET!) as DecodedToken;
//     const role = decoded.role;
//     const pathname = request.nextUrl.pathname;

//     if (role === 'admin' || role === 'ceoadmin') {
//       return NextResponse.next();
//     } else if (role === 'accountant' && ['/dashboard/approvedloans', '/dashboard/attendancepayments'].includes(pathname)) {
//       return NextResponse.next();
//     } else if (role === 'hrmanager' && pathname === '/dashboard/anualleave') {
//       return NextResponse.next();
//     } else if (role === 'fieldsupervisor' && pathname === '/dashboard/alltasks') {
//       return NextResponse.next();
//     } else {
//       return NextResponse.redirect(new URL('/403', request.url));
//     }
//   } catch (err) {
//     return NextResponse.redirect(new URL('/auth', request.url));
//   }
// }

// export const config = {
//   matcher: ['/dashboard/:path*'],
// };


// // middleware.ts
// import { NextResponse } from 'next/server';
// import type { NextRequest } from 'next/server';
// import jwt, { JwtPayload } from 'jsonwebtoken';
// import { UserRole } from './context/types';

// interface DecodedToken extends JwtPayload {
//   role: UserRole;
// }

// export async function middleware(request: NextRequest) {
//   const tokenCookie = request.cookies.get('token');

//   if (!tokenCookie) {
//     return NextResponse.redirect(new URL('/auth', request.url));
//   }

//   const token = tokenCookie.value;

//   try {
//     const decoded = jwt.verify(token, process.env.JWT_SECRET!) as DecodedToken;
//     const role = decoded.role;
//     const pathname = request.nextUrl.pathname;

//     if (role === 'admin' || role === 'ceoadmin') {
//       return NextResponse.next();
//     } else if (role === 'accountant' && ['/dashboard/approvedloans', '/dashboard/attendancepayments'].includes(pathname)) {
//       return NextResponse.next();
//     } else if (role === 'hrmanager' && pathname === '/dashboard/anualleave') {
//       return NextResponse.next();
//     } else if (role === 'fieldsupervisor' && pathname === '/dashboard/alltasks') {
//       return NextResponse.next();
//     } else {
//       return NextResponse.redirect(new URL('/403', request.url));
//     }
//   } catch (err) {
//     console.error('Token verification failed:', err);
//     return NextResponse.redirect(new URL('/auth', request.url));
//   }
// }

// export const config = {
//   matcher: ['/dashboard/:path*'],
// };


// // middleware.ts
// import { NextResponse } from 'next/server';
// import type { NextRequest } from 'next/server';
// import { getUserFromToken } from './lib/auth';

// export async function middleware(request: NextRequest) {
//   const tokenCookie = request.cookies.get('token');

//   if (!tokenCookie) {
//     return NextResponse.redirect(new URL('/auth', request.url));
//   }

//   const token = tokenCookie.value;
//   const user = getUserFromToken(token);

//   if (!user) {
//     return NextResponse.redirect(new URL('/auth', request.url));
//   }

//   const role = user.role;
//   const pathname = request.nextUrl.pathname;

//   if (role === 'admin' || role === 'ceoadmin') {
//     return NextResponse.next();
//   } else if (role === 'accountant' && ['/dashboard/approvedloans', '/dashboard/attendancepayments'].includes(pathname)) {
//     return NextResponse.next();
//   } else if (role === 'hrmanager' && pathname === '/dashboard/anualleave') {
//     return NextResponse.next();
//   } else if (role === 'fieldsupervisor' && pathname === '/dashboard/alltasks') {
//     return NextResponse.next();
//   } else {
//     return NextResponse.redirect(new URL('/403', request.url));
//   }
// }

// export const config = {
//   matcher: ['/dashboard/:path*'],
// };
