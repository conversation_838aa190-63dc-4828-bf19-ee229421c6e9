#!/usr/bin/env tsx

/**
 * TypeScript Script to create a Super Admin user for Kawandama Hills Plantation Management System
 * 
 * Usage: npx tsx scripts/create-super-admin.ts
 * 
 * This script will create a super admin user with the following credentials:
 * Email: <EMAIL>
 * Password: @Admin2020
 * Role: SUPER_ADMIN
 * Status: ACTIVE
 */

import { config } from 'dotenv';
import { join } from 'path';
import { createInterface } from 'readline';

// Load environment variables
config({ path: join(__dirname, '..', '.env') });

// Import the authentication functions and types
import { registerUser } from '../lib/backend/auth/auth';
import { connectToDatabase } from '../lib/backend/database/connection';
import { UserRole, UserStatus } from '../types/user-roles';
import User from '../models/User';

// Super Admin user details
const SUPER_ADMIN_DATA = {
  email: '<EMAIL>',
  password: '@Admin2020',
  firstName: '<PERSON>',
  lastName: 'Mhang<PERSON>',
  role: UserRole.SUPER_ADMIN,
  status: UserStatus.ACTIVE,
  department: 'Administration',
  position: 'System Administrator',
  dateOfJoining: new Date(),
  allowMultipleDevices: true,
  trustedDevicesOnly: false,
  singleDeviceLogin: false
};

/**
 * Ask user for confirmation
 */
function askQuestion(question: string): Promise<string> {
  const rl = createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      rl.close();
      resolve(answer);
    });
  });
}

/**
 * Create super admin user using the existing authentication system
 */
async function createSuperAdmin(): Promise<void> {
  try {
    console.log('\n🔍 Checking if super admin already exists...');
    
    // Check if user already exists
    const existingUser = await User.findOne({ email: SUPER_ADMIN_DATA.email });
    
    if (existingUser) {
      console.log('⚠️  Super admin user already exists!');
      console.log(`📧 Email: ${existingUser.email}`);
      console.log(`👤 Name: ${existingUser.firstName} ${existingUser.lastName}`);
      console.log(`🔑 Role: ${existingUser.role}`);
      console.log(`📊 Status: ${existingUser.status}`);
      console.log(`📅 Created: ${existingUser.createdAt}`);
      
      // Ask if user wants to update the existing user
      const answer = await askQuestion('\n❓ Do you want to update the existing user? (y/N): ');
      
      if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
        console.log('\n🔄 Updating existing super admin user...');
        
        // Update the existing user
        existingUser.password = SUPER_ADMIN_DATA.password;
        existingUser.firstName = SUPER_ADMIN_DATA.firstName;
        existingUser.lastName = SUPER_ADMIN_DATA.lastName;
        existingUser.role = SUPER_ADMIN_DATA.role;
        existingUser.status = SUPER_ADMIN_DATA.status;
        existingUser.department = SUPER_ADMIN_DATA.department;
        existingUser.position = SUPER_ADMIN_DATA.position;
        existingUser.allowMultipleDevices = SUPER_ADMIN_DATA.allowMultipleDevices;
        existingUser.trustedDevicesOnly = SUPER_ADMIN_DATA.trustedDevicesOnly;
        existingUser.singleDeviceLogin = SUPER_ADMIN_DATA.singleDeviceLogin;
        
        await existingUser.save();
        
        console.log('✅ Super admin user updated successfully!');
        console.log(`📧 Email: ${existingUser.email}`);
        console.log(`👤 Name: ${existingUser.firstName} ${existingUser.lastName}`);
        console.log(`🔑 Role: ${existingUser.role}`);
        console.log(`📊 Status: ${existingUser.status}`);
      } else {
        console.log('❌ Operation cancelled. Existing user was not modified.');
      }
      
      return;
    }
    
    console.log('👤 Creating new super admin user...');
    
    // Create new super admin user using the registerUser function
    const superAdmin = await registerUser(
      SUPER_ADMIN_DATA.email,
      SUPER_ADMIN_DATA.password,
      SUPER_ADMIN_DATA.firstName,
      SUPER_ADMIN_DATA.lastName,
      SUPER_ADMIN_DATA.role,
      SUPER_ADMIN_DATA.status
    );
    
    // Update additional fields that aren't handled by registerUser
    superAdmin.department = SUPER_ADMIN_DATA.department;
    superAdmin.position = SUPER_ADMIN_DATA.position;
    superAdmin.dateOfJoining = SUPER_ADMIN_DATA.dateOfJoining;
    superAdmin.allowMultipleDevices = SUPER_ADMIN_DATA.allowMultipleDevices;
    superAdmin.trustedDevicesOnly = SUPER_ADMIN_DATA.trustedDevicesOnly;
    superAdmin.singleDeviceLogin = SUPER_ADMIN_DATA.singleDeviceLogin;
    
    await superAdmin.save();
    
    console.log('✅ Super admin user created successfully!');
    console.log('\n📋 User Details:');
    console.log(`📧 Email: ${superAdmin.email}`);
    console.log(`👤 Name: ${superAdmin.firstName} ${superAdmin.lastName}`);
    console.log(`🔑 Role: ${superAdmin.role}`);
    console.log(`📊 Status: ${superAdmin.status}`);
    console.log(`🏢 Department: ${superAdmin.department}`);
    console.log(`💼 Position: ${superAdmin.position}`);
    console.log(`📅 Created: ${superAdmin.createdAt}`);
    console.log(`🔐 Password: ${SUPER_ADMIN_DATA.password}`);
    
    console.log('\n🎉 Super admin setup complete!');
    console.log('🔗 You can now login to the system with these credentials.');
    
  } catch (error: any) {
    console.error('❌ Failed to create super admin user:', error.message);
    
    if (error.message.includes('already exists')) {
      console.error('💡 This error means the email address is already in use.');
    }
    
    throw error;
  }
}

/**
 * Main function
 */
async function main(): Promise<void> {
  console.log('🌱 Kawandama Hills Plantation Management System');
  console.log('🔧 Super Admin User Creation Script');
  console.log('=' .repeat(50));
  
  try {
    // Connect to database
    console.log('🔌 Connecting to database...');
    await connectToDatabase();
    console.log('✅ Connected to database successfully');
    
    // Create super admin
    await createSuperAdmin();
    
  } catch (error: any) {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  } finally {
    // Close database connection
    console.log('\n🔌 Closing database connection...');
    process.exit(0);
  }
}

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
  });
}
