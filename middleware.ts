
// // middleware.ts
// import { NextResponse } from 'next/server';
// import type { NextRequest } from 'next/server';
// import { getUserFromToken } from './lib/auth';

// export async function middleware(request: NextRequest) {
//   const tokenCookie = request.cookies.get('token');

//   if (!tokenCookie) {
//     return NextResponse.redirect(new URL('/auth', request.url));
//   }

//   const token = tokenCookie.value;
//   const user = getUserFromToken(token);

//   if (!user) {
//     return NextResponse.redirect(new URL('/auth', request.url));
//   }

//   const role = user.role;
//   const pathname = request.nextUrl.pathname;

//   const allowedPaths: Record<string, string[]> = {
//     admin: [
//       '/dashboard',
//       '/dashboard/allemployees',
//       '/dashboard/fulltimeemployees',
//       '/dashboard/attendanceemployees',
//       '/dashboard/attendance',
//       '/dashboard/monthly-attendance',
//       '/dashboard/alltasks',
//       '/dashboard/asignedtasks',
//       '/dashboard/completedtasks',
//       '/dashboard/canceledtasks',
//       '/dashboard/attendancepayments',
//       '/dashboard/taskpayments',
//       '/dashboard/bonuspayments',
//       '/dashboard/anualleave',
//       '/dashboard/maternityleave',
//       '/dashboard/educationleave',
//       '/dashboard/sickleave',
//       '/dashboard/funeralleave',
//       '/dashboard/allloans',
//       '/dashboard/loanrequest',
//       '/dashboard/appovedloans',
//       '/dashboard/declinedloans',
//       '/dashboard/inventory',
//       '/dashboard/warehouses',
//       '/dashboard/addAdmin',
//     ],
//     ceoadmin: [
//       '/dashboard',
//       '/dashboard/allemployees',
//       '/dashboard/fulltimeemployees',
//       '/dashboard/attendanceemployees',
//       '/dashboard/attendance',
//       '/dashboard/monthly-attendance',
//       '/dashboard/alltasks',
//       '/dashboard/asignedtasks',
//       '/dashboard/completedtasks',
//       '/dashboard/canceledtasks',
//       '/dashboard/attendancepayments',
//       '/dashboard/taskpayments',
//       '/dashboard/bonuspayments',
//       '/dashboard/anualleave',
//       '/dashboard/maternityleave',
//       '/dashboard/educationleave',
//       '/dashboard/sickleave',
//       '/dashboard/funeralleave',
//       '/dashboard/allloans',
//       '/dashboard/loanrequest',
//       '/dashboard/appovedloans',
//       '/dashboard/declinedloans',
//       '/dashboard/inventory',
//       '/dashboard/warehouses',
//       '/dashboard/addAdmin',
//     ],
//     accountant: [
//       '/dashboard/attendancepayments',
//       '/dashboard/taskpayments',
//       '/dashboard/bonuspayments',
//       '/dashboard/allloans',
//       '/dashboard/appovedloans',
//       '/dashboard/declinedloans',
//     ],
//     hrmanager: [
//       '/dashboard/allemployees',
//       '/dashboard/fulltimeemployees',
//       '/dashboard/attendanceemployees',
//       '/dashboard/anualleave',
//       '/dashboard/maternityleave',
//       '/dashboard/educationleave',
//       '/dashboard/sickleave',
//       '/dashboard/funeralleave',
//       '/dashboard/attendance',
//       '/dashboard/monthly-attendance',
//     ],
//     fieldsupervisor: [
//       '/dashboard/alltasks',
//       '/dashboard/asignedtasks',
//       '/dashboard/completedtasks',
//       '/dashboard/canceledtasks',
//     ],
//   };

//   const isAllowed = allowedPaths[role]?.some((path) => pathname.startsWith(path));

//   if (isAllowed) {
//     return NextResponse.next();
//   } else {
//     return NextResponse.redirect(new URL('/403', request.url));
//   }
// }

// export const config = {
//   matcher: ['/dashboard/:path*'],
// };


// middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getUserFromToken } from './lib/auth';

export async function middleware(request: NextRequest) {
  const tokenCookie = request.cookies.get('token');

  if (!tokenCookie) {
    return NextResponse.redirect(new URL('/auth', request.url));
  }

  const token = tokenCookie.value;
  const user = getUserFromToken(token);

  if (!user) {
    return NextResponse.redirect(new URL('/auth', request.url));
  }

  const pathname = request.nextUrl.pathname;

  // Allow access to all dashboard routes if authenticated
  if (pathname.startsWith('/dashboard')) {
    return NextResponse.next();
  }

  // Redirect to 403 page for other routes
  return NextResponse.redirect(new URL('/403', request.url));
}

export const config = {
  matcher: ['/dashboard/:path*'],
};
