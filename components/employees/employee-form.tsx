
// "use client"

// import * as React from "react"
// import { use<PERSON>orm, FormProvider, useFormContext } from "react-hook-form"
// import { zodResolver } from "@hookform/resolvers/zod"
// import * as z from "zod"
// import { Button } from "@/components/ui/button"
// import { Input } from "@/components/ui/input"
// import { Overlay } from "@/components/ui/overlay"
// import { Employee } from "@/app/types/employee"

// // Schema for form validation
// const employeeSchema = z.object({
//     firstName: z.string().nonempty("First name is required"),
//     lastName: z.string().nonempty("Last name is required"),
//     jobTitle: z.string().nonempty("Job title is required"),
//     salary: z.string().nonempty("salary is required"),
//     hireDate: z.string().nonempty("Hire date is required"),
//     department: z.string().nonempty("Department is required"),
//     email: z.string().email("Invalid email address"),
//     phone: z.string().nonempty("Phone number is required"),
//     gender: z.enum(['male', 'female', 'other']),
//     dateOfBirth: z.string().nonempty("Date of birth is required"),
//     homeOrigin: z.string().nonempty("Home origin is required"),
//     residence: z.string().nonempty("Residence is required"),
//     maritalStatus: z.enum(['single', 'married', 'divorced', 'widowed']),
//     nextOfKin: z.string().nonempty("Next of kin is required"),
//     nextOfKinContact: z.string().nonempty("Next of kin contact is required"),
//     status: z.enum(['active', 'inactive', 'terminated']),
// })

// type EmployeeFormValues = z.infer<typeof employeeSchema>

// interface EmployeeFormProps {
//     departments: { _id: string; name: string }[]; // Departments array passed as props
//     onSubmit: (data: Omit<Employee, '_id'>) => void
// }

// const Step1And3: React.FC = () => {
//     const methods = useFormContext<EmployeeFormValues>()
//     const { register, formState: { errors } } = methods

//     return (
//         <div>
//             <label htmlFor="firstName" className="block text-sm font-medium">First Name</label>
//             <Input id="firstName" {...register("firstName")} />
//             {errors.firstName && <p className="text-red-600">{errors.firstName.message}</p>}

//             <label htmlFor="lastName" className="block text-sm font-medium">Last Name</label>
//             <Input id="lastName" {...register("lastName")} />
//             {errors.lastName && <p className="text-red-600">{errors.lastName.message}</p>}

//             <label htmlFor="email" className="block text-sm font-medium">Email</label>
//             <Input id="email" type="email" {...register("email")} />
//             {errors.email && <p className="text-red-600">{errors.email.message}</p>}

//             <label htmlFor="phone" className="block text-sm font-medium">Phone</label>
//             <Input id="phone" {...register("phone")} />
//             {errors.phone && <p className="text-red-600">{errors.phone.message}</p>}

//             <label htmlFor="gender" className="block text-sm font-medium">Gender</label>
//             <select id="gender" {...register('gender')}>
//                 <option value="male">Male</option>
//                 <option value="female">Female</option>
//                 <option value="other">Other</option>
//             </select>
//             {errors.gender && <p className="text-red-600">{errors.gender.message}</p>}

//             <label htmlFor="dateOfBirth" className="block text-sm font-medium">Date of Birth</label>
//             <Input id="dateOfBirth" type="date" {...register("dateOfBirth")} />
//             {errors.dateOfBirth && <p className="text-red-600">{errors.dateOfBirth.message}</p>}

//             <label htmlFor="homeOrigin" className="block text-sm font-medium">Home Origin</label>
//             <Input id="homeOrigin" {...register("homeOrigin")} />
//             {errors.homeOrigin && <p className="text-red-600">{errors.homeOrigin.message}</p>}

//             <label htmlFor="residence" className="block text-sm font-medium">Residence</label>
//             <Input id="residence" {...register("residence")} />
//             {errors.residence && <p className="text-red-600">{errors.residence.message}</p>}

//             <label htmlFor="maritalStatus" className="block text-sm font-medium">Marital Status</label>
//             <select id="maritalStatus" {...register('maritalStatus')}>
//                 <option value="single">Single</option>
//                 <option value="married">Married</option>
//                 <option value="divorced">Divorced</option>
//                 <option value="widowed">Widowed</option>
//             </select>
//             {errors.maritalStatus && <p className="text-red-600">{errors.maritalStatus.message}</p>}
//         </div>
//     )
// }

// interface Step2Props {
//     departments: { _id: string; name: string }[]
// }

// const Step2: React.FC<Step2Props> = ({ departments }) => {
//     const methods = useFormContext<EmployeeFormValues>()
//     const { register, formState: { errors } } = methods

//     return (
//         <div>
//             <label htmlFor="nextOfKin" className="block text-sm font-medium">Next of Kin</label>
//             <Input id="nextOfKin" {...register("nextOfKin")} />
//             {errors.nextOfKin && <p className="text-red-600">{errors.nextOfKin.message}</p>}

//             <label htmlFor="nextOfKinContact" className="block text-sm font-medium">Next of Kin Contact</label>
//             <Input id="nextOfKinContact" {...register("nextOfKinContact")} />
//             {errors.nextOfKinContact && <p className="text-red-600">{errors.nextOfKinContact.message}</p>}
//             <label htmlFor="jobTitle" className="block text-sm font-medium">Job Title</label>
//             <Input id="jobTitle" {...register("jobTitle")} />
//             {errors.jobTitle && <p className="text-red-600">{errors.jobTitle.message}</p>}

//             <label htmlFor="salary" className="block text-sm font-medium">Salary</label>
//             <Input id="salary" type="text" {...register("salary")} />
//             {errors.salary && <p className="text-red-600">{errors.salary.message}</p>}

//             <label htmlFor="hireDate" className="block text-sm font-medium">Hire Date</label>
//             <Input id="hireDate" type="date" {...register("hireDate")} />
//             {errors.hireDate && <p className="text-red-600">{errors.hireDate.message}</p>}

//             <label htmlFor="department" className="block text-sm font-medium">Department</label>
//             <select id="department" {...register('department')}>
//                 { /* Iterate over departments and render options */}
//                 {departments.map(dep => (
//                     <option key={dep._id} value={dep._id}>{dep.name}</option>
//                 ))}
//             </select>
//             {errors.department && <p className="text-red-600">{errors.department.message}</p>}

//             <label htmlFor="status" className="block text-sm font-medium">Status</label>
//             <select id="status" {...register('status')}>
//                 <option value="active">Active</option>
//                 <option value="inactive">Inactive</option>
//                 <option value="terminated">Terminated</option>
//             </select>
//             {errors.status && <p className="text-red-600">{errors.status.message}</p>}
//         </div>
//     )
// }

// export function EmployeeForm({ departments, onSubmit }: EmployeeFormProps) {
//     const methods = useForm<EmployeeFormValues>({
//         resolver: zodResolver(employeeSchema),
//         defaultValues: {
//             firstName: "",
//             lastName: "",
//             jobTitle: "",
//             salary: "",
//             hireDate: "",
//             department: "",
//             email: "",
//             phone: "",
//             gender: "male",
//             dateOfBirth: "",
//             homeOrigin: "",
//             residence: "",
//             maritalStatus: "single",
//             nextOfKin: "",
//             nextOfKinContact: "",
//             status: "active"
//         }
//     })
//     const { handleSubmit, trigger, formState: { isSubmitting } } = methods
//     const [step, setStep] = React.useState(1)
//     const [isOpen, setIsOpen] = React.useState(false)

//     const nextStep = async () => {
//         const valid = await trigger(step === 1 ? ["firstName", "lastName", "email", "phone", "gender", "dateOfBirth", "homeOrigin", "residence", "maritalStatus"] : ["nextOfKin", "nextOfKinContact", "jobTitle", "salary", "hireDate", "department", "status"])
//         if (valid) setStep(step => step + 1)
//     }
//     const prevStep = () => setStep(step => step - 1)

//     const handleFormSubmit = (data: EmployeeFormValues) => {
//         if (step === 2) {
//             onSubmit(data as Omit<Employee, '_id'>)
//             setIsOpen(false) // Close the overlay after submitting
//         } else {
//             nextStep()
//         }
//     }

//     return (
//         <>
//             <Button onClick={() => setIsOpen(true)} className="ml-4">
//                 Add Employee
//             </Button>
//             <Overlay isOpen={isOpen} onClose={() => setIsOpen(false)}>
//                 <FormProvider {...methods}>
//                     <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
//                         {step === 1 && <Step1And3 />}
//                         {step === 2 && <Step2 departments={departments} />}
//                         <div className="flex justify-between">
//                             {step > 1 && (
//                                 <Button type="button" onClick={prevStep}>
//                                     Previous
//                                 </Button>
//                             )}
//                             {step < 2 && (
//                                 <Button type="button" onClick={nextStep} disabled={isSubmitting}>
//                                     Next
//                                 </Button>
//                             )}
//                             {step === 2 && (
//                                 <Button type="submit" disabled={isSubmitting}>
//                                     Submit
//                                 </Button>
//                             )}
//                         </div>
//                     </form>
//                 </FormProvider>
//             </Overlay>
//         </>
//     )
// }





// app/components/employees/employee-form.tsx
"use client"

import * as React from "react"
import { useForm, FormProvider, useFormContext } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Overlay } from "@/components/ui/overlay"
import { Employee } from "@/app/types/employee"

// Schema for form validation
const employeeSchema = z.object({
    firstName: z.string().nonempty("First name is required"),
    lastName: z.string().nonempty("Last name is required"),
    jobTitle: z.string().nonempty("Job title is required"),
    salary: z.string().nonempty("Salary is required"),
    hireDate: z.string().nonempty("Hire date is required"),
    department: z.string().nonempty("Department is required"),
    email: z.string().email("Invalid email address"),
    phone: z.string().nonempty("Phone number is required"),
    gender: z.enum(['male', 'female', 'other']),
    dateOfBirth: z.string().nonempty("Date of birth is required"),
    homeOrigin: z.string().nonempty("Home origin is required"),
    residence: z.string().nonempty("Residence is required"),
    maritalStatus: z.enum(['single', 'married', 'divorced', 'widowed']),
    nextOfKin: z.string().nonempty("Next of kin is required"),
    nextOfKinContact: z.string().nonempty("Next of kin contact is required"),
    status: z.enum(['active', 'inactive', 'terminated']),
})

type EmployeeFormValues = z.infer<typeof employeeSchema>

interface EmployeeFormProps {
    departments: { _id: string; name: string }[];
    onSubmit: (data: Omit<Employee, '_id'>) => void;
    isOpen: boolean;
    onClose: () => void;
}

const Step1And3: React.FC = () => {
    const methods = useFormContext<EmployeeFormValues>()
    const { register, formState: { errors } } = methods

    return (
        <div>
            <label htmlFor="firstName" className="block text-sm font-medium">First Name</label>
            <Input id="firstName" {...register("firstName")} />
            {errors.firstName && <p className="text-red-600">{errors.firstName.message}</p>}

            <label htmlFor="lastName" className="block text-sm font-medium">Last Name</label>
            <Input id="lastName" {...register("lastName")} />
            {errors.lastName && <p className="text-red-600">{errors.lastName.message}</p>}

            <label htmlFor="email" className="block text-sm font-medium">Email</label>
            <Input id="email" type="email" {...register("email")} />
            {errors.email && <p className="text-red-600">{errors.email.message}</p>}

            <label htmlFor="phone" className="block text-sm font-medium">Phone</label>
            <Input id="phone" {...register("phone")} />
            {errors.phone && <p className="text-red-600">{errors.phone.message}</p>}

            <label htmlFor="gender" className="block text-sm font-medium">Gender</label>
            <select id="gender" {...register('gender')}>
                <option value="male">Male</option>
                <option value="female">Female</option>
                <option value="other">Other</option>
            </select>
            {errors.gender && <p className="text-red-600">{errors.gender.message}</p>}

            <label htmlFor="dateOfBirth" className="block text-sm font-medium">Date of Birth</label>
            <Input id="dateOfBirth" type="date" {...register("dateOfBirth")} />
            {errors.dateOfBirth && <p className="text-red-600">{errors.dateOfBirth.message}</p>}

            <label htmlFor="homeOrigin" className="block text-sm font-medium">Home Origin</label>
            <Input id="homeOrigin" {...register("homeOrigin")} />
            {errors.homeOrigin && <p className="text-red-600">{errors.homeOrigin.message}</p>}

            <label htmlFor="residence" className="block text-sm font-medium">Residence</label>
            <Input id="residence" {...register("residence")} />
            {errors.residence && <p className="text-red-600">{errors.residence.message}</p>}

            <label htmlFor="maritalStatus" className="block text-sm font-medium">Marital Status</label>
            <select id="maritalStatus" {...register('maritalStatus')}>
                <option value="single">Single</option>
                <option value="married">Married</option>
                <option value="divorced">Divorced</option>
                <option value="widowed">Widowed</option>
            </select>
            {errors.maritalStatus && <p className="text-red-600">{errors.maritalStatus.message}</p>}
        </div>
    )
}

interface Step2Props {
    departments: { _id: string; name: string }[]
}

const Step2: React.FC<Step2Props> = ({ departments }) => {
    const methods = useFormContext<EmployeeFormValues>()
    const { register, formState: { errors } } = methods

    return (
        <div>
            <label htmlFor="nextOfKin" className="block text-sm font-medium">Next of Kin</label>
            <Input id="nextOfKin" {...register("nextOfKin")} />
            {errors.nextOfKin && <p className="text-red-600">{errors.nextOfKin.message}</p>}

            <label htmlFor="nextOfKinContact" className="block text-sm font-medium">Next of Kin Contact</label>
            <Input id="nextOfKinContact" {...register("nextOfKinContact")} />
            {errors.nextOfKinContact && <p className="text-red-600">{errors.nextOfKinContact.message}</p>}
            <label htmlFor="jobTitle" className="block text-sm font-medium">Job Title</label>
            <Input id="jobTitle" {...register("jobTitle")} />
            {errors.jobTitle && <p className="text-red-600">{errors.jobTitle.message}</p>}

            <label htmlFor="salary" className="block text-sm font-medium">Salary</label>
            <Input id="salary" type="text" {...register("salary")} />
            {errors.salary && <p className="text-red-600">{errors.salary.message}</p>}

            <label htmlFor="hireDate" className="block text-sm font-medium">Hire Date</label>
            <Input id="hireDate" type="date" {...register("hireDate")} />
            {errors.hireDate && <p className="text-red-600">{errors.hireDate.message}</p>}

            <label htmlFor="department" className="block text-sm font-medium">Department</label>
            <select id="department" {...register('department')}>
                {departments.map(dep => (
                    <option key={dep._id} value={dep._id}>{dep.name}</option>
                ))}
            </select>
            {errors.department && <p className="text-red-600">{errors.department.message}</p>}

            <label htmlFor="status" className="block text-sm font-medium">Status</label>
            <select id="status" {...register('status')}>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="terminated">Terminated</option>
            </select>
            {errors.status && <p className="text-red-600">{errors.status.message}</p>}
        </div>
    )
}

export const EmployeeForm: React.FC<EmployeeFormProps> = ({ departments, onSubmit, isOpen, onClose }) => {
    const methods = useForm<EmployeeFormValues>({
        resolver: zodResolver(employeeSchema),
        defaultValues: {
            firstName: "",
            lastName: "",
            jobTitle: "",
            salary: "",
            hireDate: "",
            department: "",
            email: "",
            phone: "",
            gender: "male",
            dateOfBirth: "",
            homeOrigin: "",
            residence: "",
            maritalStatus: "single",
            nextOfKin: "",
            nextOfKinContact: "",
            status: "active"
        }
    })
    const { handleSubmit, trigger, formState: { isSubmitting } } = methods
    const [step, setStep] = React.useState(1)

    const nextStep = async () => {
        const valid = await trigger(step === 1 ? ["firstName", "lastName", "email", "phone", "gender", "dateOfBirth", "homeOrigin", "residence", "maritalStatus"] : ["nextOfKin", "nextOfKinContact", "jobTitle", "salary", "hireDate", "department", "status"])
        if (valid) setStep(step => step + 1)
    }
    const prevStep = () => setStep(step => step - 1)

    const handleFormSubmit = (data: EmployeeFormValues) => {
        if (step === 2) {
            onSubmit(data as Omit<Employee, '_id'>)
            onClose() // Close the overlay after submitting
        } else {
            nextStep()
        }
    }

    return (
        <Overlay isOpen={isOpen} onClose={onClose}>
            <FormProvider {...methods}>
                <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4 p-4 bg-white rounded shadow-md">
                    {step === 1 && <Step1And3 />}
                    {step === 2 && <Step2 departments={departments} />}
                    <div className="flex justify-between">
                        {step > 1 && (
                            <Button type="button" onClick={prevStep}>
                                Previous
                            </Button>
                        )}
                        {step < 2 && (
                            <Button type="button" onClick={nextStep} disabled={isSubmitting}>
                                Next
                            </Button>
                        )}
                        {step === 2 && (
                            <Button type="submit" disabled={isSubmitting}>
                                Submit
                            </Button>
                        )}
                    </div>
                </form>
            </FormProvider>
        </Overlay>
    )
}


