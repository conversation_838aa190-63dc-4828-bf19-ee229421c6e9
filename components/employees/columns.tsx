// components employees/columns.tsx
import { Employee } from "@/app/types/employee";
import { ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";

export const columns: ColumnDef<Employee>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "firstName",
    header: () => <div className="text-left">First Name</div>,
    cell: ({ row }) => {
      const firstName = row.getValue("firstName") as string;
      return <div className="text-left font-medium">{firstName || "N/A"}</div>;
    },
  },
  {
    accessorKey: "lastName",
    header: () => <div className="text-left">Last Name</div>,
    cell: ({ row }) => {
      const lastName = row.getValue("lastName") as string;
      return <div className="text-left font-medium">{lastName || "N/A"}</div>;
    },
  },
  {
    accessorKey: "phone",
    header: () => <div className="text-left">Phone</div>,
    cell: ({ row }) => {
      const phone = row.getValue("phone") as string;
      return <div className="text-left font-medium">{phone || "N/A"}</div>;
    },
  },
  {
    accessorKey: "gender",
    header: () => <div className="text-left">Gender</div>,
    cell: ({ row }) => {
      const gender = row.getValue("gender") as string;
      return <div className="text-left font-medium">{gender || "N/A"}</div>;
    },
  },
  {
    accessorKey: "status",
    header: () => <div className="text-left">Status</div>,
    cell: ({ row }) => {
      const status = row.getValue("status") as Employee["status"];
      if (!status) return <div className="text-left">N/A</div>;

      let statusClass = "text-gray-600";

      if (status === "active") statusClass = "text-green-600";
      else if (status === "inactive") statusClass = "text-yellow-600";
      else if (status === "terminated") statusClass = "text-red-600";

      return <div className={`font-medium ${statusClass}`}>{status}</div>;
    },
  },
  // {
  //   id: "actions",
  //   header: () => <div className="text-left">Actions</div>,
  //   cell: ({ row }) => {
  //     const employee = row.original;

  //     return (
  //       <DropdownMenu>
  //         <DropdownMenuTrigger asChild>
  //           <Button variant="ghost" className="h-8 w-8 p-0">
  //             <span className="sr-only">Open menu</span>
  //             <MoreHorizontal className="h-4 w-4" />
  //           </Button>
  //         </DropdownMenuTrigger>
  //         <DropdownMenuContent align="end">
  //           <DropdownMenuLabel>Actions</DropdownMenuLabel>
  //           <DropdownMenuItem
  //             onClick={() => navigator.clipboard.writeText(employee._id)}
  //           >
  //             Copy employee ID
  //           </DropdownMenuItem>
  //           <DropdownMenuSeparator />
  //           <DropdownMenuItem>View employee</DropdownMenuItem>
  //           <DropdownMenuItem>View employee details</DropdownMenuItem>
  //         </DropdownMenuContent>
  //       </DropdownMenu>
  //     );
  //   },
  // },
];
