// // app/components/leave/leave-request-form.tsx

// import React, { useState } from 'react'
// import { Leave } from '@/app/types/leave'
// import { Button } from '@/components/ui/button'
// import { Input } from '@/components/ui/input'
// import { Textarea } from '@/components/ui/textarea'
// import { Select, SelectItem } from '@/components/ui/select'
// import { Modal, ModalHeader, ModalBody, ModalFooter } from '@/components/ui/modal'

// const LeaveRequestForm: React.FC = () => {
//   const [formData, setFormData] = useState<Omit<Leave, 'leaveId' | 'status'>>({
//     employeeName: '',
//     leaveType: 'annual', // Default value
//     startDate: '',
//     endDate: '',
//     reason: ''
//   })

//   const [isModalOpen, setIsModalOpen] = useState(false)

//   const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
//     const { name, value } = e.target
//     setFormData({ ...formData, [name]: value })
//   }

//   const handleSelectChange = (value: Leave['leaveType']) => {
//     setFormData({ ...formData, leaveType: value })
//   }

//   const handleSubmit = (e: React.FormEvent) => {
//     e.preventDefault()
//     // Handle form submission logic here
//     const leaveRequest: Leave = {
//       ...formData,
//       leaveId: new Date().toISOString(), // Generate a unique ID for the leave request
//       status: 'pending' // Default status
//     }
//     console.log('Leave Request Submitted:', leaveRequest)
//     setIsModalOpen(false)
//   }

//   return (
//     <>
//       <Button onClick={() => setIsModalOpen(true)} className="absolute top-0 right-0 mt-4 mr-4">
//         Create Leave Request
//       </Button>
//       <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)}>
//         <form onSubmit={handleSubmit} className="p-4">
//           <ModalHeader>
//             <h2 className="text-xl font-semibold">Leave Request Form</h2>
//           </ModalHeader>
//           <ModalBody>
//             <div className="space-y-4">
//               <div>
//                 <label htmlFor="employeeName" className="block text-sm font-medium">Employee Name</label>
//                 <Input
//                   id="employeeName"
//                   name="employeeName"
//                   value={formData.employeeName}
//                   onChange={handleInputChange}
//                   required
//                 />
//               </div>
//               <div>
//                 <label htmlFor="leaveType" className="block text-sm font-medium">Leave Type</label>
//                 <Select id="leaveType" value={formData.leaveType} onChange={handleSelectChange} required>
//                   <SelectItem value="annual">Annual Leave</SelectItem>
//                   <SelectItem value="maternity">Maternity Leave</SelectItem>
//                   <SelectItem value="educational">Educational Leave</SelectItem>
//                   <SelectItem value="sick">Sick Leave</SelectItem>
//                   <SelectItem value="funeral">Funeral Leave</SelectItem>
//                 </Select>
//               </div>
//               <div>
//                 <label htmlFor="startDate" className="block text-sm font-medium">Start Date</label>
//                 <Input
//                   type="date"
//                   id="startDate"
//                   name="startDate"
//                   value={formData.startDate}
//                   onChange={handleInputChange}
//                   required
//                 />
//               </div>
//               <div>
//                 <label htmlFor="endDate" className="block text-sm font-medium">End Date</label>
//                 <Input
//                   type="date"
//                   id="endDate"
//                   name="endDate"
//                   value={formData.endDate}
//                   onChange={handleInputChange}
//                   required
//                 />
//               </div>
//               <div>
//                 <label htmlFor="reason" className="block text-sm font-medium">Reason</label>
//                 <Textarea
//                   id="reason"
//                   name="reason"
//                   value={formData.reason}
//                   onChange={handleInputChange}
//                   required
//                 />
//               </div>
//             </div>
//           </ModalBody>
//           <ModalFooter>
//             <Button type="button" variant="outline" onClick={() => setIsModalOpen(false)}>
//               Cancel
//             </Button>
//             <Button type="submit" className="ml-2">
//               Submit
//             </Button>
//           </ModalFooter>
//         </form>
//       </Modal>
//     </>
//   )
// }

// export default LeaveRequestForm
