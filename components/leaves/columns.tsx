// // app/components/leaves/columns.tsx

// import { Leave } from "@/app/types/leave"
// import { ColumnDef } from "@tanstack/react-table"
// import { MoreHorizontal } from "lucide-react"
// import { Button } from "@/components/ui/button"
// import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
// import { Checkbox } from "@/components/ui/checkbox"

// export const columns: ColumnDef<Leave>[] = [
//   {
//     id: "select",
//     header: ({ table }) => (
//       <Checkbox
//         checked={
//           table.getIsAllPageRowsSelected() ||
//           (table.getIsSomePageRowsSelected() && "indeterminate")
//         }
//         onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
//         aria-label="Select all"
//       />
//     ),
//     cell: ({ row }) => (
//       <Checkbox
//         checked={row.getIsSelected()}
//         onCheckedChange={(value) => row.toggleSelected(!!value)}
//         aria-label="Select row"
//       />
//     ),
//     enableSorting: false,
//     enableHiding: false,
//   },
//   {
//     accessorKey: "employeeName",
//     header: () => <div className="text-left">Employee Name</div>,
//     cell: ({ row }) => <div className="text-left font-medium">{row.getValue("employeeName")}</div>,
//   },
//   {
//     accessorKey: "startDate",
//     header: () => <div className="text-left">Start Date</div>,
//     cell: ({ row }) => <div className="text-left">{row.getValue("startDate")}</div>,
//   },
//   {
//     accessorKey: "endDate",
//     header: () => <div className="text-left">End Date</div>,
//     cell: ({ row }) => <div className="text-left">{row.getValue("endDate")}</div>,
//   },
//   {
//     accessorKey: "reason",
//     header: () => <div className="text-left">Reason</div>,
//     cell: ({ row }) => <div className="text-left">{row.getValue("reason")}</div>,
//   },
//   {
//     accessorKey: "status",
//     header: () => <div className="text-left">Status</div>,
//     cell: ({ row }) => {
//       const status = row.getValue("status") as Leave["status"]
//       const statusClass = {
//         "approved": "text-green-600",
//         "pending": "text-yellow-600",
//         "rejected": "text-red-600",
//       }[status]
//       return <div className={`text-left ${statusClass}`}>{status}</div>
//     },
//   },
//   {
//     id: "actions",
//     header: () => <div className="text-left">Actions</div>,
//     cell: ({ row }) => {
//       const leave = row.original

//       return (
//         <DropdownMenu>
//           <DropdownMenuTrigger asChild>
//             <Button variant="ghost" className="h-8 w-8 p-0">
//               <span className="sr-only">Open menu</span>
//               <MoreHorizontal className="h-4 w-4" />
//             </Button>
//           </DropdownMenuTrigger>
//           <DropdownMenuContent align="end">
//             <DropdownMenuLabel>Actions</DropdownMenuLabel>
//             <DropdownMenuItem onClick={() => alert(`Copy leave ID: ${leave.leaveId}`)}>
//               Copy leave ID
//             </DropdownMenuItem>
//             <DropdownMenuSeparator />
//             <DropdownMenuItem>View details</DropdownMenuItem>
//             <DropdownMenuItem>Edit</DropdownMenuItem>
//             <DropdownMenuItem>Delete</DropdownMenuItem>
//           </DropdownMenuContent>
//         </DropdownMenu>
//       )
//     },
//   },
// ]


// app/components/leaves/columns.tsx

"use client"

import { Leave } from "@/app/types/leave"
import { ColumnDef } from "@tanstack/react-table"
import { MoreHorizontal, ArrowUpDown } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export const columns: ColumnDef<Leave>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "employeeName",
    header: () => <div className="text-left">Employee Name</div>,
    cell: ({ row }) => <div className="text-left font-medium">{row.getValue("employeeName")}</div>,
  },
  {
    accessorKey: "leaveType",
    header: () => <div className="text-left">Leave Type</div>,
    cell: ({ row }) => <div className="text-left">{row.getValue("leaveType")}</div>,
  },
  {
    accessorKey: "startDate",
    header: () => <div className="text-left">Start Date</div>,
    cell: ({ row }) => <div className="text-left">{row.getValue("startDate")}</div>,
  },
  {
    accessorKey: "endDate",
    header: () => <div className="text-left">End Date</div>,
    cell: ({ row }) => <div className="text-left">{row.getValue("endDate")}</div>,
  },
  {
    accessorKey: "reason",
    header: () => <div className="text-left">Reason</div>,
    cell: ({ row }) => <div className="text-left">{row.getValue("reason")}</div>,
  },
  {
    accessorKey: "status",
    header: () => <div className="text-left">Status</div>,
    cell: ({ row }) => {
      const status = row.getValue("status") as Leave["status"]
      const statusClass = {
        approved: "text-green-600",
        pending: "text-yellow-600",
        rejected: "text-red-600",
      }[status]
      return <div className={`text-left ${statusClass}`}>{status}</div>
    },
  },
  {
    id: "actions",
    header: () => <div className="text-left">Actions</div>,
    cell: ({ row }) => {
      const leave = row.original

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(leave.leaveId)}
            >
              Copy leave ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>View leave details</DropdownMenuItem>
            <DropdownMenuItem>Edit leave</DropdownMenuItem>
            <DropdownMenuItem>Delete leave</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
