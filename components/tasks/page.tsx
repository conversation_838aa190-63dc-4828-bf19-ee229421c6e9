

import React from 'react'

const AllTasksPage = () => {
  return (
    <div>AllTasksPage</div>
  )
}

export default AllTasksPage

// import { DataTable } from "@/components/tasks/data-table"; 
// import { columns } from "@/components/tasks/columns"; 
// import { Task } from "@/app/types/task";


// const tasks: Task[] = [
//   {
//     id: "1",
//     title: "Prepare nursery beds",
//     description: "Prepare nursery beds for planting new seedlings.",
//     status: "todo",
//     assignedTo: "John <PERSON>",
//     dueDate: new Date("2024-08-01"),
//   },
//   {
//     id: "2",
//     title: "Plant new seedlings",
//     description: "Plant new seedlings in the nursery beds.",
//     status: "todo",
//     assignedTo: "<PERSON>",
//     dueDate: new Date("2024-08-05"),
//   },
//   {
//     id: "3",
//     title: "Harvest leaves",
//     description: "Harvest leaves for extracting citron oil.",
//     status: "todo",
//     assignedTo: "<PERSON>",
//     dueDate: new Date("2024-08-10"),
//   },
//   {
//     id: "4",
//     title: "Process citron oil",
//     description: "Process harvested leaves to extract citron oil.",
//     status: "todo",
//     assignedTo: "Michael Johnson",
//     dueDate: new Date("2024-08-15"),
//   },
//   {
//     id: "5",
//     title: "Produce charcoal",
//     description: "Start charcoal production process.",
//     status: "todo",
//     assignedTo: "Sarah Wilson",
//     dueDate: new Date("2024-08-20"),
//   },
//   {
//     id: "6",
//     title: "Pack citron oil",
//     description: "Package citron oil extracted from harvested leaves.",
//     status: "todo",
//     assignedTo: "David Lee",
//     dueDate: new Date("2024-08-25"),
//   },
//   {
//     id: "7",
//     title: "Quality check charcoal",
//     description: "Perform quality checks on produced charcoal.",
//     status: "todo",
//     assignedTo: "Olivia Martinez",
//     dueDate: new Date("2024-08-30"),
//   },
//   {
//     id: "8",
//     title: "Distribute nursery seedlings",
//     description: "Distribute nursery seedlings to designated areas.",
//     status: "todo",
//     assignedTo: "Daniel Garcia",
//     dueDate: new Date("2024-09-01"),
//   },
//   {
//     id: "9",
//     title: "Market citron oil",
//     description: "Develop marketing strategy for citron oil products.", 
//     status: "todo",
//     assignedTo: "Sophia Rodriguez",
//     dueDate: new Date("2024-09-05"),
//   },
//   {
//     id: "10",
//     title: "Sales of charcoal",
//     description: "Plan sales and distribution of charcoal products.",
//     status: "todo",
//     assignedTo: "James Taylor", 
//     dueDate: new Date("2024-09-10"),
//   },
// ];

// function TaskManagement() {
//   return <DataTable columns={columns} data={tasks} />;
// }

// export default TaskManagement;




