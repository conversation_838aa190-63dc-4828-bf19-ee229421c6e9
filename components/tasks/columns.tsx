

// app/components/tasks/columns.tsx

"use client"

import { Task } from "@/app/types/task"
import { ColumnDef } from "@tanstack/react-table"
import { MoreHorizontal, ArrowUpDown } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export const columns: ColumnDef<Task>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "title",
    header: () => <div className="text-left">Title</div>,
    cell: ({ row }) => <div className="text-left font-medium">{row.getValue("title")}</div>,
  },
  {
    accessorKey: "description",
    header: () => <div className="text-left">Description</div>,
    cell: ({ row }) => <div className="text-left">{row.getValue("description")}</div>,
  },
  {
    accessorKey: "dueDate",
    header: () => <div className="text-left">Due Date</div>,
    cell: ({ row }) => <div className="text-left">{row.getValue("dueDate")}</div>,
  },
  {
    accessorKey: "priority",
    header: () => <div className="text-left">Priority</div>,
    cell: ({ row }) => {
      const priority = row.getValue("priority") as Task["priority"]
      const priorityClass = {
        high: "text-red-600",
        medium: "text-yellow-600",
        low: "text-green-600",
      }[priority]
      return <div className={`text-left ${priorityClass}`}>{priority}</div>
    },
  },
  {
    accessorKey: "status",
    header: () => <div className="text-left">Status</div>,
    cell: ({ row }) => {
      const status = row.getValue("status") as Task["status"]
      const statusClass = {
        "not started": "text-gray-600",
        "in progress": "text-yellow-600",
        "completed": "text-green-600",
        "canceled": "text-red-600",
      }[status]
      return <div className={`text-left ${statusClass}`}>{status}</div>
    },
  },
  {
    accessorKey: "assignee",
    header: () => <div className="text-left">Assignee</div>,
    cell: ({ row }) => <div className="text-left">{row.getValue("assignee")}</div>,
  },
  {
    id: "actions",
    header: () => <div className="text-left">Actions</div>,
    cell: ({ row }) => {
      const task = row.original

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(task.id)}
            >
              Copy task ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>View task details</DropdownMenuItem>
            <DropdownMenuItem>Edit task</DropdownMenuItem>
            <DropdownMenuItem>Delete task</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
