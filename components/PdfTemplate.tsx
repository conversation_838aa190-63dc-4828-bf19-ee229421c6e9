import { Payment } from '@/app/types/payment';
import React from 'react';

interface PdfTemplateProps {
    // payment: Payment; 
    payment: any;
}

const PdfTemplate: React.FC<PdfTemplateProps> = ({ payment }) => {
    return (
        <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
            <h1>Payment Report</h1>
            <p><strong>Employee Name:</strong> {payment.employee.firstName} {payment.employee.lastName}</p>
            <p><strong>Phone Number:</strong> {payment.employee.phone_number}</p>
            <p><strong>Amount:</strong> {payment.amount}</p>
            <p><strong>Tax:</strong> {payment.tax}</p>
            <p><strong>Loan Deduction:</strong> {payment.loan_deduction}</p>
            <p><strong>Insurance:</strong> {payment.insurance}</p>
            <p><strong>Final Amount:</strong> {payment.final_amount}</p>
            <p><strong>Status:</strong> {payment.status}</p>
        </div>
    );
};

export default PdfTemplate;
