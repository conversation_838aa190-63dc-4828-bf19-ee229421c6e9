// app/components/monthlyAttendance/columns.tsx

"use client"

import { MonthlyAttendance } from "@/app/types/monthlyAttendance"
import { ColumnDef } from "@tanstack/react-table"
import { MoreHorizontal } from "lucide-react"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export const columns: ColumnDef<MonthlyAttendance>[] = [
  {
    accessorKey: "employeeId",
    header: "Employee ID",
    cell: ({ row }) => {
      const employeeId = row.getValue("employeeId") as string
      return <div className="font-medium">{employeeId}</div>
    },
  },
  {
    accessorKey: "firstname",
    header: "First Name",
    cell: ({ row }) => {
      const firstname = row.getValue("firstname") as string
      return <div className="font-medium">{firstname}</div>
    },
  },
  {
    accessorKey: "lastname",
    header: "Last Name",
    cell: ({ row }) => {
      const lastname = row.getValue("lastname") as string
      return <div className="font-medium">{lastname}</div>
    },
  },
  {
    accessorKey: "month",
    header: "Month",
    cell: ({ row }) => {
      const month = row.getValue("month") as string
      return <div className="font-medium">{month}</div>
    },
  },
  {
    accessorKey: "presentDays",
    header: "Present Days",
    cell: ({ row }) => {
      const presentDays = row.getValue("presentDays") as number
      return <div className="font-medium text-green-600">{presentDays}</div>
    },
  },
  {
    accessorKey: "absentDays",
    header: "Absent Days",
    cell: ({ row }) => {
      const absentDays = row.getValue("absentDays") as number
      return <div className="font-medium text-red-600">{absentDays}</div>
    },
  },
  {
    accessorKey: "lateDays",
    header: "Late Days",
    cell: ({ row }) => {
      const lateDays = row.getValue("lateDays") as number
      return <div className="font-medium text-yellow-600">{lateDays}</div>
    },
  },
  {
    accessorKey: "excusedDays",
    header: "Excused Days",
    cell: ({ row }) => {
      const excusedDays = row.getValue("excusedDays") as number
      return <div className="font-medium text-blue-600">{excusedDays}</div>
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const attendance = row.original

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(attendance.employeeId)}
            >
              Copy Employee ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>View Details</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
