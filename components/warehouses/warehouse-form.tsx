// // app/components/warehouses/warehouse-form.tsx

// "use client"

// import * as React from "react"
// import { useForm } from "react-hook-form"
// import { zodResolver } from "@hookform/resolvers/zod"
// import * as z from "zod"
// import { Button } from "@/components/ui/button"
// import { Input } from "@/components/ui/input"
// import { Overlay, Dialog, DialogContent, DialogTrigger } from "@/components/ui/overlay"

// // Schema for form validation
// const warehouseSchema = z.object({
//   name: z.string().nonempty("Name is required"),
//   location: z.string().nonempty("Location is required"),
//   capacity: z.number().min(1, "Capacity must be at least 1"),
// })

// type WarehouseFormValues = z.infer<typeof warehouseSchema>

// export function WarehouseForm() {
//   const {
//     register,
//     handleSubmit,
//     formState: { errors },
//   } = useForm<WarehouseFormValues>({
//     resolver: zodResolver(warehouseSchema),
//   })

//   const onSubmit = (data: WarehouseFormValues) => {
//     console.log(data)
//     // Handle form submission
//   }

//   return (
//     <Dialog>
//       <DialogTrigger asChild>
//         <Button className="fixed top-4 right-4">Add Warehouse</Button>
//       </DialogTrigger>
//       <DialogContent>
//         <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
//           <div>
//             <label htmlFor="name" className="block text-sm font-medium">
//               Name
//             </label>
//             <Input id="name" {...register("name")} />
//             {errors.name && <p className="text-red-600">{errors.name.message}</p>}
//           </div>
//           <div>
//             <label htmlFor="location" className="block text-sm font-medium">
//               Location
//             </label>
//             <Input id="location" {...register("location")} />
//             {errors.location && <p className="text-red-600">{errors.location.message}</p>}
//           </div>
//           <div>
//             <label htmlFor="capacity" className="block text-sm font-medium">
//               Capacity
//             </label>
//             <Input id="capacity" type="number" {...register("capacity")} />
//             {errors.capacity && <p className="text-red-600">{errors.capacity.message}</p>}
//           </div>
//           <Button type="submit">Submit</Button>
//         </form>
//       </DialogContent>
//     </Dialog>
//   )
// }



// // app/components/warehouses/warehouse-form.tsx

// "use client"

// import * as React from "react"
// import { useForm } from "react-hook-form"
// import { zodResolver } from "@hookform/resolvers/zod"
// import * as z from "zod"
// import { Button } from "@/components/ui/button"
// import { Input } from "@/components/ui/input"
// import { Overlay } from "@/components/ui/overlay"

// // Schema for form validation
// const warehouseSchema = z.object({
//   name: z.string().nonempty("Name is required"),
//   location: z.string().nonempty("Location is required"),
//   capacity: z.number().min(1, "Capacity must be at least 1"),
// })

// type WarehouseFormValues = z.infer<typeof warehouseSchema>

// export function WarehouseForm() {
//   const [isOpen, setIsOpen] = React.useState(false)
//   const {
//     register,
//     handleSubmit,
//     formState: { errors },
//   } = useForm<WarehouseFormValues>({
//     resolver: zodResolver(warehouseSchema),
//   })

//   const onSubmit = (data: WarehouseFormValues) => {
//     console.log(data)
//     // Handle form submission
//   }

//   return (
//     <>
//       <Button onClick={() => setIsOpen(true)} className="fixed top-4 right-4">
//         Add Warehouse
//       </Button>
//       <Overlay isOpen={isOpen} onClose={() => setIsOpen(false)}>
//         <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
//           <div>
//             <label htmlFor="name" className="block text-sm font-medium">
//               Name
//             </label>
//             <Input id="name" {...register("name")} />
//             {errors.name && <p className="text-red-600">{errors.name.message}</p>}
//           </div>
//           <div>
//             <label htmlFor="location" className="block text-sm font-medium">
//               Location
//             </label>
//             <Input id="location" {...register("location")} />
//             {errors.location && <p className="text-red-600">{errors.location.message}</p>}
//           </div>
//           <div>
//             <label htmlFor="capacity" className="block text-sm font-medium">
//               Capacity
//             </label>
//             <Input id="capacity" type="number" {...register("capacity")} />
//             {errors.capacity && <p className="text-red-600">{errors.capacity.message}</p>}
//           </div>
//           <Button type="submit">Submit</Button>
//         </form>
//       </Overlay>
//     </>
//   )
// }



// app/components/warehouses/warehouse-form.tsx

"use client"

import * as React from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Overlay } from "@/components/ui/overlay"

// Schema for form validation
const warehouseSchema = z.object({
  name: z.string().nonempty("Name is required"),
  location: z.string().nonempty("Location is required"),
  capacity: z.number().min(1, "Capacity must be at least 1"),
})

type WarehouseFormValues = z.infer<typeof warehouseSchema>

export function WarehouseForm() {
  const [isOpen, setIsOpen] = React.useState(false)
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<WarehouseFormValues>({
    resolver: zodResolver(warehouseSchema),
  })

  const onSubmit = (data: WarehouseFormValues) => {
    console.log(data)
    // Handle form submission
  }

  return (
    <>
      <Button onClick={() => setIsOpen(true)} className="ml-4">
        Add Warehouse
      </Button>
      <Overlay isOpen={isOpen} onClose={() => setIsOpen(false)}>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium">
              Name
            </label>
            <Input id="name" {...register("name")} />
            {errors.name && <p className="text-red-600">{errors.name.message}</p>}
          </div>
          <div>
            <label htmlFor="location" className="block text-sm font-medium">
              Location
            </label>
            <Input id="location" {...register("location")} />
            {errors.location && <p className="text-red-600">{errors.location.message}</p>}
          </div>
          <div>
            <label htmlFor="capacity" className="block text-sm font-medium">
              Capacity
            </label>
            <Input id="capacity" type="number" {...register("capacity")} />
            {errors.capacity && <p className="text-red-600">{errors.capacity.message}</p>}
          </div>
          <Button type="submit">Submit</Button>
        </form>
      </Overlay>
    </>
  )
}
