// app/components/warehouses/columns.ts

import { ColumnDef } from "@tanstack/react-table"
import { Warehouse } from "@/app/types/warehouse"

export const columns: ColumnDef<Warehouse>[] = [
  {
    accessorKey: "warehouseId",
    header: "ID",
  },
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "location",
    header: "Location",
  },
  {
    accessorKey: "capacity",
    header: "Capacity",
  },
]
