"use client";

import Link from 'next/link';
import Image from 'next/image';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'white';
  showImage?: boolean;
}

export function Logo({ size = 'md', variant = 'default', showImage = false }: LogoProps) {
  const sizeClasses = {
    sm: 'h-6 w-20',
    md: 'h-8 w-28',
    lg: 'h-10 w-36',
  };

  const textSizeClasses = {
    sm: 'text-base',
    md: 'text-xl',
    lg: 'text-2xl',
  };

  const textColorClasses = {
    default: 'text-gray-900',
    white: 'text-white',
  };

  const logoSizeClasses = {
    sm: 40,
    md: 48,
    lg: 56,
  };

  return (
    <div className="flex flex-col items-center gap-4">
      {showImage && (
        <div className="mb-4">
          <Image
            src="/images/logo.png"
            alt="Kawandama Hills Plantation Logo"
            width={logoSizeClasses[size] * 2.5}
            height={logoSizeClasses[size] * 2.5}
            priority
          />
        </div>
      )}

      <Link href="/" className="flex items-center gap-3">
        <div className="flex items-center justify-center">
          <div className={`${sizeClasses[size]} flex items-center justify-center border border-green-600 rounded-md`}>
            <span className={`font-bold text-green-600 px-2`}>
              KHP
            </span>
          </div>
        </div>
        <span className={`font-semibold ${textSizeClasses[size]} text-green-600 whitespace-nowrap`}>
          Kawandama Hills Plantation
        </span>
      </Link>
    </div>
  );
}
