// app/components/ui/overlay.tsx

import * as React from "react"
import { cn } from "@/lib/utils"

interface OverlayProps {
  isOpen: boolean
  onClose: () => void
  children: React.ReactNode
}

export function Overlay({ isOpen, onClose, children }: OverlayProps) {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="relative w-full max-w-lg p-4 bg-white rounded-md shadow-lg">
        <button
          onClick={onClose}
          className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
        >
          <span className="sr-only">Close</span>
          &times;
        </button>
        {children}
      </div>
    </div>
  )
}
