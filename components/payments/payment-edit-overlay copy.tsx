// // app/components/payments/payment-edit-overlay.tsx

// "use client"

// import * as React from "react"
// import { useForm, FormProvider, useFormContext } from "react-hook-form"
// import { zodResolver } from "@hookform/resolvers/zod"
// import * as z from "zod"
// import { Button } from "@/components/ui/button"
// import { Input } from "@/components/ui/input"
// import { Overlay } from "@/components/ui/overlay"
// import { Payment } from "@/app/types/payment"
// import { Employee } from "@/app/types/employee"
// import DuplicatePaymentError from "@/components/payments/duplicate-payment-error"
// import { calculateTax } from "@/lib/taxCalculation"
// import { UserRole } from "@/context/types"  // Import the UserRole type

// const paymentSchema = z.object({
//     amount: z.string().nonempty("Amount is required"),
//     status: z.enum(['pending', 'revised', 'review', 'approved', 'processing', 'failed', 'cancel']),
//     employee: z.string().nonempty("Employee is required"),
//     tax: z.string().optional().default('0'),
//     loan_deduction: z.string().optional().default('0'),
//     insurance: z.string().optional().default('0'),
//     final_amount: z.string().optional().default('0')
// })

// type PaymentFormValues = z.infer<typeof paymentSchema>

// interface PaymentEditOverlayProps {
//     isOpen: boolean;
//     onClose: () => void;
//     payment: Payment;
//     onSubmit: (id: string, data: Partial<Payment>) => Promise<void>;
//     employees: { _id: string; firstName: string; lastName: string; phone: string; salary: number }[];
//     userRole: UserRole;  // Use the UserRole type
// }

// interface StepProps {
//     payment: Payment;
//     employees: { _id: string; firstName: string; lastName: string; phone: string; salary: number }[];
//     userRole: UserRole;  // Use the UserRole type
// }

// const Step: React.FC<StepProps> = ({ payment, employees, userRole }) => {
//     const methods = useFormContext<PaymentFormValues>()
//     const { register, formState: { errors }, watch, setValue } = methods

//     const selectedEmployeeId = watch("employee");
//     const loanDeduction = parseFloat(watch("loan_deduction") || '0');
//     const insurance = parseFloat(watch("insurance") || '0');
//     const selectedEmployee = employees.find(employee => employee._id === selectedEmployeeId);

//     React.useEffect(() => {
//         if (selectedEmployee) {
//             const amount = parseFloat(selectedEmployee.salary.toString());
//             const tax = calculateTax(amount);
//             const finalAmount = amount - tax - loanDeduction - insurance;

//             setValue("amount", amount.toString());
//             setValue("tax", tax.toString());
//             setValue("final_amount", finalAmount.toString());
//         }
//     }, [selectedEmployee, loanDeduction, insurance, setValue]);

//     return (
//         <div>
//             <label htmlFor="employee" className="block text-sm font-medium">Employee</label>
//             <select id="employee" defaultValue={typeof payment.employee === 'string' ? payment.employee : payment.employee._id} {...register('employee')}>
//                 {employees.map(emp => (
//                     <option key={emp._id} value={emp._id}>{emp.firstName} {emp.lastName}</option>
//                 ))}
//             </select>
//             {errors.employee && <p className="text-red-600">{errors.employee.message}</p>}

//             {selectedEmployee && (
//                 <>
//                     <label htmlFor="amount" className="block text-sm font-medium">Amount</label>
//                     <Input id="amount" type="text" {...register('amount')} readOnly />

//                     <label htmlFor="tax" className="block text-sm font-medium">Tax</label>
//                     <Input id="tax" type="text" {...register('tax')} readOnly />

//                     <label htmlFor="loan_deduction" className="block text-sm font-medium">Loan Deduction</label>
//                     <Input id="loan_deduction" type="text" {...register('loan_deduction')} />

//                     <label htmlFor="insurance" className="block text-sm font-medium">Insurance</label>
//                     <Input id="insurance" type="text" {...register('insurance')} />

//                     <label htmlFor="final_amount" className="block text-sm font-medium">Final Amount</label>
//                     <Input id="final_amount" type="text" {...register('final_amount')} readOnly />
//                 </>
//             )}

//             <label htmlFor="status" className="block text-sm font-medium">Status</label>
//             {userRole === 'accountant' ? (
//                 <Input id="status" type="text" value="pending" readOnly />
//             ) : (
//                 <select id="status" defaultValue={payment.status} {...register('status')}>
//                     {userRole === 'accountSupervisor' && (
//                         <>
//                             <option value="pending">Pending</option>
//                             <option value="revised">Revised</option>
//                         </>
//                     )}
//                     {(userRole === 'admin' || userRole === 'ceoadmin') && (
//                         <>
//                             <option value="review">Review</option>
//                             <option value="approved">Approved</option>
//                             <option value="cancel">Cancel</option>
//                             <option value="processing">Processing</option>
//                         </>
//                     )}
//                 </select>
//             )}
//             {errors.status && <p className="text-red-600">{errors.status.message}</p>}
//         </div>
//     )
// }

// export default function PaymentEditOverlay({ isOpen, onClose, payment, onSubmit, employees, userRole }: PaymentEditOverlayProps) {
//     const [alertMessage, setAlertMessage] = React.useState<string | null>(null);

//     const methods = useForm<PaymentFormValues>({
//         resolver: zodResolver(paymentSchema),
//         defaultValues: {
//             amount: payment.amount.toString(),
//             status: payment.status,
//             employee: typeof payment.employee === 'string' ? payment.employee : payment.employee._id,
//             tax: payment.tax || '0',
//             loan_deduction: payment.loan_deduction || '0',
//             insurance: payment.insurance || '0',
//             final_amount: payment.final_amount || '0'
//         }
//     })
//     const { handleSubmit, formState: { isSubmitting } } = methods

//     const handleFormSubmit = async (data: PaymentFormValues) => {
//         const amount = parseFloat(data.amount);
//         const tax = parseFloat(data.tax);
//         const loanDeduction = parseFloat(data.loan_deduction);
//         const insurance = parseFloat(data.insurance);
//         const finalAmount = amount - tax - loanDeduction - insurance;
//         const updatedData = { ...data, amount: amount.toString(), tax: tax.toString(), final_amount: finalAmount.toString() };

//         try {
//             await onSubmit(payment._id, updatedData);
//             onClose();
//         } catch (error: any) {
//             console.error("Error in handleFormSubmit:", error);
//             if (error.response?.status === 409) {
//                 setAlertMessage('Employee has already been paid within the last 28 days');
//             } else {
//                 setAlertMessage('An unexpected error occurred. Please try again.');
//             }
//         }
//     };

//     return (
//         <Overlay isOpen={isOpen} onClose={onClose}>
//             {alertMessage === 'Employee has already been paid within the last 28 days' && (
//                 <DuplicatePaymentError onClose={() => setAlertMessage(null)} />
//             )}
//             <FormProvider {...methods}>
//                 <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
//                     {alertMessage && alertMessage !== 'Employee has already been paid within the last 28 days' && (
//                         <div className="p-4 bg-red-100 rounded-md shadow-md">
//                             <h2 className="text-xl font-bold text-red-800">Error</h2>
//                             <p className="mt-2 text-red-600">{alertMessage}</p>
//                             <Button onClick={() => setAlertMessage(null)} className="mt-4">Close</Button>
//                         </div>
//                     )}
//                     <Step payment={payment} employees={employees} userRole={userRole} />
//                     <div className="flex justify-between">
//                         <Button type="submit" disabled={isSubmitting}>
//                             Submit
//                         </Button>
//                     </div>
//                 </form>
//             </FormProvider>
//         </Overlay>
//     )
// }
