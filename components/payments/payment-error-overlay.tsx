// app/components/employees/employee-error-overlay.tsx

"use client"

import React from 'react';
import { Overlay } from '@/components/ui/overlay';
import { Button } from '@/components/ui/button';

interface PaymentErrorOverlayProps {
    onClose: () => void;
}

export default function PaymentErrorOverlay({ onClose }: PaymentErrorOverlayProps) {
    return (
        <Overlay isOpen={true} onClose={onClose}>
            <div className="p-4 bg-red-100 rounded-md shadow-md">
                <h2 className="text-xl font-bold text-red-800">Error</h2>
                <p className="mt-2 text-red-600">An error occurred during the operation.</p>
                <Button onClick={onClose} className="mt-4">Close</Button>
            </div>
        </Overlay>
    );
}
