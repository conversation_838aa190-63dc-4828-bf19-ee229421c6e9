"use client"

import * as React from "react"
import { useForm, FormProvider } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Employee } from "@/app/types/employee"
import { Overlay } from "@/components/ui/overlay" // Import Overlay component

// Schema for form validation
const paymentSchema = z.object({
    amount: z.string().nonempty("Amount is required"),
    status: z.enum(['success', 'processing', 'failed', 'pending']),
    employee: z.string().nonempty("Employee is required")
})

type PaymentFormValues = z.infer<typeof paymentSchema>

interface PaymentFormProps {
    employees: Employee[];
    onSubmit: (data: Omit<PaymentFormValues, 'employee'> & { employee: string }) => void;
    isOpen: boolean; // Prop to control modal visibility
    onClose: () => void; // Prop to handle modal close
}

export const PaymentForm: React.FC<PaymentFormProps> = ({ employees, onSubmit, isOpen, onClose }) => {
    const methods = useForm<PaymentFormValues>({
        resolver: zodResolver(paymentSchema),
        defaultValues: {
            amount: "",
            status: "pending",
            employee: ""
        }
    });

    const { handleSubmit, register, formState: { errors }, watch } = methods;

    const selectedEmployeeId = watch("employee");
    const selectedEmployee = employees.find(employee => employee._id === selectedEmployeeId);

    // const handleFormSubmit = (data: PaymentFormValues) => {
    //     const paymentData = {
    //         ...data,
    //         amount: selectedEmployee ? selectedEmployee.salary : ""
    //     };
    //     console.log(paymentData); // Log data for debugging
    //     onSubmit(paymentData);
    // };


    const handleFormSubmit = (data: PaymentFormValues) => {
        const paymentData = {
            ...data,
            amount: selectedEmployee ? selectedEmployee.salary.toString() : "0", // Convert amount to string
        };
        console.log(paymentData); // Log data for debugging
        onSubmit(paymentData);
    };

    return (
        <Overlay isOpen={isOpen} onClose={onClose}>
            <FormProvider {...methods}>
                <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4 p-4 bg-white rounded shadow-md">
                    <label htmlFor="employee" className="block text-sm font-medium">Employee</label>
                    <select id="employee" {...register("employee")}>
                        {employees.map(employee => (
                            <option key={employee._id} value={employee._id}>
                                {employee.firstName} {employee.lastName}
                            </option>
                        ))}
                    </select>
                    {errors.employee && <p className="text-red-600">{errors.employee.message}</p>}

                    {selectedEmployee && (
                        <>
                            <label htmlFor="amount" className="block text-sm font-medium">Amount</label>
                            <Input
                                id="amount"
                                type="text"
                                value={selectedEmployee.salary}
                                readOnly
                            />
                        </>
                    )}

                    <label htmlFor="status" className="block text-sm font-medium">Status</label>
                    <select id="status" {...register("status")}>
                        <option value="success">Success</option>
                        <option value="processing">Processing</option>
                        <option value="failed">Failed</option>
                        <option value="pending">Pending</option>
                    </select>
                    {errors.status && <p className="text-red-600">{errors.status.message}</p>}

                    <div className="flex justify-end space-x-2">
                        <Button type="button" variant="outline" onClick={onClose}>
                            Cancel
                        </Button>
                        <Button type="submit">Submit</Button>
                    </div>
                </form>
            </FormProvider>
        </Overlay>
    )
}
