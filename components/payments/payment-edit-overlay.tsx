// // import React, { useState, useEffect } from 'react';
// // import { Employee } from '@/app/types/employee';
// // import { Payment } from '@/app/types/payment';
// // import { calculateTax, calculateWorkingDays, calculateLoanDeductions } from '@/lib/taxCalculation';
// // import { Dialog, Transition } from '@headlessui/react';
// // import { Fragment } from 'react';

// // interface PaymentEditOverlayProps {
// //     isOpen: boolean;
// //     onClose: () => void;
// //     onSubmit: (id: string, data: Partial<Payment>) => void;
// //     userRole: string;
// //     employees: Employee[];
// //     payment?: Payment | null;
// // }

// // export default function PaymentEditOverlay({
// //     isOpen,
// //     onClose,
// //     onSubmit,
// //     userRole,
// //     employees,
// //     payment,
// // }: PaymentEditOverlayProps) {
// //     const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
// //     const [attendanceDays, setAttendanceDays] = useState(0);
// //     const [dayRate, setDayRate] = useState('0');
// //     const [totalPayment, setTotalPayment] = useState('0');
// //     const [tax, setTax] = useState('0');
// //     const [bonusPayment, setBonusPayment] = useState('0');
// //     const [insurance, setInsurance] = useState('0');
// //     const [loanDeductions, setLoanDeductions] = useState({
// //         social_welfare: '0',
// //         salary_advance: '0',
// //         medical: '0',
// //         extra: [{ type: '', amount: '0' }]
// //     });
// //     const [pensionDeduction, setPensionDeduction] = useState('0');
// //     const [finalAmount, setFinalAmount] = useState('0');
// //     const [loading, setLoading] = useState(false);
// //     const [step, setStep] = useState(1);
// //     const [employeeSearch, setEmployeeSearch] = useState('');
// //     const [selectedMonth, setSelectedMonth] = useState<number>(new Date().getMonth() + 1);
// //     const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());

// //     useEffect(() => {
// //         if (selectedEmployee) {
// //             const fetchAttendance = async () => {
// //                 setLoading(true);
// //                 try {
// //                     const response = await fetch(`/api/attendance/getAttendanceByEmployee`, {
// //                         method: 'POST',
// //                         headers: {
// //                             'Content-Type': 'application/json',
// //                         },
// //                         body: JSON.stringify({ employeeIdentity: selectedEmployee.employeeIdentity, month: selectedMonth, year: selectedYear }),
// //                     });

// //                     if (!response.ok) {
// //                         throw new Error('Failed to fetch attendance records');
// //                     }

// //                     const { attendances } = await response.json();
// //                     const attendanceCount = attendances.length;
// //                     setAttendanceDays(attendanceCount);

// //                     const workingDays = calculateWorkingDays(selectedMonth, selectedYear);
// //                     const dayRateValue = (parseFloat(selectedEmployee.salary) / workingDays).toFixed(2);
// //                     setDayRate(dayRateValue);

// //                     const totalPaymentValue = (parseFloat(dayRateValue) * attendanceCount).toFixed(2);
// //                     setTotalPayment(totalPaymentValue);
// //                 } catch (error) {
// //                     console.error(error);
// //                 } finally {
// //                     setLoading(false);
// //                 }
// //             };
// //             fetchAttendance();

// //             const taxValue = calculateTax(parseFloat(selectedEmployee.salary));
// //             setTax(taxValue.toFixed(2));
// //         }
// //     }, [selectedEmployee, selectedMonth, selectedYear]);

// //     useEffect(() => {
// //         if (step === 3) {
// //             const pension = (parseFloat(totalPayment) * 0.07).toFixed(2);
// //             setPensionDeduction(pension);

// //             const loanDeductionsTotal = calculateLoanDeductions(loanDeductions);
// //             const finalAmountValue = (
// //                 parseFloat(totalPayment) +
// //                 parseFloat(bonusPayment) -
// //                 parseFloat(tax) -
// //                 loanDeductionsTotal -
// //                 parseFloat(insurance) -
// //                 parseFloat(pension)
// //             ).toFixed(2);
// //             setFinalAmount(finalAmountValue);
// //         }
// //     }, [step, totalPayment, bonusPayment, insurance, loanDeductions, tax]);

// //     const handleEmployeeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
// //         const employeeIdentity = e.target.value;
// //         const employee = employees.find(emp => emp.employeeIdentity === employeeIdentity) || null;
// //         setSelectedEmployee(employee);
// //     };

// //     const handleMonthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
// //         setSelectedMonth(parseInt(e.target.value));
// //     };

// //     const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
// //         setSelectedYear(parseInt(e.target.value));
// //     };

// //     const handleNextStep = () => {
// //         if (step < 3) {
// //             setStep(step + 1);
// //         }
// //     };

// //     const handlePreviousStep = () => {
// //         if (step > 1) {
// //             setStep(step - 1);
// //         }
// //     };

// //     const handleSubmit = () => {
// //         if (selectedEmployee) {
// //             const paymentData: Partial<Payment> = {
// //                 amount: totalPayment,
// //                 bonus_payment: bonusPayment,
// //                 status: 'pending',
// //                 employee: selectedEmployee._id,
// //                 tax,
// //                 loan_deduction: loanDeductions,
// //                 insurance,
// //                 pension_deduction: pensionDeduction,
// //                 final_amount: finalAmount,
// //                 day_rate: dayRate,
// //                 working_days: calculateWorkingDays(selectedMonth, selectedYear).toString(),
// //                 attendance_days: attendanceDays.toString(),
// //                 employeeIdentity: selectedEmployee.employeeIdentity,
// //                 month: selectedMonth,
// //                 year: selectedYear,
// //             };

// //             onSubmit(payment?._id || '', paymentData);
// //             onClose();
// //         }
// //     };

// //     const handleLoanChange = (index: number, field: 'type' | 'amount', value: string) => {
// //         const newLoanDeductions = { ...loanDeductions };
// //         newLoanDeductions.extra[index][field] = value;
// //         setLoanDeductions(newLoanDeductions);
// //     };

// //     const addExtraLoan = () => {
// //         setLoanDeductions({
// //             ...loanDeductions,
// //             extra: [...loanDeductions.extra, { type: '', amount: '0' }]
// //         });
// //     };

// //     const filteredEmployees = employees.filter(
// //         emp =>
// //             emp.firstName.toLowerCase().includes(employeeSearch.toLowerCase()) ||
// //             emp.lastName.toLowerCase().includes(employeeSearch.toLowerCase()) ||
// //             emp.employeeIdentity.toLowerCase().includes(employeeSearch.toLowerCase())
// //     );

// //     return (
// //         <Transition appear show={isOpen} as={Fragment}>
// //             <Dialog as="div" className="relative z-10" onClose={onClose}>
// //                 <Transition.Child
// //                     as={Fragment}
// //                     enter="ease-out duration-300"
// //                     enterFrom="opacity-0"
// //                     enterTo="opacity-100"
// //                     leave="ease-in duration-200"
// //                     leaveFrom="opacity-100"
// //                     leaveTo="opacity-0"
// //                 >
// //                     <div className="fixed inset-0 bg-black bg-opacity-25" />
// //                 </Transition.Child>

// //                 <div className="fixed inset-0 overflow-y-auto">
// //                     <div className="flex min-h-full items-center justify-center p-4 text-center">
// //                         <Transition.Child
// //                             as={Fragment}
// //                             enter="ease-out duration-300"
// //                             enterFrom="opacity-0 scale-95"
// //                             enterTo="opacity-100 scale-100"
// //                             leave="ease-in duration-200"
// //                             leaveFrom="opacity-100 scale-100"
// //                             leaveTo="opacity-0 scale-95"
// //                         >
// //                             <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
// //                                 <Dialog.Title
// //                                     as="h3"
// //                                     className="text-lg font-medium leading-6 text-gray-900"
// //                                 >
// //                                     {payment ? 'Edit Payment' : 'Create Payment'}
// //                                 </Dialog.Title>

// //                                 {/* Step 1 */}
// //                                 {step === 1 && (
// //                                     <div>
// //                                         <div className="relative mt-4">
// //                                             <div className="mt-2 mb-2">
// //                                                 <input
// //                                                     type="text"
// //                                                     placeholder="Search Employee"
// //                                                     className="w-full px-3 py-2 border rounded-md focus:ring-indigo-500 focus:border-indigo-500"
// //                                                     value={employeeSearch}
// //                                                     onChange={(e) => setEmployeeSearch(e.target.value)}
// //                                                 />
// //                                             </div>

// //                                             <select
// //                                                 className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
// //                                                 value={selectedEmployee?.employeeIdentity || ""}
// //                                                 onChange={handleEmployeeChange}
// //                                             >
// //                                                 <option value="" disabled>Select Employee</option>
// //                                                 {filteredEmployees.map(emp => (
// //                                                     <option key={emp.employeeIdentity} value={emp.employeeIdentity}>
// //                                                         {emp.firstName} {emp.lastName} - {emp.employeeIdentity}
// //                                                     </option>
// //                                                 ))}
// //                                             </select>
// //                                         </div>

// //                                         {selectedEmployee && (
// //                                             <>
// //                                                 <div className="mt-4">
// //                                                     <label className="block text-sm font-medium text-gray-700">Select Month you are paying for</label>
// //                                                     <select
// //                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
// //                                                         value={selectedMonth}
// //                                                         onChange={handleMonthChange}
// //                                                     >
// //                                                         {Array.from({ length: 12 }, (_, i) => (
// //                                                             <option key={i + 1} value={i + 1}>
// //                                                                 {new Date(0, i).toLocaleString('default', { month: 'long' })}
// //                                                             </option>
// //                                                         ))}
// //                                                     </select>
// //                                                 </div>

// //                                                 <div className="mt-4">
// //                                                     <label className="block text-sm font-medium text-gray-700">Year</label>
// //                                                     <select
// //                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
// //                                                         value={selectedYear}
// //                                                         onChange={handleYearChange}
// //                                                     >
// //                                                         {Array.from({ length: 5 }, (_, i) => (
// //                                                             <option key={i} value={new Date().getFullYear() - i}>
// //                                                                 {new Date().getFullYear() - i}
// //                                                             </option>
// //                                                         ))}
// //                                                     </select>
// //                                                 </div>

// //                                                 <div className="mt-4">
// //                                                     <label className="block text-sm font-medium text-gray-700">Salary</label>
// //                                                     <input
// //                                                         type="text"
// //                                                         readOnly
// //                                                         value={selectedEmployee.salary}
// //                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
// //                                                     />
// //                                                 </div>

// //                                                 <div className="mt-4">
// //                                                     <label className="block text-sm font-medium text-gray-700">Attendance Days</label>
// //                                                     <input
// //                                                         type="text"
// //                                                         readOnly
// //                                                         value={attendanceDays}
// //                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
// //                                                     />
// //                                                 </div>

// //                                                 <div className="mt-4">
// //                                                     <label className="block text-sm font-medium text-gray-700">Day Rate</label>
// //                                                     <input
// //                                                         type="text"
// //                                                         readOnly
// //                                                         value={dayRate}
// //                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
// //                                                     />
// //                                                 </div>

// //                                                 <div className="mt-4">
// //                                                     <label className="block text-sm font-medium text-gray-700">Tax</label>
// //                                                     <input
// //                                                         type="text"
// //                                                         readOnly
// //                                                         value={tax}
// //                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
// //                                                     />
// //                                                 </div>

// //                                                 <div className="mt-4">
// //                                                     <label className="block text-sm font-medium text-gray-700">Total Payment</label>
// //                                                     <input
// //                                                         type="text"
// //                                                         readOnly
// //                                                         value={totalPayment}
// //                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
// //                                                     />
// //                                                 </div>
// //                                             </>
// //                                         )}
// //                                     </div>
// //                                 )}

// //                                 {/* Step 2 */}
// //                                 {step === 2 && (
// //                                     <div>
// //                                         <div className="mt-4">
// //                                             <label className="block text-sm font-medium text-gray-700">Bonus Payment</label>
// //                                             <input
// //                                                 type="text"
// //                                                 value={bonusPayment}
// //                                                 onChange={(e) => setBonusPayment(e.target.value)}
// //                                                 className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
// //                                             />
// //                                         </div>

// //                                         <div className="mt-4">
// //                                             <label className="block text-sm font-medium text-gray-700">Insurance</label>
// //                                             <input
// //                                                 type="text"
// //                                                 value={insurance}
// //                                                 onChange={(e) => setInsurance(e.target.value)}
// //                                                 className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
// //                                             />
// //                                         </div>

// //                                         <div className="mt-4">
// //                                             <label className="block text-sm font-medium text-gray-700">Loan Deductions</label>
// //                                             <div>
// //                                                 <label className="block text-sm font-medium text-gray-700">Social Welfare</label>
// //                                                 <input
// //                                                     type="text"
// //                                                     value={loanDeductions.social_welfare}
// //                                                     onChange={(e) => setLoanDeductions({ ...loanDeductions, social_welfare: e.target.value })}
// //                                                     className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
// //                                                 />
// //                                             </div>

// //                                             <div className="mt-4">
// //                                                 <label className="block text-sm font-medium text-gray-700">Salary Advance</label>
// //                                                 <input
// //                                                     type="text"
// //                                                     value={loanDeductions.salary_advance}
// //                                                     onChange={(e) => setLoanDeductions({ ...loanDeductions, salary_advance: e.target.value })}
// //                                                     className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
// //                                                 />
// //                                             </div>

// //                                             <div className="mt-4">
// //                                                 <label className="block text-sm font-medium text-gray-700">Medical</label>
// //                                                 <input
// //                                                     type="text"
// //                                                     value={loanDeductions.medical}
// //                                                     onChange={(e) => setLoanDeductions({ ...loanDeductions, medical: e.target.value })}
// //                                                     className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
// //                                                 />
// //                                             </div>

// //                                             {loanDeductions.extra.map((extra, index) => (
// //                                                 <div key={index} className="mt-4">
// //                                                     <label className="block text-sm font-medium text-gray-700">Loan Type</label>
// //                                                     <input
// //                                                         type="text"
// //                                                         value={extra.type}
// //                                                         onChange={(e) => handleLoanChange(index, 'type', e.target.value)}
// //                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
// //                                                     />

// //                                                     <label className="block text-sm font-medium text-gray-700 mt-2">Amount</label>
// //                                                     <input
// //                                                         type="text"
// //                                                         value={extra.amount}
// //                                                         onChange={(e) => handleLoanChange(index, 'amount', e.target.value)}
// //                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
// //                                                     />
// //                                                 </div>
// //                                             ))}

// //                                             <button
// //                                                 type="button"
// //                                                 onClick={addExtraLoan}
// //                                                 className="mt-4 inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
// //                                             >
// //                                                 Add Extra Loan
// //                                             </button>
// //                                         </div>
// //                                     </div>
// //                                 )}

// //                                 {/* Step 3 */}
// //                                 {step === 3 && (
// //                                     <div>
// //                                         <div className="mt-4">
// //                                             <label className="block text-sm font-medium text-gray-700">Tax</label>
// //                                             <input
// //                                                 type="text"
// //                                                 readOnly
// //                                                 value={tax}
// //                                                 className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
// //                                             />
// //                                         </div>

// //                                         <div className="mt-4">
// //                                             <label className="block text-sm font-medium text-gray-700">Pension Deduction</label>
// //                                             <input
// //                                                 type="text"
// //                                                 readOnly
// //                                                 value={pensionDeduction}
// //                                                 className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
// //                                             />
// //                                         </div>

// //                                         <div className="mt-4">
// //                                             <label className="block text-sm font-medium text-gray-700">Final Amount</label>
// //                                             <input
// //                                                 type="text"
// //                                                 readOnly
// //                                                 value={finalAmount}
// //                                                 className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
// //                                             />
// //                                         </div>
// //                                     </div>
// //                                 )}

// //                                 <div className="mt-4 flex justify-between">
// //                                     {step > 1 && (
// //                                         <button
// //                                             type="button"
// //                                             onClick={handlePreviousStep}
// //                                             className="inline-flex justify-center rounded-md border border-transparent bg-gray-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
// //                                         >
// //                                             Previous
// //                                         </button>
// //                                     )}

// //                                     {step < 3 && selectedEmployee && (
// //                                         <button
// //                                             type="button"
// //                                             onClick={handleNextStep}
// //                                             className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
// //                                         >
// //                                             Next
// //                                         </button>
// //                                     )}

// //                                     {step === 3 && (
// //                                         <button
// //                                             type="button"
// //                                             onClick={handleSubmit}
// //                                             className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
// //                                             disabled={loading}
// //                                         >
// //                                             {payment ? 'Update Payment' : 'Create Payment'}
// //                                         </button>
// //                                     )}
// //                                 </div>
// //                             </Dialog.Panel>
// //                         </Transition.Child>
// //                     </div>
// //                 </div>
// //             </Dialog>
// //         </Transition>
// //     );
// // }



// import React, { useState, useEffect } from 'react';
// import { Employee } from '@/app/types/employee';
// import { Payment } from '@/app/types/payment';
// import { calculateTax, calculateWorkingDays, calculateLoanDeductions } from '@/lib/taxCalculation';
// import { Dialog, Transition } from '@headlessui/react';
// import { Fragment } from 'react';

// interface PaymentEditOverlayProps {
//     isOpen: boolean;
//     onClose: () => void;
//     onSubmit: (id: string, data: Partial<Payment>) => void;
//     userRole: string;
//     employees: Employee[];
//     payment?: Payment | null;
// }

// export default function PaymentEditOverlay({
//     isOpen,
//     onClose,
//     onSubmit,
//     userRole,
//     employees,
//     payment,
// }: PaymentEditOverlayProps) {
//     const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
//     const [attendanceDays, setAttendanceDays] = useState(0);
//     const [dayRate, setDayRate] = useState('0');
//     const [totalPayment, setTotalPayment] = useState('0');
//     const [tax, setTax] = useState('0');
//     const [bonusPayment, setBonusPayment] = useState('0');
//     const [insurance, setInsurance] = useState('0');
//     const [loanDeductions, setLoanDeductions] = useState({
//         social_welfare: '0',
//         salary_advance: '0',
//         medical: '0',
//         extra: [{ type: '', amount: '0' }]
//     });
//     const [pensionDeduction, setPensionDeduction] = useState('0');
//     const [finalAmount, setFinalAmount] = useState('0');
//     const [loading, setLoading] = useState(false);
//     const [step, setStep] = useState(1);
//     const [employeeSearch, setEmployeeSearch] = useState('');
//     const [selectedMonth, setSelectedMonth] = useState<number>(new Date().getMonth() + 1);
//     const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());

//     // Fetch attendance based on selected employee, month, and year
//     useEffect(() => {
//         if (selectedEmployee) {
//             const fetchAttendance = async () => {
//                 setLoading(true);
//                 try {
//                     const response = await fetch(`/api/attendance/getAttendanceByEmployee`, {
//                         method: 'POST',
//                         headers: {
//                             'Content-Type': 'application/json',
//                         },
//                         body: JSON.stringify({
//                             employeeIdentity: selectedEmployee.employeeIdentity,
//                             month: selectedMonth,
//                             year: selectedYear
//                         }),
//                     });

//                     if (!response.ok) {
//                         throw new Error('Failed to fetch attendance records');
//                     }

//                     const { attendances } = await response.json();
//                     const attendanceCount = attendances.length;
//                     setAttendanceDays(attendanceCount);

//                     // Calculate the working days and day rate
//                     const workingDays = calculateWorkingDays(selectedMonth, selectedYear);
//                     const dayRateValue = (parseFloat(selectedEmployee.salary) / workingDays).toFixed(2);
//                     setDayRate(dayRateValue);

//                     // Calculate total payment based on attendance days
//                     const totalPaymentValue = (parseFloat(dayRateValue) * attendanceCount).toFixed(2);
//                     setTotalPayment(totalPaymentValue);
//                 } catch (error) {
//                     console.error(error);
//                     setAttendanceDays(0);  // If no attendance, set to 0
//                 } finally {
//                     setLoading(false);
//                 }
//             };
//             fetchAttendance();

//             // Calculate tax for the employee salary
//             const taxValue = calculateTax(parseFloat(selectedEmployee.salary));
//             setTax(taxValue.toFixed(2));
//         }
//     }, [selectedEmployee, selectedMonth, selectedYear]);

//     // Calculate final payment amount at step 3
//     useEffect(() => {
//         if (step === 3) {
//             const pension = (parseFloat(totalPayment) * 0.07).toFixed(2);
//             setPensionDeduction(pension);

//             const loanDeductionsTotal = calculateLoanDeductions(loanDeductions);
//             const finalAmountValue = (
//                 parseFloat(totalPayment) +
//                 parseFloat(bonusPayment) -
//                 parseFloat(tax) -
//                 loanDeductionsTotal -
//                 parseFloat(insurance) -
//                 parseFloat(pension)
//             ).toFixed(2);
//             setFinalAmount(finalAmountValue);
//         }
//     }, [step, totalPayment, bonusPayment, insurance, loanDeductions, tax]);

//     // Handlers for dropdowns and form submission
//     const handleEmployeeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
//         const employeeIdentity = e.target.value;
//         const employee = employees.find(emp => emp.employeeIdentity === employeeIdentity) || null;
//         setSelectedEmployee(employee);
//     };

//     const handleMonthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
//         setSelectedMonth(parseInt(e.target.value));
//     };

//     const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
//         setSelectedYear(parseInt(e.target.value));
//     };

//     const handleNextStep = () => {
//         if (step < 3) {
//             setStep(step + 1);
//         }
//     };

//     const handlePreviousStep = () => {
//         if (step > 1) {
//             setStep(step - 1);
//         }
//     };

//     const handleSubmit = () => {
//         if (selectedEmployee) {
//             const paymentData: Partial<Payment> = {
//                 amount: totalPayment,
//                 bonus_payment: bonusPayment,
//                 status: 'pending',
//                 employee: selectedEmployee._id,
//                 tax,
//                 loan_deduction: loanDeductions,
//                 insurance,
//                 pension_deduction: pensionDeduction,
//                 final_amount: finalAmount,
//                 day_rate: dayRate,
//                 working_days: calculateWorkingDays(selectedMonth, selectedYear).toString(),
//                 attendance_days: attendanceDays.toString(),
//                 employeeIdentity: selectedEmployee.employeeIdentity,
//                 month: selectedMonth,
//                 year: selectedYear,
//             };

//             onSubmit(payment?._id || '', paymentData);
//             onClose();
//         }
//     };

//     const handleLoanChange = (index: number, field: 'type' | 'amount', value: string) => {
//         const newLoanDeductions = { ...loanDeductions };
//         newLoanDeductions.extra[index][field] = value;
//         setLoanDeductions(newLoanDeductions);
//     };

//     const addExtraLoan = () => {
//         setLoanDeductions({
//             ...loanDeductions,
//             extra: [...loanDeductions.extra, { type: '', amount: '0' }]
//         });
//     };

//     const filteredEmployees = employees.filter(
//         emp =>
//             emp.firstName.toLowerCase().includes(employeeSearch.toLowerCase()) ||
//             emp.lastName.toLowerCase().includes(employeeSearch.toLowerCase()) ||
//             emp.employeeIdentity.toLowerCase().includes(employeeSearch.toLowerCase())
//     );

//     return (
//         <Transition appear show={isOpen} as={Fragment}>
//             <Dialog as="div" className="relative z-10" onClose={onClose}>
//                 <Transition.Child
//                     as={Fragment}
//                     enter="ease-out duration-300"
//                     enterFrom="opacity-0"
//                     enterTo="opacity-100"
//                     leave="ease-in duration-200"
//                     leaveFrom="opacity-100"
//                     leaveTo="opacity-0"
//                 >
//                     <div className="fixed inset-0 bg-black bg-opacity-25" />
//                 </Transition.Child>

//                 <div className="fixed inset-0 overflow-y-auto">
//                     <div className="flex min-h-full items-center justify-center p-4 text-center">
//                         <Transition.Child
//                             as={Fragment}
//                             enter="ease-out duration-300"
//                             enterFrom="opacity-0 scale-95"
//                             enterTo="opacity-100 scale-100"
//                             leave="ease-in duration-200"
//                             leaveFrom="opacity-100 scale-100"
//                             leaveTo="opacity-0 scale-95"
//                         >
//                             <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
//                                 <Dialog.Title
//                                     as="h3"
//                                     className="text-lg font-medium leading-6 text-gray-900"
//                                 >
//                                     {payment ? 'Edit Payment' : 'Create Payment'}
//                                 </Dialog.Title>

//                                 {/* Step 1 */}
//                                 {step === 1 && (
//                                     <div>
//                                         <div className="relative mt-4">
//                                             <div className="mt-2 mb-2">
//                                                 <input
//                                                     type="text"
//                                                     placeholder="Search Employee"
//                                                     className="w-full px-3 py-2 border rounded-md focus:ring-indigo-500 focus:border-indigo-500"
//                                                     value={employeeSearch}
//                                                     onChange={(e) => setEmployeeSearch(e.target.value)}
//                                                 />
//                                             </div>

//                                             <select
//                                                 className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                 value={selectedEmployee?.employeeIdentity || ""}
//                                                 onChange={handleEmployeeChange}
//                                             >
//                                                 <option value="" disabled>Select Employee</option>
//                                                 {filteredEmployees.map(emp => (
//                                                     <option key={emp.employeeIdentity} value={emp.employeeIdentity}>
//                                                         {emp.firstName} {emp.lastName} - {emp.employeeIdentity}
//                                                     </option>
//                                                 ))}
//                                             </select>
//                                         </div>

//                                         {selectedEmployee && (
//                                             <>
//                                                 <div className="mt-4">
//                                                     <label className="block text-sm font-medium text-gray-700">Select Month you are paying for</label>
//                                                     <select
//                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                         value={selectedMonth}
//                                                         onChange={handleMonthChange}
//                                                     >
//                                                         {Array.from({ length: 12 }, (_, i) => (
//                                                             <option key={i + 1} value={i + 1}>
//                                                                 {new Date(0, i).toLocaleString('default', { month: 'long' })}
//                                                             </option>
//                                                         ))}
//                                                     </select>
//                                                 </div>

//                                                 <div className="mt-4">
//                                                     <label className="block text-sm font-medium text-gray-700">Year</label>
//                                                     <select
//                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                         value={selectedYear}
//                                                         onChange={handleYearChange}
//                                                     >
//                                                         {Array.from({ length: 5 }, (_, i) => (
//                                                             <option key={i} value={new Date().getFullYear() - i}>
//                                                                 {new Date().getFullYear() - i}
//                                                             </option>
//                                                         ))}
//                                                     </select>
//                                                 </div>

//                                                 <div className="mt-4">
//                                                     <label className="block text-sm font-medium text-gray-700">Salary</label>
//                                                     <input
//                                                         type="text"
//                                                         readOnly
//                                                         value={selectedEmployee.salary}
//                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                     />
//                                                 </div>

//                                                 <div className="mt-4">
//                                                     <label className="block text-sm font-medium text-gray-700">Attendance Days</label>
//                                                     <input
//                                                         type="text"
//                                                         readOnly
//                                                         value={attendanceDays}
//                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                     />
//                                                 </div>

//                                                 <div className="mt-4">
//                                                     <label className="block text-sm font-medium text-gray-700">Day Rate</label>
//                                                     <input
//                                                         type="text"
//                                                         readOnly
//                                                         value={dayRate}
//                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                     />
//                                                 </div>

//                                                 <div className="mt-4">
//                                                     <label className="block text-sm font-medium text-gray-700">Tax</label>
//                                                     <input
//                                                         type="text"
//                                                         readOnly
//                                                         value={tax}
//                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                     />
//                                                 </div>

//                                                 <div className="mt-4">
//                                                     <label className="block text-sm font-medium text-gray-700">Total Payment</label>
//                                                     <input
//                                                         type="text"
//                                                         readOnly
//                                                         value={totalPayment}
//                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                     />
//                                                 </div>
//                                             </>
//                                         )}
//                                     </div>
//                                 )}

//                                 {/* Step 2 */}
//                                 {step === 2 && (
//                                     <div>
//                                         <div className="mt-4">
//                                             <label className="block text-sm font-medium text-gray-700">Bonus Payment</label>
//                                             <input
//                                                 type="text"
//                                                 value={bonusPayment}
//                                                 onChange={(e) => setBonusPayment(e.target.value)}
//                                                 className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                             />
//                                         </div>

//                                         <div className="mt-4">
//                                             <label className="block text-sm font-medium text-gray-700">Insurance</label>
//                                             <input
//                                                 type="text"
//                                                 value={insurance}
//                                                 onChange={(e) => setInsurance(e.target.value)}
//                                                 className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                             />
//                                         </div>

//                                         <div className="mt-4">
//                                             <label className="block text-sm font-medium text-gray-700">Loan Deductions</label>
//                                             <div>
//                                                 <label className="block text-sm font-medium text-gray-700">Social Welfare</label>
//                                                 <input
//                                                     type="text"
//                                                     value={loanDeductions.social_welfare}
//                                                     onChange={(e) => setLoanDeductions({ ...loanDeductions, social_welfare: e.target.value })}
//                                                     className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                 />
//                                             </div>

//                                             <div className="mt-4">
//                                                 <label className="block text-sm font-medium text-gray-700">Salary Advance</label>
//                                                 <input
//                                                     type="text"
//                                                     value={loanDeductions.salary_advance}
//                                                     onChange={(e) => setLoanDeductions({ ...loanDeductions, salary_advance: e.target.value })}
//                                                     className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                 />
//                                             </div>

//                                             <div className="mt-4">
//                                                 <label className="block text-sm font-medium text-gray-700">Medical</label>
//                                                 <input
//                                                     type="text"
//                                                     value={loanDeductions.medical}
//                                                     onChange={(e) => setLoanDeductions({ ...loanDeductions, medical: e.target.value })}
//                                                     className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                 />
//                                             </div>

//                                             {loanDeductions.extra.map((extra, index) => (
//                                                 <div key={index} className="mt-4">
//                                                     <label className="block text-sm font-medium text-gray-700">Loan Type</label>
//                                                     <input
//                                                         type="text"
//                                                         value={extra.type}
//                                                         onChange={(e) => handleLoanChange(index, 'type', e.target.value)}
//                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                     />

//                                                     <label className="block text-sm font-medium text-gray-700 mt-2">Amount</label>
//                                                     <input
//                                                         type="text"
//                                                         value={extra.amount}
//                                                         onChange={(e) => handleLoanChange(index, 'amount', e.target.value)}
//                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                     />
//                                                 </div>
//                                             ))}

//                                             <button
//                                                 type="button"
//                                                 onClick={addExtraLoan}
//                                                 className="mt-4 inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
//                                             >
//                                                 Add Extra Loan
//                                             </button>
//                                         </div>
//                                     </div>
//                                 )}

//                                 {/* Step 3 */}
//                                 {step === 3 && (
//                                     <div>
//                                         <div className="mt-4">
//                                             <label className="block text-sm font-medium text-gray-700">Tax</label>
//                                             <input
//                                                 type="text"
//                                                 readOnly
//                                                 value={tax}
//                                                 className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                             />
//                                         </div>

//                                         <div className="mt-4">
//                                             <label className="block text-sm font-medium text-gray-700">Pension Deduction</label>
//                                             <input
//                                                 type="text"
//                                                 readOnly
//                                                 value={pensionDeduction}
//                                                 className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                             />
//                                         </div>

//                                         <div className="mt-4">
//                                             <label className="block text-sm font-medium text-gray-700">Final Amount</label>
//                                             <input
//                                                 type="text"
//                                                 readOnly
//                                                 value={finalAmount}
//                                                 className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                             />
//                                         </div>
//                                     </div>
//                                 )}

//                                 <div className="mt-4 flex justify-between">
//                                     {step > 1 && (
//                                         <button
//                                             type="button"
//                                             onClick={handlePreviousStep}
//                                             className="inline-flex justify-center rounded-md border border-transparent bg-gray-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
//                                         >
//                                             Previous
//                                         </button>
//                                     )}

//                                     {step < 3 && selectedEmployee && (
//                                         <button
//                                             type="button"
//                                             onClick={handleNextStep}
//                                             className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
//                                         >
//                                             Next
//                                         </button>
//                                     )}

//                                     {step === 3 && (
//                                         <button
//                                             type="button"
//                                             onClick={handleSubmit}
//                                             className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
//                                             disabled={loading}
//                                         >
//                                             {payment ? 'Update Payment' : 'Create Payment'}
//                                         </button>
//                                     )}
//                                 </div>
//                             </Dialog.Panel>
//                         </Transition.Child>
//                     </div>
//                 </div>
//             </Dialog>
//         </Transition>
//     );
// }




// import React, { useState, useEffect } from 'react';
// import { Employee } from '@/app/types/employee';
// import { Payment } from '@/app/types/payment';
// import { calculateTax, calculateWorkingDays, calculateLoanDeductions } from '@/lib/taxCalculation';
// import { Dialog, Transition } from '@headlessui/react';
// import { Fragment } from 'react';

// interface PaymentEditOverlayProps {
//     isOpen: boolean;
//     onClose: () => void;
//     onSubmit: (id: string, data: Partial<Payment>) => void;
//     userRole: string;
//     employees: Employee[];
//     payment?: Payment | null;
// }

// export default function PaymentEditOverlay({
//     isOpen,
//     onClose,
//     onSubmit,
//     userRole,
//     employees,
//     payment,
// }: PaymentEditOverlayProps) {
//     const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
//     const [attendanceDays, setAttendanceDays] = useState(0);
//     const [dayRate, setDayRate] = useState('0');
//     const [totalPayment, setTotalPayment] = useState('0');
//     const [tax, setTax] = useState('0');
//     const [bonusPayment, setBonusPayment] = useState('0');
//     const [insurance, setInsurance] = useState('0');
//     const [loanDeductions, setLoanDeductions] = useState({
//         social_welfare: '0',
//         salary_advance: '0',
//         medical: '0',
//         extra: [{ type: '', amount: '0' }]
//     });
//     const [pensionDeduction, setPensionDeduction] = useState('0');
//     const [finalAmount, setFinalAmount] = useState('0');
//     const [loading, setLoading] = useState(false);
//     const [step, setStep] = useState(1);
//     const [employeeSearch, setEmployeeSearch] = useState('');
//     const [selectedMonth, setSelectedMonth] = useState<number>(new Date().getMonth() + 1);
//     const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());

//     useEffect(() => {
//         if (selectedEmployee) {
//             const fetchAttendance = async () => {
//                 setLoading(true);
//                 try {
//                     const response = await fetch(`/api/attendance/getAttendanceByEmployee`, {
//                         method: 'POST',
//                         headers: {
//                             'Content-Type': 'application/json',
//                         },
//                         body: JSON.stringify({ employeeIdentity: selectedEmployee.employeeIdentity, month: selectedMonth, year: selectedYear }),
//                     });

//                     if (!response.ok) {
//                         throw new Error('Failed to fetch attendance records');
//                     }

//                     const { attendances } = await response.json();
//                     const attendanceCount = attendances.length;
//                     setAttendanceDays(attendanceCount);

//                     const workingDays = calculateWorkingDays(selectedMonth, selectedYear);
//                     const dayRateValue = (parseFloat(selectedEmployee.salary) / workingDays).toFixed(2);
//                     setDayRate(dayRateValue);

//                     const totalPaymentValue = (parseFloat(dayRateValue) * attendanceCount).toFixed(2);
//                     setTotalPayment(totalPaymentValue);
//                 } catch (error) {
//                     console.error(error);
//                 } finally {
//                     setLoading(false);
//                 }
//             };

//             fetchAttendance();
//             const taxValue = calculateTax(parseFloat(selectedEmployee.salary));
//             setTax(taxValue.toFixed(2));
//         }
//     }, [selectedEmployee, selectedMonth, selectedYear]);

//     useEffect(() => {
//         if (step === 3) {
//             const pension = (parseFloat(totalPayment) * 0.07).toFixed(2);
//             setPensionDeduction(pension);

//             const loanDeductionsTotal = calculateLoanDeductions(loanDeductions);
//             const finalAmountValue = (
//                 parseFloat(totalPayment) +
//                 parseFloat(bonusPayment) -
//                 parseFloat(tax) -
//                 loanDeductionsTotal -
//                 parseFloat(insurance) -
//                 parseFloat(pension)
//             ).toFixed(2);
//             setFinalAmount(finalAmountValue);
//         }
//     }, [step, totalPayment, bonusPayment, insurance, loanDeductions, tax]);

//     const handleEmployeeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
//         const employeeIdentity = e.target.value;
//         const employee = employees.find(emp => emp.employeeIdentity === employeeIdentity) || null;
//         setSelectedEmployee(employee);
//     };

//     const handleMonthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
//         setSelectedMonth(parseInt(e.target.value));
//     };

//     const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
//         setSelectedYear(parseInt(e.target.value));
//     };

//     const handleNextStep = () => {
//         if (step < 3) {
//             setStep(step + 1);
//         }
//     };

//     const handlePreviousStep = () => {
//         if (step > 1) {
//             setStep(step - 1);
//         }
//     };

//     const handleSubmit = () => {
//         if (selectedEmployee) {
//             const paymentData: Partial<Payment> = {
//                 amount: totalPayment,
//                 bonus_payment: bonusPayment,
//                 status: 'pending',
//                 employee: selectedEmployee._id,
//                 tax,
//                 loan_deduction: loanDeductions,
//                 insurance,
//                 pension_deduction: pensionDeduction,
//                 final_amount: finalAmount,
//                 day_rate: dayRate,
//                 working_days: calculateWorkingDays(selectedMonth, selectedYear).toString(),
//                 attendance_days: attendanceDays.toString(),
//                 employeeIdentity: selectedEmployee.employeeIdentity,
//                 month: selectedMonth,
//                 year: selectedYear,
//             };

//             onSubmit(payment?._id || '', paymentData);
//             onClose();
//         }
//     };

//     const handleLoanChange = (index: number, field: 'type' | 'amount', value: string) => {
//         const newLoanDeductions = { ...loanDeductions };
//         newLoanDeductions.extra[index][field] = value;
//         setLoanDeductions(newLoanDeductions);
//     };

//     const addExtraLoan = () => {
//         setLoanDeductions({
//             ...loanDeductions,
//             extra: [...loanDeductions.extra, { type: '', amount: '0' }]
//         });
//     };

//     const filteredEmployees = employees.filter(
//         emp =>
//             emp.firstName.toLowerCase().includes(employeeSearch.toLowerCase()) ||
//             emp.lastName.toLowerCase().includes(employeeSearch.toLowerCase()) ||
//             emp.employeeIdentity.toLowerCase().includes(employeeSearch.toLowerCase())
//     );

//     return (
//         <Transition appear show={isOpen} as={Fragment}>
//             <Dialog as="div" className="relative z-10" onClose={onClose}>
//                 <Transition.Child
//                     as={Fragment}
//                     enter="ease-out duration-300"
//                     enterFrom="opacity-0"
//                     enterTo="opacity-100"
//                     leave="ease-in duration-200"
//                     leaveFrom="opacity-100"
//                     leaveTo="opacity-0"
//                 >
//                     <div className="fixed inset-0 bg-black bg-opacity-25" />
//                 </Transition.Child>

//                 <div className="fixed inset-0 overflow-y-auto">
//                     <div className="flex min-h-full items-center justify-center p-4 text-center">
//                         <Transition.Child
//                             as={Fragment}
//                             enter="ease-out duration-300"
//                             enterFrom="opacity-0 scale-95"
//                             enterTo="opacity-100 scale-100"
//                             leave="ease-in duration-200"
//                             leaveFrom="opacity-100 scale-100"
//                             leaveTo="opacity-0 scale-95"
//                         >
//                             <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
//                                 <Dialog.Title
//                                     as="h3"
//                                     className="text-lg font-medium leading-6 text-gray-900"
//                                 >
//                                     {payment ? 'Edit Payment' : 'Create Payment'}
//                                 </Dialog.Title>

//                                 {step === 1 && (
//                                     <div>
//                                         <div className="relative mt-4">
//                                             <div className="mt-2 mb-2">
//                                                 <input
//                                                     type="text"
//                                                     placeholder="Search Employee"
//                                                     className="w-full px-3 py-2 border rounded-md focus:ring-indigo-500 focus:border-indigo-500"
//                                                     value={employeeSearch}
//                                                     onChange={(e) => setEmployeeSearch(e.target.value)}
//                                                 />
//                                             </div>

//                                             <select
//                                                 className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                 value={selectedEmployee?.employeeIdentity || ""}
//                                                 onChange={handleEmployeeChange}
//                                             >
//                                                 <option value="" disabled>Select Employee</option>
//                                                 {filteredEmployees.map(emp => (
//                                                     <option key={emp.employeeIdentity} value={emp.employeeIdentity}>
//                                                         {emp.firstName} {emp.lastName} - {emp.employeeIdentity}
//                                                     </option>
//                                                 ))}
//                                             </select>
//                                         </div>

//                                         {selectedEmployee && (
//                                             <>
//                                                 <div className="mt-4">
//                                                     <label className="block text-sm font-medium text-gray-700">Select Month you are paying for</label>
//                                                     <select
//                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                         value={selectedMonth}
//                                                         onChange={handleMonthChange}
//                                                     >
//                                                         {Array.from({ length: 12 }, (_, i) => (
//                                                             <option key={i + 1} value={i + 1}>
//                                                                 {new Date(0, i).toLocaleString('default', { month: 'long' })}
//                                                             </option>
//                                                         ))}
//                                                     </select>
//                                                 </div>

//                                                 <div className="mt-4">
//                                                     <label className="block text-sm font-medium text-gray-700">Year</label>
//                                                     <select
//                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                         value={selectedYear}
//                                                         onChange={handleYearChange}
//                                                     >
//                                                         {Array.from({ length: 5 }, (_, i) => (
//                                                             <option key={i} value={new Date().getFullYear() - i}>
//                                                                 {new Date().getFullYear() - i}
//                                                             </option>
//                                                         ))}
//                                                     </select>
//                                                 </div>

//                                                 <div className="mt-4">
//                                                     <label className="block text-sm font-medium text-gray-700">Salary</label>
//                                                     <input
//                                                         type="text"
//                                                         readOnly
//                                                         value={selectedEmployee.salary}
//                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                     />
//                                                 </div>

//                                                 <div className="mt-4">
//                                                     <label className="block text-sm font-medium text-gray-700">Attendance Days</label>
//                                                     <input
//                                                         type="text"
//                                                         readOnly
//                                                         value={attendanceDays}
//                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                     />
//                                                 </div>

//                                                 <div className="mt-4">
//                                                     <label className="block text-sm font-medium text-gray-700">Day Rate</label>
//                                                     <input
//                                                         type="text"
//                                                         readOnly
//                                                         value={dayRate}
//                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                     />
//                                                 </div>

//                                                 <div className="mt-4">
//                                                     <label className="block text-sm font-medium text-gray-700">Tax</label>
//                                                     <input
//                                                         type="text"
//                                                         readOnly
//                                                         value={tax}
//                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                     />
//                                                 </div>

//                                                 <div className="mt-4">
//                                                     <label className="block text-sm font-medium text-gray-700">Total Payment</label>
//                                                     <input
//                                                         type="text"
//                                                         readOnly
//                                                         value={totalPayment}
//                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                     />
//                                                 </div>
//                                             </>
//                                         )}
//                                     </div>
//                                 )}

//                                 {/* Step 2 */}
//                                 {step === 2 && (
//                                     <div>
//                                         <div className="mt-4">
//                                             <label className="block text-sm font-medium text-gray-700">Bonus Payment</label>
//                                             <input
//                                                 type="text"
//                                                 value={bonusPayment}
//                                                 onChange={(e) => setBonusPayment(e.target.value)}
//                                                 className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                             />
//                                         </div>

//                                         <div className="mt-4">
//                                             <label className="block text-sm font-medium text-gray-700">Insurance</label>
//                                             <input
//                                                 type="text"
//                                                 value={insurance}
//                                                 onChange={(e) => setInsurance(e.target.value)}
//                                                 className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                             />
//                                         </div>

//                                         <div className="mt-4">
//                                             <label className="block text-sm font-medium text-gray-700">Loan Deductions</label>
//                                             <div>
//                                                 <label className="block text-sm font-medium text-gray-700">Social Welfare</label>
//                                                 <input
//                                                     type="text"
//                                                     value={loanDeductions.social_welfare}
//                                                     onChange={(e) => setLoanDeductions({ ...loanDeductions, social_welfare: e.target.value })}
//                                                     className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                 />
//                                             </div>

//                                             <div className="mt-4">
//                                                 <label className="block text-sm font-medium text-gray-700">Salary Advance</label>
//                                                 <input
//                                                     type="text"
//                                                     value={loanDeductions.salary_advance}
//                                                     onChange={(e) => setLoanDeductions({ ...loanDeductions, salary_advance: e.target.value })}
//                                                     className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                 />
//                                             </div>

//                                             <div className="mt-4">
//                                                 <label className="block text-sm font-medium text-gray-700">Medical</label>
//                                                 <input
//                                                     type="text"
//                                                     value={loanDeductions.medical}
//                                                     onChange={(e) => setLoanDeductions({ ...loanDeductions, medical: e.target.value })}
//                                                     className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                 />
//                                             </div>

//                                             {loanDeductions.extra.map((extra, index) => (
//                                                 <div key={index} className="mt-4">
//                                                     <label className="block text-sm font-medium text-gray-700">Loan Type</label>
//                                                     <input
//                                                         type="text"
//                                                         value={extra.type}
//                                                         onChange={(e) => handleLoanChange(index, 'type', e.target.value)}
//                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                     />

//                                                     <label className="block text-sm font-medium text-gray-700 mt-2">Amount</label>
//                                                     <input
//                                                         type="text"
//                                                         value={extra.amount}
//                                                         onChange={(e) => handleLoanChange(index, 'amount', e.target.value)}
//                                                         className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                                     />
//                                                 </div>
//                                             ))}

//                                             <button
//                                                 type="button"
//                                                 onClick={addExtraLoan}
//                                                 className="mt-4 inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
//                                             >
//                                                 Add Extra Loan
//                                             </button>
//                                         </div>
//                                     </div>
//                                 )}

//                                 {/* Step 3 */}
//                                 {step === 3 && (
//                                     <div>
//                                         <div className="mt-4">
//                                             <label className="block text-sm font-medium text-gray-700">Tax</label>
//                                             <input
//                                                 type="text"
//                                                 readOnly
//                                                 value={tax}
//                                                 className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                             />
//                                         </div>

//                                         <div className="mt-4">
//                                             <label className="block text-sm font-medium text-gray-700">Pension Deduction</label>
//                                             <input
//                                                 type="text"
//                                                 readOnly
//                                                 value={pensionDeduction}
//                                                 className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                             />
//                                         </div>

//                                         <div className="mt-4">
//                                             <label className="block text-sm font-medium text-gray-700">Final Amount</label>
//                                             <input
//                                                 type="text"
//                                                 readOnly
//                                                 value={finalAmount}
//                                                 className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
//                                             />
//                                         </div>
//                                     </div>
//                                 )}

//                                 <div className="mt-4 flex justify-between">
//                                     {step > 1 && (
//                                         <button
//                                             type="button"
//                                             onClick={handlePreviousStep}
//                                             className="inline-flex justify-center rounded-md border border-transparent bg-gray-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
//                                         >
//                                             Previous
//                                         </button>
//                                     )}

//                                     {step < 3 && selectedEmployee && (
//                                         <button
//                                             type="button"
//                                             onClick={handleNextStep}
//                                             className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
//                                         >
//                                             Next
//                                         </button>
//                                     )}

//                                     {step === 3 && (
//                                         <button
//                                             type="button"
//                                             onClick={handleSubmit}
//                                             className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
//                                             disabled={loading}
//                                         >
//                                             {payment ? 'Update Payment' : 'Create Payment'}
//                                         </button>
//                                     )}
//                                 </div>
//                             </Dialog.Panel>
//                         </Transition.Child>
//                     </div>
//                 </div>
//             </Dialog>
//         </Transition>
//     );
// }












import React, { useState, useEffect } from 'react';
import { Employee } from '@/app/types/employee';
import { Payment } from '@/app/types/payment';
import { calculateTax, calculateWorkingDays, calculateLoanDeductions } from '@/lib/taxCalculation';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';

interface PaymentEditOverlayProps {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (id: string, data: Partial<Payment>) => void;
    userRole: string;
    employees: Employee[];
    payment?: Payment | null;
}

export default function PaymentEditOverlay({
    isOpen,
    onClose,
    onSubmit,
    userRole,
    employees,
    payment,
}: PaymentEditOverlayProps) {
    const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
    const [attendanceDays, setAttendanceDays] = useState(0);
    const [dayRate, setDayRate] = useState('0');
    const [totalPayment, setTotalPayment] = useState('0');
    const [tax, setTax] = useState('0');
    const [bonusPayment, setBonusPayment] = useState('0');
    const [insurance, setInsurance] = useState('0');
    const [loanDeductions, setLoanDeductions] = useState({
        social_welfare: '0',
        salary_advance: '0',
        medical: '0',
        extra: [{ type: '', amount: '0' }]
    });
    const [pensionDeduction, setPensionDeduction] = useState('0');
    const [finalAmount, setFinalAmount] = useState('0');
    const [status, setStatus] = useState<Payment['status']>('pending'); // Default to 'pending'
    const [loading, setLoading] = useState(false);
    const [step, setStep] = useState(1);
    const [employeeSearch, setEmployeeSearch] = useState('');
    const [selectedMonth, setSelectedMonth] = useState<number>(new Date().getMonth() + 1);
    const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());

    useEffect(() => {
        if (selectedEmployee) {
            const fetchAttendance = async () => {
                setLoading(true);
                try {
                    const response = await fetch(`/api/attendance/getAttendanceByEmployee`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ employeeIdentity: selectedEmployee.employeeIdentity, month: selectedMonth, year: selectedYear }),
                    });

                    if (!response.ok) {
                        throw new Error('Failed to fetch attendance records');
                    }

                    const { attendances } = await response.json();
                    const attendanceCount = attendances.length;
                    setAttendanceDays(attendanceCount);

                    const workingDays = calculateWorkingDays(selectedMonth, selectedYear);
                    const dayRateValue = (parseFloat(selectedEmployee.salary) / workingDays).toFixed(2);
                    setDayRate(dayRateValue);

                    const totalPaymentValue = (parseFloat(dayRateValue) * attendanceCount).toFixed(2);
                    setTotalPayment(totalPaymentValue);
                } catch (error) {
                    console.error(error);
                } finally {
                    setLoading(false);
                }
            };

            fetchAttendance();
            const taxValue = calculateTax(parseFloat(selectedEmployee.salary));
            setTax(taxValue.toFixed(2));
        }
    }, [selectedEmployee, selectedMonth, selectedYear]);

    useEffect(() => {
        if (step === 3) {
            const pension = (parseFloat(totalPayment) * 0.07).toFixed(2);
            setPensionDeduction(pension);

            const loanDeductionsTotal = calculateLoanDeductions(loanDeductions);
            const finalAmountValue = (
                parseFloat(totalPayment) +
                parseFloat(bonusPayment) -
                parseFloat(tax) -
                loanDeductionsTotal -
                parseFloat(insurance) -
                parseFloat(pension)
            ).toFixed(2);
            setFinalAmount(finalAmountValue);
        }
    }, [step, totalPayment, bonusPayment, insurance, loanDeductions, tax]);

    const handleEmployeeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        const employeeIdentity = e.target.value;
        const employee = employees.find(emp => emp.employeeIdentity === employeeIdentity) || null;
        setSelectedEmployee(employee);
    };

    const handleMonthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setSelectedMonth(parseInt(e.target.value));
    };

    const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setSelectedYear(parseInt(e.target.value));
    };

    const handleNextStep = () => {
        if (step < 3) {
            setStep(step + 1);
        }
    };

    const handlePreviousStep = () => {
        if (step > 1) {
            setStep(step - 1);
        }
    };

    const handleSubmit = () => {
        if (selectedEmployee) {
            const paymentData: Partial<Payment> = {
                amount: totalPayment,
                bonus_payment: bonusPayment,
                status, // Include status
                employee: selectedEmployee._id,
                tax,
                loan_deduction: loanDeductions,
                insurance,
                pension_deduction: pensionDeduction,
                final_amount: finalAmount,
                day_rate: dayRate,
                working_days: calculateWorkingDays(selectedMonth, selectedYear).toString(),
                attendance_days: attendanceDays.toString(),
                employeeIdentity: selectedEmployee.employeeIdentity,
                month: selectedMonth,
                year: selectedYear,
            };

            onSubmit(payment?._id || '', paymentData);
            onClose();
        }
    };

    const handleLoanChange = (index: number, field: 'type' | 'amount', value: string) => {
        const newLoanDeductions = { ...loanDeductions };
        newLoanDeductions.extra[index][field] = value;
        setLoanDeductions(newLoanDeductions);
    };

    const addExtraLoan = () => {
        setLoanDeductions({
            ...loanDeductions,
            extra: [...loanDeductions.extra, { type: '', amount: '0' }]
        });
    };

    const filteredEmployees = employees.filter(
        emp =>
            emp.firstName.toLowerCase().includes(employeeSearch.toLowerCase()) ||
            emp.lastName.toLowerCase().includes(employeeSearch.toLowerCase()) ||
            emp.employeeIdentity.toLowerCase().includes(employeeSearch.toLowerCase())
    );

    return (
        <Transition appear show={isOpen} as={Fragment}>
            <Dialog as="div" className="relative z-10" onClose={onClose}>
                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="fixed inset-0 bg-black bg-opacity-25" />
                </Transition.Child>

                <div className="fixed inset-0 overflow-y-auto">
                    <div className="flex min-h-full items-center justify-center p-4 text-center">
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 scale-95"
                            enterTo="opacity-100 scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 scale-100"
                            leaveTo="opacity-0 scale-95"
                        >
                            <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                <Dialog.Title
                                    as="h3"
                                    className="text-lg font-medium leading-6 text-gray-900"
                                >
                                    {payment ? 'Edit Payment' : 'Create Payment'}
                                </Dialog.Title>

                                {step === 1 && (
                                    <div>
                                        <div className="relative mt-4">
                                            <div className="mt-2 mb-2">
                                                <input
                                                    type="text"
                                                    placeholder="Search Employee"
                                                    className="w-full px-3 py-2 border rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                                                    value={employeeSearch}
                                                    onChange={(e) => setEmployeeSearch(e.target.value)}
                                                />
                                            </div>

                                            <select
                                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                value={selectedEmployee?.employeeIdentity || ""}
                                                onChange={handleEmployeeChange}
                                            >
                                                <option value="" disabled>Select Employee</option>
                                                {filteredEmployees.map(emp => (
                                                    <option key={emp.employeeIdentity} value={emp.employeeIdentity}>
                                                        {emp.firstName} {emp.lastName} - {emp.employeeIdentity}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>

                                        {selectedEmployee && (
                                            <>
                                                <div className="mt-4">
                                                    <label className="block text-sm font-medium text-gray-700">Month</label>
                                                    <select
                                                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                        value={selectedMonth}
                                                        onChange={handleMonthChange}
                                                    >
                                                        {Array.from({ length: 12 }, (_, i) => (
                                                            <option key={i + 1} value={i + 1}>
                                                                {new Date(0, i).toLocaleString('default', { month: 'long' })}
                                                            </option>
                                                        ))}
                                                    </select>
                                                </div>

                                                <div className="mt-4">
                                                    <label className="block text-sm font-medium text-gray-700">Year</label>
                                                    <select
                                                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                        value={selectedYear}
                                                        onChange={handleYearChange}
                                                    >
                                                        {Array.from({ length: 5 }, (_, i) => (
                                                            <option key={i} value={new Date().getFullYear() - i}>
                                                                {new Date().getFullYear() - i}
                                                            </option>
                                                        ))}
                                                    </select>
                                                </div>

                                                {/* Status Dropdown for admin or ceoadmin */}
                                                {['admin', 'ceoadmin'].includes(userRole) && (
                                                    <div className="mt-4">
                                                        <label className="block text-sm font-medium text-gray-700">Status</label>
                                                        <select
                                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                            value={status}
                                                            onChange={(e) => setStatus(e.target.value as Payment['status'])}
                                                        >
                                                            <option value="pending">Pending</option>
                                                            <option value="revised">Revised</option>
                                                            <option value="review">Review</option>
                                                            <option value="approved">Approved</option>
                                                            <option value="processing">Processing</option>
                                                            <option value="failed">Failed</option>
                                                            <option value="cancel">Cancel</option>
                                                        </select>
                                                    </div>
                                                )}

                                                {/* Other Fields */}
                                                <div className="mt-4">
                                                    <label className="block text-sm font-medium text-gray-700">Attendance Days</label>
                                                    <input
                                                        type="text"
                                                        readOnly
                                                        value={attendanceDays}
                                                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                    />
                                                </div>

                                                <div className="mt-4">
                                                    <label className="block text-sm font-medium text-gray-700">Day Rate</label>
                                                    <input
                                                        type="text"
                                                        readOnly
                                                        value={dayRate}
                                                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                    />
                                                </div>

                                                <div className="mt-4">
                                                    <label className="block text-sm font-medium text-gray-700">Tax</label>
                                                    <input
                                                        type="text"
                                                        readOnly
                                                        value={tax}
                                                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                    />
                                                </div>

                                                <div className="mt-4">
                                                    <label className="block text-sm font-medium text-gray-700">Total Payment</label>
                                                    <input
                                                        type="text"
                                                        readOnly
                                                        value={totalPayment}
                                                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                    />
                                                </div>
                                            </>
                                        )}
                                    </div>
                                )}

                                {/* Step 2 */}
                                {step === 2 && (
                                    <div>
                                        <div className="mt-4">
                                            <label className="block text-sm font-medium text-gray-700">Bonus Payment</label>
                                            <input
                                                type="text"
                                                value={bonusPayment}
                                                onChange={(e) => setBonusPayment(e.target.value)}
                                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                            />
                                        </div>

                                        <div className="mt-4">
                                            <label className="block text-sm font-medium text-gray-700">Insurance</label>
                                            <input
                                                type="text"
                                                value={insurance}
                                                onChange={(e) => setInsurance(e.target.value)}
                                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                            />
                                        </div>

                                        <div className="mt-4">
                                            <label className="block text-sm font-medium text-gray-700">Loan Deductions</label>
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700">Social Welfare</label>
                                                <input
                                                    type="text"
                                                    value={loanDeductions.social_welfare}
                                                    onChange={(e) => setLoanDeductions({ ...loanDeductions, social_welfare: e.target.value })}
                                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                />
                                            </div>

                                            <div className="mt-4">
                                                <label className="block text-sm font-medium text-gray-700">Salary Advance</label>
                                                <input
                                                    type="text"
                                                    value={loanDeductions.salary_advance}
                                                    onChange={(e) => setLoanDeductions({ ...loanDeductions, salary_advance: e.target.value })}
                                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                />
                                            </div>

                                            <div className="mt-4">
                                                <label className="block text-sm font-medium text-gray-700">Medical</label>
                                                <input
                                                    type="text"
                                                    value={loanDeductions.medical}
                                                    onChange={(e) => setLoanDeductions({ ...loanDeductions, medical: e.target.value })}
                                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                />
                                            </div>

                                            {loanDeductions.extra.map((extra, index) => (
                                                <div key={index} className="mt-4">
                                                    <label className="block text-sm font-medium text-gray-700">Loan Type</label>
                                                    <input
                                                        type="text"
                                                        value={extra.type}
                                                        onChange={(e) => handleLoanChange(index, 'type', e.target.value)}
                                                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                    />

                                                    <label className="block text-sm font-medium text-gray-700 mt-2">Amount</label>
                                                    <input
                                                        type="text"
                                                        value={extra.amount}
                                                        onChange={(e) => handleLoanChange(index, 'amount', e.target.value)}
                                                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                    />
                                                </div>
                                            ))}

                                            <button
                                                type="button"
                                                onClick={addExtraLoan}
                                                className="mt-4 inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                            >
                                                Add Extra Loan
                                            </button>
                                        </div>
                                    </div>
                                )}

                                {/* Step 3 */}
                                {step === 3 && (
                                    <div>
                                        <div className="mt-4">
                                            <label className="block text-sm font-medium text-gray-700">Tax</label>
                                            <input
                                                type="text"
                                                readOnly
                                                value={tax}
                                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                            />
                                        </div>

                                        <div className="mt-4">
                                            <label className="block text-sm font-medium text-gray-700">Pension Deduction</label>
                                            <input
                                                type="text"
                                                readOnly
                                                value={pensionDeduction}
                                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                            />
                                        </div>

                                        <div className="mt-4">
                                            <label className="block text-sm font-medium text-gray-700">Final Amount</label>
                                            <input
                                                type="text"
                                                readOnly
                                                value={finalAmount}
                                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                            />
                                        </div>
                                    </div>
                                )}

                                <div className="mt-4 flex justify-between">
                                    {step > 1 && (
                                        <button
                                            type="button"
                                            onClick={handlePreviousStep}
                                            className="inline-flex justify-center rounded-md border border-transparent bg-gray-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                        >
                                            Previous
                                        </button>
                                    )}

                                    {step < 3 && selectedEmployee && (
                                        <button
                                            type="button"
                                            onClick={handleNextStep}
                                            className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                        >
                                            Next
                                        </button>
                                    )}

                                    {step === 3 && (
                                        <button
                                            type="button"
                                            onClick={handleSubmit}
                                            className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                            disabled={loading}
                                        >
                                            {payment ? 'Update Payment' : 'Create Payment'}
                                        </button>
                                    )}
                                </div>
                            </Dialog.Panel>
                        </Transition.Child>
                    </div>
                </div>
            </Dialog>
        </Transition>
    );
}
