




"use client"

import * as React from "react"
import { useF<PERSON>, Form<PERSON>rovider, useFormContext, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@/components/ui/button"
import { Overlay } from "@/components/ui/overlay"
import { Payment } from "@/app/types/payment"
import { Employee } from "@/app/types/employee"
import DuplicatePaymentError from "@/components/payments/duplicate-payment-error"
import { calculateTax } from "@/lib/taxCalculation"
import { UserRole } from "@/context/types"
import { paymentSchema, PaymentFormValues } from "@/app/types/paymentSchema"
import { Input } from "../ui/input"
import { debounce } from "@/lib/debounce"

interface PaymentEditOverlayProps {
    isOpen: boolean;
    onClose: () => void;
    payment: Payment;
    onSubmit: (id: string, data: Partial<Payment>) => Promise<void>;
    employees: { _id: string; firstName: string; lastName: string; phone: string; salary: number }[];
    userRole: UserRole;
}


// Step1 form component
interface Step1Props {
    employees: { _id: string; firstName: string; lastName: string; phone: string; salary: number }[];
    payment: Payment;
}



const Step1: React.FC<Step1Props> = ({ employees, payment }) => {
    const methods = useFormContext<PaymentFormValues>();
    const { register, formState: { errors }, watch, setValue } = methods;

    const selectedEmployeeId = watch("employee");
    const selectedEmployee = employees.find(employee => employee._id === selectedEmployeeId);

    const debounceUpdate = React.useCallback(debounce(() => {
        if (selectedEmployee) {
            setValue("amount", selectedEmployee.salary.toString());
        }
    }, 2000), [selectedEmployee, setValue]);

    React.useEffect(() => {
        debounceUpdate();
    }, [selectedEmployee, debounceUpdate]);

    return (
        <div>
            <label htmlFor="employee" className="block text-sm font-medium">Employee</label>
            <select id="employee" defaultValue={typeof payment.employee === 'string' ? payment.employee : payment.employee._id} {...register('employee')}>
                {employees.map(emp => (
                    <option key={emp._id} value={emp._id}>{emp.firstName} {emp.lastName}</option>
                ))}
            </select>
            {errors.employee && <p className="text-red-600">{errors.employee.message}</p>}

            {selectedEmployee && (
                <>
                    <label htmlFor="amount" className="block text-sm font-medium">Amount</label>
                    <Input id="amount" type="text" {...register('amount')} readOnly />

                    <label htmlFor="bonus_payment" className="block text-sm font-medium">Bonus Payment</label>
                    <Input id="bonus_payment" type="text" {...register('bonus_payment')} />

                    <label htmlFor="tax" className="block text-sm font-medium">Tax</label>
                    <Input id="tax" type="text" {...register('tax')} readOnly />

                    <label htmlFor="pension_deduction" className="block text-sm font-medium">Pension Deduction</label>
                    <Input id="pension_deduction" type="text" {...register('pension_deduction')} readOnly />

                    <label htmlFor="insurance" className="block text-sm font-medium">Insurance</label>
                    <Input id="insurance" type="text" {...register('insurance')} />
                </>
            )}
        </div>
    );
};





interface Step2Props {
    control: any;
    register: any;
    fields: any;
    errors: any;
    append: any;
}

const Step2: React.FC<Step2Props> = ({ control, register, fields, errors, append }) => {
    const { watch, setValue } = useFormContext<PaymentFormValues>();
    const loanDeduction = watch("loan_deduction");
    const bonusPayment = parseFloat(watch("bonus_payment") || '0');
    const insurance = parseFloat(watch("insurance") || '0');
    const amount = parseFloat(watch("amount") || '0');

    const debounceUpdate = React.useCallback(debounce(() => {
        const amountWithBonus = amount + bonusPayment;
        const tax = calculateTax(amountWithBonus);
        const pensionDeduction = (amountWithBonus * 0.07).toFixed(2);
        const loanDeductionsTotal = Object.values(loanDeduction).reduce((total, value) => {
            if (typeof value === 'string') return total + parseFloat(value || '0');
            if (Array.isArray(value)) return total + value.reduce((subTotal, obj) => subTotal + parseFloat(obj.amount || '0'), 0);
            return total;
        }, 0);
        const finalAmount = (amountWithBonus - tax - loanDeductionsTotal - insurance - parseFloat(pensionDeduction)).toFixed(2);

        setValue("tax", tax.toString());
        setValue("pension_deduction", pensionDeduction);
        setValue("final_amount", finalAmount.toString());
    }, 2000), [amount, bonusPayment, loanDeduction, insurance, setValue]);

    React.useEffect(() => {
        debounceUpdate();
    }, [amount, bonusPayment, loanDeduction, insurance, debounceUpdate]);

    const addExtraLoan = () => append({ type: "", amount: "0" });

    return (
        <div>
            <div>
                <label className="block text-sm font-medium">Loan Deductions</label>
                <div>
                    <label htmlFor="social_welfare" className="block text-sm font-medium">Social Welfare</label>
                    <Input id="social_welfare" type="text" {...register('loan_deduction.social_welfare')} />
                </div>
                <div>
                    <label htmlFor="salary_advance" className="block text-sm font-medium">Salary Advance</label>
                    <Input id="salary_advance" type="text" {...register('loan_deduction.salary_advance')} />
                </div>
                <div>
                    <label htmlFor="medical" className="block text-sm font-medium">Medical</label>
                    <Input id="medical" type="text" {...register('loan_deduction.medical')} />
                </div>
                {fields.map((field: { id: React.Key | null | undefined }, index: any) => (
                    <div key={field.id}>
                        <label htmlFor={`loan_deduction.extra[${index}].type`} className="block text-sm font-medium">Loan Type</label>
                        <Input id={`loan_deduction.extra[${index}].type`} type="text" {...register(`loan_deduction.extra.${index}.type`)} />
                        <label htmlFor={`loan_deduction.extra[${index}].amount`} className="block text-sm font-medium">Amount</label>
                        <Input id={`loan_deduction.extra[${index}].amount`} type="text" {...register(`loan_deduction.extra.${index}.amount`)} />
                    </div>
                ))}
                <Button type="button" onClick={addExtraLoan}>Add Extra Loan</Button>
            </div>

            <label htmlFor="final_amount" className="block text-sm font-medium">Final Amount</label>
            <Input id="final_amount" type="text" {...register('final_amount')} readOnly />

            <label htmlFor="status" className="block text-sm font-medium">Status</label>
            <select id="status" defaultValue="" {...register('status')}>
                <option value="pending">Pending</option>
                <option value="revised">Revised</option>
                <option value="review">Review</option>
                <option value="approved">Approved</option>
                <option value="cancel">Cancel</option>
                <option value="processing">Processing</option>
            </select>
            {errors.status && <p className="text-red-600">{errors.status.message}</p>}
        </div>
    );
};

const PaymentEditOverlay: React.FC<PaymentEditOverlayProps> = ({ isOpen, onClose, payment, onSubmit, employees, userRole }) => {
    const methods = useForm<PaymentFormValues>({
        resolver: zodResolver(paymentSchema),
        defaultValues: {
            amount: payment.amount.toString(),
            bonus_payment: '0',
            status: payment.status,
            employee: typeof payment.employee === 'string' ? payment.employee : payment.employee._id,
            tax: payment.tax || '0',
            loan_deduction: {
                social_welfare: '0',
                salary_advance: '0',
                medical: '0',
                extra: []
            },
            insurance: payment.insurance || '0',
            pension_deduction: '0',
            final_amount: payment.final_amount || '0'
        }
    });

    const { handleSubmit, formState: { isSubmitting }, trigger } = methods;
    const [alertMessage, setAlertMessage] = React.useState<string | null>(null);
    const [step, setStep] = React.useState(1);
    const { fields, append } = useFieldArray({
        control: methods.control,
        name: "loan_deduction.extra"
    });

    const nextStep = async () => {
        const valid = await trigger(step === 1 ? ["employee", "amount", "bonus_payment", "tax", "pension_deduction", "insurance"] : ["loan_deduction", "final_amount", "status"]);
        if (valid) setStep(step => step + 1);
    };

    const prevStep = () => setStep(step => step - 1);

    const handleFormSubmit = async (data: PaymentFormValues) => {
        const amount = parseFloat(data.amount);
        const bonusPayment = parseFloat(data.bonus_payment);
        const tax = parseFloat(data.tax);
        const pensionDeduction = parseFloat(data.pension_deduction);
        const insurance = parseFloat(data.insurance);
        const loanDeductionsTotal = Object.values(data.loan_deduction).reduce((total, value) => {
            if (typeof value === 'string') return total + parseFloat(value || '0');
            if (Array.isArray(value)) return total + value.reduce((subTotal, obj) => subTotal + parseFloat(obj.amount || '0'), 0);
            return total;
        }, 0);
        const finalAmount = amount + bonusPayment - tax - loanDeductionsTotal - insurance - pensionDeduction;
        const updatedData = { ...data, amount: amount.toString(), tax: tax.toString(), final_amount: finalAmount.toString() };

        if (step === 2) {
            try {
                await onSubmit(payment._id, updatedData);
                onClose();
            } catch (error: any) {
                console.error("Error in handleFormSubmit:", error);
                if (error.response?.status === 409) {
                    setAlertMessage('Employee has already been paid within the last 28 days');
                } else {
                    setAlertMessage('An unexpected error occurred. Please try again.');
                }
            }
        } else {
            nextStep();
        }
    };

    return (
        <Overlay isOpen={isOpen} onClose={onClose}>
            {alertMessage && (
                <DuplicatePaymentError onClose={() => setAlertMessage(null)} />
            )}
            <FormProvider {...methods}>
                <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4 overflow-y-auto max-h-[80vh]">
                    {step === 1 && <Step1 employees={employees} payment={payment} />}
                    {step === 2 && <Step2 control={methods.control} register={methods.register} fields={fields} errors={methods.formState.errors} append={append} />}
                    <div className="flex justify-between">
                        {step > 1 && (
                            <Button type="button" onClick={prevStep}>
                                Previous
                            </Button>
                        )}
                        {step < 2 && (
                            <Button type="button" onClick={nextStep} disabled={isSubmitting}>
                                Next
                            </Button>
                        )}
                        {step === 2 && (
                            <Button type="submit" disabled={isSubmitting}>
                                Submit
                            </Button>
                        )}
                    </div>
                </form>
            </FormProvider>
        </Overlay>
    );
};

export default PaymentEditOverlay;
