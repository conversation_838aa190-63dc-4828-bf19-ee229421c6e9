

import { Payment } from "@/app/types/payment";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";

type ColumnDefWithAccessor<TData> = ColumnDef<TData> & { accessorKey?: string };

export const getColumns = (
  handleEditPayment: (payment: Payment) => void,
  handleDeletePayment: (paymentId: string) => void,
  userRole: string
): ColumnDef<Payment>[] => {
  const commonColumns: ColumnDefWithAccessor<Payment>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "_id",
      header: () => <div className="text-left">ID</div>,
      cell: ({ row }) => {
        const id = row.getValue("_id") as string;
        return <div className="text-left font-medium">{id}</div>;
      },
    },
    {
      accessorKey: "amount",
      header: () => <div className="text-right">Amount</div>,
      cell: ({ row }) => {
        const amount = row.getValue("amount") as string;
        const formatted = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "MWK",
        }).format(Number(amount));

        return <div className="text-right font-medium">{formatted}</div>;
      },
    },
    {
      accessorKey: "employee.firstName",
      header: () => <div className="text-left">First Name</div>,
      cell: ({ row }) => {
        const employee = row.original.employee as any;
        return <div className="text-left font-medium">{employee?.firstName || ""}</div>;
      },
    },
    {
      accessorKey: "employee.lastName",
      header: () => <div className="text-left">Last Name</div>,
      cell: ({ row }) => {
        const employee = row.original.employee as any;
        return <div className="text-left font-medium">{employee?.lastName || ""}</div>;
      },
    },
    {
      accessorKey: "status",
      header: () => <div className="text-left">Status</div>,
      cell: ({ row }) => {
        const status = row.getValue("status") as Payment["status"];
        let statusClass = "text-gray-600";

        if (status === "approved") statusClass = "text-green-600";
        else if (status === "processing") statusClass = "text-yellow-600";
        else if (status === "failed") statusClass = "text-red-600";
        else if (status === "pending") statusClass = "text-orange-600";
        else if (status === "revised") statusClass = "text-blue-600";
        else if (status === "review") statusClass = "text-purple-600";
        else if (status === "cancel") statusClass = "text-red-400";

        return <div className={`font-medium ${statusClass}`}>{status}</div>;
      },
    },
    {
      accessorKey: "final_amount",
      header: () => <div className="text-right">Final Amount</div>,
      cell: ({ row }) => {
        const finalAmount = row.getValue("final_amount") as string;
        const finalAmountNum = parseFloat(finalAmount);
        const formatted = !isNaN(finalAmountNum)
          ? new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "MWK",
          }).format(finalAmountNum)
          : "MWK 0.00";

        return <div className="text-right font-medium">{formatted}</div>;
      },
    },
    {
      id: "actions",
      header: () => <div className="text-left">Actions</div>,
      cell: ({ row }) => {
        const payment = row.original;
        return (
          <div className="flex space-x-2">
            {["admin", "ceoadmin", "accountant"].includes(userRole) && (
              <Button size="sm" onClick={() => handleEditPayment(payment)}>
                {userRole === "accountant" ? "View Details" : "Edit"}
              </Button>
            )}
            {["admin", "ceoadmin"].includes(userRole) && (
              <Button size="sm" variant="destructive" onClick={() => handleDeletePayment(payment._id)}>
                Delete
              </Button>
            )}
          </div>
        );
      },
    },
  ];

  if (userRole === "admin" || userRole === "ceoadmin") {
    return commonColumns;
  } else if (userRole === "accountant") {
    return commonColumns.filter(column => column.id !== "actions" || column.id === "actions");
  } else if (userRole === "hrmanager") {
    return commonColumns.filter(column => column.accessorKey && ["employee.firstName", "employee.lastName", "amount"].includes(column.accessorKey));
  } else {
    return commonColumns;
  }
};
