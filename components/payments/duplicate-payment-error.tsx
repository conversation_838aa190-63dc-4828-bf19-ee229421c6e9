// app/components/payments/duplicate-payment-error.tsx

"use client"

import React from 'react';
import { Overlay } from '@/components/ui/overlay';
import { Button } from '@/components/ui/button';

interface DuplicatePaymentErrorProps {
    onClose: () => void;
}

export default function DuplicatePaymentError({ onClose }: DuplicatePaymentErrorProps) {
    return (
        <Overlay isOpen={true} onClose={onClose}>
            <div className="p-4 bg-red-100 rounded-md shadow-md">
                <h2 className="text-xl font-bold text-red-800">Duplicate Payment Error</h2>
                <p className="mt-2 text-red-600">This employee has already been paid within the last 28 days. Please try again later.</p>
                <Button onClick={onClose} className="mt-4">Close</Button>
            </div>
        </Overlay>
    );
}
