// //components/payments/approvedpaymentDataTable.tsx

// import * as React from "react";
// import {
//     ColumnDef,
//     SortingState,
//     VisibilityState,
//     RowSelectionState,
//     flexRender,
//     getCoreRowModel,
//     getPaginationRowModel,
//     getSortedRowModel,
//     useReactTable,
// } from "@tanstack/react-table";
// import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
// import { Button } from "@/components/ui/button";
// import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
// import { Input } from "@/components/ui/input";
// import { Checkbox } from "@/components/ui/checkbox";
// import { UserRole } from "@/context/types";
// import { getColumns } from "./approvedpaymentscolumns";
// import { Payment } from '@/app/types/payment';

// interface DataTableProps<TData extends Payment> {
//     data: TData[];
//     userRole: UserRole;
//     onEdit: (payment: TData) => void;
//     onDelete: (paymentId: string) => void;
// }

// export function ApprovedpaymentDataTable<TData extends Payment>({
//     data,
//     userRole,
//     onEdit,
//     onDelete,
// }: DataTableProps<TData>) {
//     const handleDownloadPayslip = React.useCallback(async (paymentId: string) => {
//         try {
//             const response = await fetch(`/api/payments/downloadPayslip?paymentId=${paymentId}`);
//             if (!response.ok) {
//                 throw new Error("Failed to download payslip");
//             }

//             const blob = await response.blob();
//             const url = window.URL.createObjectURL(blob);
//             const link = document.createElement('a');
//             link.href = url;
//             link.setAttribute('download', `payslip_${paymentId}.pdf`);
//             document.body.appendChild(link);
//             link.click();
//             link.parentNode?.removeChild(link);
//         } catch (error) {
//             console.error("Error downloading payslip:", error);
//         }
//     }, []);

//     const columns = React.useMemo(() => getColumns(handleDownloadPayslip, userRole), [handleDownloadPayslip, userRole]);

//     const [sorting, setSorting] = React.useState<SortingState>([]);
//     const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
//     const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({});

//     const table = useReactTable({
//         data,
//         columns,
//         onSortingChange: setSorting,
//         getCoreRowModel: getCoreRowModel(),
//         getPaginationRowModel: getPaginationRowModel(),
//         getSortedRowModel: getSortedRowModel(),
//         state: {
//             sorting,
//             columnVisibility,
//             rowSelection,
//         },
//     });

//     return (
//         <div>
//             <div className="flex items-center py-4">
//                 <Input
//                     placeholder="Filter by first name..."
//                     value={(table.getColumn("employee.firstName")?.getFilterValue() as string) || ""}
//                     onChange={(event) => table.getColumn("employee.firstName")?.setFilterValue(event.target.value)}
//                     className="max-w-sm"
//                 />
//                 <DropdownMenu>
//                     <DropdownMenuTrigger asChild>
//                         <Button variant="outline" className="ml-auto">
//                             Columns
//                         </Button>
//                     </DropdownMenuTrigger>
//                     <DropdownMenuContent align="end">
//                         {table.getAllColumns().filter((column) => column.getCanHide()).map((column) => (
//                             <DropdownMenuCheckboxItem
//                                 key={column.id}
//                                 className="capitalize"
//                                 checked={column.getIsVisible()}
//                                 onCheckedChange={(value) => column.toggleVisibility(!!value)}
//                             >
//                                 {column.id}
//                             </DropdownMenuCheckboxItem>
//                         ))}
//                     </DropdownMenuContent>
//                 </DropdownMenu>
//             </div>
//             <div className="rounded-md border">
//                 <Table>
//                     <TableHeader>
//                         {table.getHeaderGroups().map((headerGroup) => (
//                             <TableRow key={headerGroup.id}>
//                                 {headerGroup.headers.map((header) => (
//                                     <TableHead key={header.id}>
//                                         {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
//                                     </TableHead>
//                                 ))}
//                             </TableRow>
//                         ))}
//                     </TableHeader>
//                     <TableBody>
//                         {table.getRowModel().rows?.length ? (
//                             table.getRowModel().rows.map((row) => (
//                                 <TableRow
//                                     key={row.id}
//                                     data-state={row.getIsSelected() && "selected"}
//                                 >
//                                     {row.getVisibleCells().map((cell) => (
//                                         <TableCell key={cell.id}>
//                                             {flexRender(cell.column.columnDef.cell, cell.getContext())}
//                                         </TableCell>
//                                     ))}
//                                 </TableRow>
//                             ))
//                         ) : (
//                             <TableRow>
//                                 <TableCell colSpan={columns.length} className="h-24 text-center">
//                                     No results.
//                                 </TableCell>
//                             </TableRow>
//                         )}
//                     </TableBody>
//                 </Table>
//             </div>
//             <div className="flex items-center justify-end space-x-2 py-4">
//                 <div className="text-sm text-muted-foreground">
//                     {table.getRowModel().rows.length} of {table.getFilteredRowModel().rows.length} row(s) selected.
//                 </div>
//                 <div className="flex-1"></div>
//                 <Button
//                     variant="outline"
//                     size="sm"
//                     onClick={() => table.previousPage()}
//                     disabled={!table.getCanPreviousPage()}
//                 >
//                     Previous
//                 </Button>
//                 <Button
//                     variant="outline"
//                     size="sm"
//                     onClick={() => table.nextPage()}
//                     disabled={!table.getCanNextPage()}
//                 >
//                     Next
//                 </Button>
//             </div>
//         </div>
//     );
// }







// import * as React from "react";
// import {
//     ColumnDef,
//     SortingState,
//     VisibilityState,
//     RowSelectionState,
//     flexRender,
//     getCoreRowModel,
//     getPaginationRowModel,
//     getSortedRowModel,
//     useReactTable,
// } from "@tanstack/react-table";
// import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
// import { Button } from "@/components/ui/button";
// import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
// import { Input } from "@/components/ui/input";
// import { Checkbox } from "@/components/ui/checkbox";
// import { UserRole } from "@/context/types";
// import { getColumns } from "./approvedpaymentscolumns";
// import { Payment } from '@/app/types/payment';
// import { Download, FileText } from 'lucide-react'; // Importing icons for the buttons

// interface DataTableProps<TData extends Payment> {
//     data: TData[];
//     userRole: UserRole;
//     onEdit: (payment: TData) => void;
//     onDelete: (paymentId: string) => void;
// }

// export function ApprovedpaymentDataTable<TData extends Payment>({
//     data,
//     userRole,
//     onEdit,
//     onDelete,
// }: DataTableProps<TData>) {
//     const handleDownloadPayslip = React.useCallback(async (paymentId: string) => {
//         try {
//             const response = await fetch(`/api/payments/downloadPayslip?paymentId=${paymentId}`);
//             if (!response.ok) {
//                 throw new Error("Failed to download payslip");
//             }

//             const blob = await response.blob();
//             const url = window.URL.createObjectURL(blob);
//             const link = document.createElement('a');
//             link.href = url;
//             link.setAttribute('download', `payslip_${paymentId}.pdf`);
//             document.body.appendChild(link);
//             link.click();
//             link.parentNode?.removeChild(link);
//         } catch (error) {
//             console.error("Error downloading payslip:", error);
//         }
//     }, []);

//     const handleDownloadExcel = React.useCallback(async (selectedColumns: string[]) => {
//         try {
//             const response = await fetch('/api/payments/downloadExcel', {
//                 method: 'POST',
//                 headers: {
//                     'Content-Type': 'application/json',
//                 },
//                 body: JSON.stringify({ selectedColumns, data }),
//             });
//             if (!response.ok) {
//                 throw new Error("Failed to download Excel report");
//             }

//             const blob = await response.blob();
//             const url = window.URL.createObjectURL(blob);
//             const link = document.createElement('a');
//             link.href = url;
//             link.setAttribute('download', 'report.xlsx');
//             document.body.appendChild(link);
//             link.click();
//             link.parentNode?.removeChild(link);
//         } catch (error) {
//             console.error("Error downloading Excel report:", error);
//         }
//     }, [data]);

//     const handleDownloadPdf = React.useCallback(async (selectedColumns: string[]) => {
//         try {
//             const response = await fetch('/api/payments/downloadPdf', {
//                 method: 'POST',
//                 headers: {
//                     'Content-Type': 'application/json',
//                 },
//                 body: JSON.stringify({ selectedColumns, data }),
//             });
//             if (!response.ok) {
//                 throw new Error("Failed to download PDF report");
//             }

//             const blob = await response.blob();
//             const url = window.URL.createObjectURL(blob);
//             const link = document.createElement('a');
//             link.href = url;
//             link.setAttribute('download', 'report.pdf');
//             document.body.appendChild(link);
//             link.click();
//             link.parentNode?.removeChild(link);
//         } catch (error) {
//             console.error("Error downloading PDF report:", error);
//         }
//     }, [data]);

//     const columns = React.useMemo(() => getColumns(handleDownloadPayslip, userRole), [handleDownloadPayslip, userRole]);

//     const [sorting, setSorting] = React.useState<SortingState>([]);
//     const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
//     const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({});

//     const table = useReactTable({
//         data,
//         columns,
//         onSortingChange: setSorting,
//         getCoreRowModel: getCoreRowModel(),
//         getPaginationRowModel: getPaginationRowModel(),
//         getSortedRowModel: getSortedRowModel(),
//         state: {
//             sorting,
//             columnVisibility,
//             rowSelection,
//         },
//     });

//     const renderColumnSelectionDropdown = (onDownload: (selectedColumns: string[]) => void) => (
//         <DropdownMenu>
//             <DropdownMenuTrigger asChild>
//                 <Button variant="outline" className="ml-2">
//                     <Download className="w-4 h-4 mr-2" />
//                     Export
//                 </Button>
//             </DropdownMenuTrigger>
//             <DropdownMenuContent align="end">
//                 {table.getAllColumns().filter((column) => column.getCanHide()).map((column) => (
//                     <DropdownMenuCheckboxItem
//                         key={column.id}
//                         className="capitalize"
//                         checked={column.getIsVisible()}
//                         onCheckedChange={(value) => column.toggleVisibility(!!value)}
//                     >
//                         {column.id}
//                     </DropdownMenuCheckboxItem>
//                 ))}
//                 <Button onClick={() => onDownload(table.getAllColumns().map(column => column.id))}>Download</Button>
//             </DropdownMenuContent>
//         </DropdownMenu>
//     );

//     return (
//         <div>
//             <div className="flex items-center py-4">
//                 <Input
//                     placeholder="Filter by first name..."
//                     value={(table.getColumn("employee.firstName")?.getFilterValue() as string) || ""}
//                     onChange={(event) => table.getColumn("employee.firstName")?.setFilterValue(event.target.value)}
//                     className="max-w-sm"
//                 />
//                 {renderColumnSelectionDropdown(handleDownloadExcel)}
//                 {renderColumnSelectionDropdown(handleDownloadPdf)}
//             </div>
//             <div className="rounded-md border">
//                 <Table>
//                     <TableHeader>
//                         {table.getHeaderGroups().map((headerGroup) => (
//                             <TableRow key={headerGroup.id}>
//                                 {headerGroup.headers.map((header) => (
//                                     <TableHead key={header.id}>
//                                         {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
//                                     </TableHead>
//                                 ))}
//                             </TableRow>
//                         ))}
//                     </TableHeader>
//                     <TableBody>
//                         {table.getRowModel().rows?.length ? (
//                             table.getRowModel().rows.map((row) => (
//                                 <TableRow
//                                     key={row.id}
//                                     data-state={row.getIsSelected() && "selected"}
//                                 >
//                                     {row.getVisibleCells().map((cell) => (
//                                         <TableCell key={cell.id}>
//                                             {flexRender(cell.column.columnDef.cell, cell.getContext())}
//                                         </TableCell>
//                                     ))}
//                                 </TableRow>
//                             ))
//                         ) : (
//                             <TableRow>
//                                 <TableCell colSpan={columns.length} className="h-24 text-center">
//                                     No results.
//                                 </TableCell>
//                             </TableRow>
//                         )}
//                     </TableBody>
//                 </Table>
//             </div>
//             <div className="flex items-center justify-end space-x-2 py-4">
//                 <div className="text-sm text-muted-foreground">
//                     {table.getRowModel().rows.length} of {table.getFilteredRowModel().rows.length} row(s) selected.
//                 </div>
//                 <div className="flex-1"></div>
//                 <Button
//                     variant="outline"
//                     size="sm"
//                     onClick={() => table.previousPage()}
//                     disabled={!table.getCanPreviousPage()}
//                 >
//                     Previous
//                 </Button>
//                 <Button
//                     variant="outline"
//                     size="sm"
//                     onClick={() => table.nextPage()}
//                     disabled={!table.getCanNextPage()}
//                 >
//                     Next
//                 </Button>
//             </div>
//         </div>
//     );
// }




// import * as React from "react";
// import {
//     ColumnDef,
//     SortingState,
//     VisibilityState,
//     RowSelectionState,
//     flexRender,
//     getCoreRowModel,
//     getPaginationRowModel,
//     getSortedRowModel,
//     useReactTable,
// } from "@tanstack/react-table";
// import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
// import { Button } from "@/components/ui/button";
// import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
// import { Input } from "@/components/ui/input";
// import { Checkbox } from "@/components/ui/checkbox";
// import { UserRole } from "@/context/types";
// import { getColumns } from "./approvedpaymentscolumns";
// import { Payment } from '@/app/types/payment';
// import { FaFileExcel, FaFilePdf } from 'react-icons/fa'; // Importing Excel and PDF icons

// interface DataTableProps<TData extends Payment> {
//     data: TData[];
//     userRole: UserRole;
//     onEdit: (payment: TData) => void;
//     onDelete: (paymentId: string) => void;
// }

// export function ApprovedpaymentDataTable<TData extends Payment>({
//     data,
//     userRole,
//     onEdit,
//     onDelete,
// }: DataTableProps<TData>) {
//     const handleDownloadPayslip = React.useCallback(async (paymentId: string) => {
//         try {
//             const response = await fetch(`/api/payments/downloadPayslip?paymentId=${paymentId}`);
//             if (!response.ok) {
//                 throw new Error("Failed to download payslip");
//             }

//             const blob = await response.blob();
//             const url = window.URL.createObjectURL(blob);
//             const link = document.createElement('a');
//             link.href = url;
//             link.setAttribute('download', `payslip_${paymentId}.pdf`);
//             document.body.appendChild(link);
//             link.click();
//             link.parentNode?.removeChild(link);
//         } catch (error) {
//             console.error("Error downloading payslip:", error);
//         }
//     }, []);

//     const handleDownloadExcel = React.useCallback(async (selectedColumns: string[]) => {
//         try {
//             const response = await fetch('/api/payments/downloadExcel', {
//                 method: 'POST',
//                 headers: {
//                     'Content-Type': 'application/json',
//                 },
//                 body: JSON.stringify({ selectedColumns, data }),
//             });
//             if (!response.ok) {
//                 throw new Error("Failed to download Excel report");
//             }

//             const blob = await response.blob();
//             const url = window.URL.createObjectURL(blob);
//             const link = document.createElement('a');
//             link.href = url;
//             link.setAttribute('download', 'report.xlsx');
//             document.body.appendChild(link);
//             link.click();
//             link.parentNode?.removeChild(link);
//         } catch (error) {
//             console.error("Error downloading Excel report:", error);
//         }
//     }, [data]);

//     const handleDownloadPdf = React.useCallback(async (selectedColumns: string[]) => {
//         try {
//             const response = await fetch('/api/payments/downloadPdf', {
//                 method: 'POST',
//                 headers: {
//                     'Content-Type': 'application/json',
//                 },
//                 body: JSON.stringify({ selectedColumns, data }),
//             });
//             if (!response.ok) {
//                 throw new Error("Failed to download PDF report");
//             }

//             const blob = await response.blob();
//             const url = window.URL.createObjectURL(blob);
//             const link = document.createElement('a');
//             link.href = url;
//             link.setAttribute('download', 'report.pdf');
//             document.body.appendChild(link);
//             link.click();
//             link.parentNode?.removeChild(link);
//         } catch (error) {
//             console.error("Error downloading PDF report:", error);
//         }
//     }, [data]);

//     const columns = React.useMemo(() => getColumns(handleDownloadPayslip, userRole), [handleDownloadPayslip, userRole]);

//     const [sorting, setSorting] = React.useState<SortingState>([]);
//     const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
//     const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({});

//     const table = useReactTable({
//         data,
//         columns,
//         onSortingChange: setSorting,
//         getCoreRowModel: getCoreRowModel(),
//         getPaginationRowModel: getPaginationRowModel(),
//         getSortedRowModel: getSortedRowModel(),
//         state: {
//             sorting,
//             columnVisibility,
//             rowSelection,
//         },
//     });

//     const renderColumnSelectionDropdown = (onDownload: (selectedColumns: string[]) => void, icon: JSX.Element) => (
//         <DropdownMenu>
//             <DropdownMenuTrigger asChild>
//                 <Button variant="outline" className="ml-2">
//                     {icon}
//                     Export
//                 </Button>
//             </DropdownMenuTrigger>
//             <DropdownMenuContent align="end">
//                 {table.getAllColumns().filter((column) => column.getCanHide()).map((column) => (
//                     <DropdownMenuCheckboxItem
//                         key={column.id}
//                         className="capitalize"
//                         checked={column.getIsVisible()}
//                         onCheckedChange={(value) => column.toggleVisibility(!!value)}
//                     >
//                         {column.id}
//                     </DropdownMenuCheckboxItem>
//                 ))}
//                 <Button onClick={() => onDownload(table.getAllColumns().map(column => column.id))}>Download</Button>
//             </DropdownMenuContent>
//         </DropdownMenu>
//     );

//     return (
//         <div>
//             <div className="flex justify-between items-center py-4">
//                 <Input
//                     placeholder="Filter by first name..."
//                     value={(table.getColumn("employee.firstName")?.getFilterValue() as string) || ""}
//                     onChange={(event) => table.getColumn("employee.firstName")?.setFilterValue(event.target.value)}
//                     className="max-w-sm"
//                 />

//                 <div>
//                     {renderColumnSelectionDropdown(handleDownloadExcel, <FaFileExcel className="w-4 h-4 mr-2 text-green-500" />)}
//                     {renderColumnSelectionDropdown(handleDownloadPdf, <FaFilePdf className="w-4 h-4 mr-2 text-red-600" />)}
//                 </div>
//             </div>
//             <div className="rounded-md border">
//                 <Table>
//                     <TableHeader>
//                         {table.getHeaderGroups().map((headerGroup) => (
//                             <TableRow key={headerGroup.id}>
//                                 {headerGroup.headers.map((header) => (
//                                     <TableHead key={header.id}>
//                                         {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
//                                     </TableHead>
//                                 ))}
//                             </TableRow>
//                         ))}
//                     </TableHeader>
//                     <TableBody>
//                         {table.getRowModel().rows?.length ? (
//                             table.getRowModel().rows.map((row) => (
//                                 <TableRow
//                                     key={row.id}
//                                     data-state={row.getIsSelected() && "selected"}
//                                 >
//                                     {row.getVisibleCells().map((cell) => (
//                                         <TableCell key={cell.id}>
//                                             {flexRender(cell.column.columnDef.cell, cell.getContext())}
//                                         </TableCell>
//                                     ))}
//                                 </TableRow>
//                             ))
//                         ) : (
//                             <TableRow>
//                                 <TableCell colSpan={columns.length} className="h-24 text-center">
//                                     No results.
//                                 </TableCell>
//                             </TableRow>
//                         )}
//                     </TableBody>
//                 </Table>
//             </div>
//             <div className="flex items-center justify-end space-x-2 py-4">
//                 <div className="text-sm text-muted-foreground">
//                     {table.getRowModel().rows.length} of {table.getFilteredRowModel().rows.length} row(s) selected.
//                 </div>
//                 <div className="flex-1"></div>
//                 <Button
//                     variant="outline"
//                     size="sm"
//                     onClick={() => table.previousPage()}
//                     disabled={!table.getCanPreviousPage()}
//                 >
//                     Previous
//                 </Button>
//                 <Button
//                     variant="outline"
//                     size="sm"
//                     onClick={() => table.nextPage()}
//                     disabled={!table.getCanNextPage()}
//                 >
//                     Next
//                 </Button>
//             </div>
//         </div>
//     );
// }



import * as React from "react";
import {
    ColumnDef,
    SortingState,
    VisibilityState,
    RowSelectionState,
    flexRender,
    getCoreRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { UserRole } from "@/context/types";
import { getColumns } from "./approvedpaymentscolumns";
import { Payment } from '@/app/types/payment';
import { FaFileExcel, FaFilePdf } from 'react-icons/fa';

interface DataTableProps<TData extends Payment> {
    data: TData[];
    userRole: UserRole;
    onEdit: (payment: TData) => void;
    onDelete: (paymentId: string) => void;
}

export function ApprovedpaymentDataTable<TData extends Payment>({
    data,
    userRole,
    onEdit,
    onDelete,
}: DataTableProps<TData>) {
    const handleDownloadPayslip = React.useCallback(async (paymentId: string) => {
        try {
            const response = await fetch(`/api/payments/downloadPayslip?paymentId=${paymentId}`);
            if (!response.ok) {
                throw new Error("Failed to download payslip");
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', `payslip_${paymentId}.pdf`);
            document.body.appendChild(link);
            link.click();
            link.parentNode?.removeChild(link);
        } catch (error) {
            console.error("Error downloading payslip:", error);
        }
    }, []);

    const handleDownloadExcel = React.useCallback(async (selectedColumns: string[]) => {
        try {
            const response = await fetch('/api/payments/downloadExcel', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ selectedColumns, data }),
            });
            if (!response.ok) {
                throw new Error("Failed to download Excel report");
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', 'report.xlsx');
            document.body.appendChild(link);
            link.click();
            link.parentNode?.removeChild(link);
        } catch (error) {
            console.error("Error downloading Excel report:", error);
        }
    }, [data]);

    const handleDownloadPdf = React.useCallback(async (selectedColumns: string[]) => {
        try {
            const response = await fetch('/api/payments/downloadPdf', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ selectedColumns, data }),
            });
            if (!response.ok) {
                throw new Error("Failed to download PDF report");
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', 'report.pdf');
            document.body.appendChild(link);
            link.click();
            link.parentNode?.removeChild(link);
        } catch (error) {
            console.error("Error downloading PDF report:", error);
        }
    }, [data]);

    const columns = React.useMemo(() => getColumns(handleDownloadPayslip, userRole), [handleDownloadPayslip, userRole]);

    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({});

    const table = useReactTable({
        data,
        columns,
        onSortingChange: setSorting,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        state: {
            sorting,
            columnVisibility,
            rowSelection,
        },
    });

    const renderColumnSelectionDropdown = (onDownload: (selectedColumns: string[]) => void, icon: JSX.Element) => (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="outline" className="ml-2">
                    {icon}
                    Export
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
                {table.getAllColumns().filter((column) => column.getCanHide()).map((column) => (
                    <DropdownMenuCheckboxItem
                        key={column.id}
                        className="capitalize"
                        checked={column.getIsVisible()}
                        onCheckedChange={(value) => column.toggleVisibility(!!value)}
                    >
                        {column.id}
                    </DropdownMenuCheckboxItem>
                ))}
                <Button onClick={() => onDownload(table.getAllColumns().map(column => column.id))}>Download</Button>
            </DropdownMenuContent>
        </DropdownMenu>
    );

    return (
        <div>
            <div className="flex justify-between items-center py-4">
                <Input
                    placeholder="Filter by first name..."
                    value={(table.getColumn("employee.firstName")?.getFilterValue() as string) || ""}
                    onChange={(event) => table.getColumn("employee.firstName")?.setFilterValue(event.target.value)}
                    className="max-w-sm"
                />

                <div>
                    {renderColumnSelectionDropdown(handleDownloadExcel, <FaFileExcel className="w-4 h-4 mr-2 text-green-500" />)}
                    {renderColumnSelectionDropdown(handleDownloadPdf, <FaFilePdf className="w-4 h-4 mr-2 text-red-600" />)}
                </div>
            </div>
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => (
                                    <TableHead key={header.id}>
                                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                                    </TableHead>
                                ))}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {table.getRowModel().rows?.length ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow
                                    key={row.id}
                                    data-state={row.getIsSelected() && "selected"}
                                >
                                    {row.getVisibleCells().map((cell) => (
                                        <TableCell key={cell.id}>
                                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan={columns.length} className="h-24 text-center">
                                    No results.
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>
        </div>
    );
}
