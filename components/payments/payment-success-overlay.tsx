// app/components/employees/employee-success-overlay.tsx

"use client"

import React from 'react';
import { Overlay } from '@/components/ui/overlay';
import { Button } from '@/components/ui/button';

interface PaymentSuccessOverlayProps {
    onClose: () => void;
}

export default function PaymentSuccessOverlay({ onClose }: PaymentSuccessOverlayProps) {
    return (
        <Overlay isOpen={true} onClose={onClose}>
            <div className="p-4 bg-green-100 rounded-md shadow-md">
                <h2 className="text-xl font-bold text-green-800">Success</h2>
                <p className="mt-2 text-green-600">Operation completed successfully.</p>
                <Button onClick={onClose} className="mt-4">Close</Button>
            </div>
        </Overlay>
    );
}
