// app/components/departments/department-edit-overlay.tsx

"use client"

import React, { ChangeEvent, FormEvent, useState, useEffect } from 'react';
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Overlay } from "@/components/ui/overlay";
import { Department } from "@/app/types/department";
import LoadingSpinner from '../LoadingSpinner';

// Schema for form validation
const departmentSchema = z.object({
    name: z.string().nonempty("Name is required"),
    description: z.string().nonempty("Description is required"),
});

type DepartmentFormValues = z.infer<typeof departmentSchema>;

interface DepartmentEditOverlayProps {
    isOpen: boolean;
    onClose: () => void;
    department: Department;
    onSubmit: (_id: string, data: Partial<Department>) => Promise<void>;
}

export default function DepartmentEditOverlay({
    isOpen,
    onClose,
    department,
    onSubmit,
}: DepartmentEditOverlayProps) {
    const [isLoading, setIsLoading] = useState(false);
    const {
        register,
        handleSubmit,
        formState: { errors },
        reset,
    } = useForm<DepartmentFormValues>({
        resolver: zodResolver(departmentSchema),
        defaultValues: {
            name: department.name,
            description: department.description,
        },
    });

    useEffect(() => {
        if (isOpen) {
            reset({
                name: department.name,
                description: department.description,
            });
        }
    }, [isOpen, department, reset]);

    const handleFormSubmit = async (data: DepartmentFormValues) => {
        setIsLoading(true);
        try {
            await onSubmit(department._id, data); // Call the onSubmit function provided by the parent component
        } catch (error) {
            console.error('There was a problem with the submit operation:', error);
        } finally {
            setIsLoading(false);
            onClose();
        }
    };

    if (!isOpen) return null;

    return (
        <Overlay isOpen={isOpen} onClose={onClose}>
            <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
                <div>
                    <label htmlFor="name" className="block text-sm font-medium">
                        Name
                    </label>
                    <Input id="name" {...register("name")} />
                    {errors.name && <p className="text-red-600">{errors.name.message}</p>}
                </div>
                <div>
                    <label htmlFor="description" className="block text-sm font-medium">
                        Description
                    </label>
                    <Input id="description" {...register("description")} />
                    {errors.description && <p className="text-red-600">{errors.description.message}</p>}
                </div>
                <Button type="submit" disabled={isLoading}>
                    {isLoading ? <LoadingSpinner /> : 'Submit'}
                </Button>
            </form>
            <button className="absolute top-6 right-6" onClick={onClose}>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-600 hover:text-red-800 transition duration-150" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </Overlay>
    );
}
