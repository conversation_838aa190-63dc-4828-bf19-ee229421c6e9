// app/components/departments/department-error-overlay.tsx

"use client"

import * as React from "react"
import { Overlay } from "@/components/ui/overlay"
import { Button } from "@/components/ui/button"

interface DepartmentErrorOverlayProps {
    onClose: () => void
}

export default function DepartmentErrorOverlay({ onClose }: DepartmentErrorOverlayProps) {
    return (
        <Overlay isOpen={true} onClose={onClose}>
            <div className="space-y-4">
                <h2 className="text-lg font-medium">Error</h2>
                <p>There was a problem with the operation.</p>
                <Button onClick={onClose}>Close</Button>
            </div>
        </Overlay>
    )
}


// "use client"

// import * as React from "react"
// import { Overlay } from "@/components/ui/overlay"
// import { Button } from "@/components/ui/button"

// interface DepartmentErrorOverlayProps {
//     onClose: () => void;
// }

// export default function DepartmentErrorOverlay({ onClose }: DepartmentErrorOverlayProps) {
//     return (
//         <Overlay  onClose={onClose}> {/* Use isError to control isOpen */}
//             <div className="space-y-4">
//                 <h2 className="text-lg font-medium">Error</h2>
//                 <p>There was a problem with the operation.</p>
//                 <Button onClick={onClose}>Close</Button>
//             </div>
//         </Overlay>
//     )
// }
