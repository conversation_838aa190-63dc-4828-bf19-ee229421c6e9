// // app/components/departments/department-success-overlay.tsx

// "use client"

// import * as React from "react"
// import { Overlay } from "@/components/ui/overlay"
// import { Button } from "@/components/ui/button"

// interface DepartmentSuccessOverlayProps {
//     onClose: () => void
// }

// export default function DepartmentSuccessOverlay({ onClose }: DepartmentSuccessOverlayProps) {
//     return (
//         <Overlay isOpen={true} onClose={onClose}>
//             <div className="space-y-4">
//                 <h2 className="text-lg font-medium">Success!</h2>
//                 <p>The operation was successful.</p>
//                 <Button onClick={onClose}>Close</Button>
//             </div>
//         </Overlay>
//     )
// }


"use client"

import * as React from "react";
import { Overlay } from "@/components/ui/overlay";
import { Button } from "@/components/ui/button";

interface DepartmentSuccessOverlayProps {
    onClose: () => void;
}

export default function DepartmentSuccessOverlay({ onClose }: DepartmentSuccessOverlayProps) {
    React.useEffect(() => {
        const timer = setTimeout(onClose, 2000);
        return () => clearTimeout(timer);
    }, [onClose]);

    return (
        <Overlay isOpen={true} onClose={onClose}>
            <div className="space-y-4 bg-green-200">
                <h2 className="text-lg font-medium text-green-600">Success!</h2>
                <p>The operation was successful.</p>
                <Button onClick={onClose}>Close</Button>
            </div>
        </Overlay>
    );
}
