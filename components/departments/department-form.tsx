// // app/components/departments/department-form.tsx

// "use client"

// import * as React from "react"
// import { useForm } from "react-hook-form"
// import { zodResolver } from "@hookform/resolvers/zod"
// import * as z from "zod"
// import { Button } from "@/components/ui/button"
// import { Input } from "@/components/ui/input"
// import { Overlay } from "@/components/ui/overlay"

// // Schema for form validation
// const departmentSchema = z.object({
//     name: z.string().nonempty("Name is required"),
//     description: z.string().nonempty("Description is required"),
// })

// type DepartmentFormValues = z.infer<typeof departmentSchema>

// export function DepartmentForm() {
//     const [isOpen, setIsOpen] = React.useState(false)
//     const {
//         register,
//         handleSubmit,
//         formState: { errors },
//     } = useForm<DepartmentFormValues>({
//         resolver: zodResolver(departmentSchema),
//     })

//     const onSubmit = (data: DepartmentFormValues) => {
//         console.log(data)
//         // Handle form submission
//     }

//     return (
//         <>
//             <Button onClick={() => setIsOpen(true)} className="ml-4">
//                 Add Department
//             </Button>
//             <Overlay isOpen={isOpen} onClose={() => setIsOpen(false)}>
//                 <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
//                     <div>
//                         <label htmlFor="name" className="block text-sm font-medium">
//                             Name
//                         </label>
//                         <Input id="name" {...register("name")} />
//                         {errors.name && <p className="text-red-600">{errors.name.message}</p>}
//                     </div>
//                     <div>
//                         <label htmlFor="description" className="block text-sm font-medium">
//                             Description
//                         </label>
//                         <Input id="description" {...register("description")} />
//                         {errors.description && <p className="text-red-600">{errors.description.message}</p>}
//                     </div>
//                     <Button type="submit">Submit</Button>
//                 </form>
//             </Overlay>
//         </>
//     )
// }


// // app/components/departments/department-form.tsx

// "use client"

// import * as React from "react"
// import { useForm } from "react-hook-form"
// import { zodResolver } from "@hookform/resolvers/zod"
// import * as z from "zod"
// import { Button } from "@/components/ui/button"
// import { Input } from "@/components/ui/input"
// import { Overlay } from "@/components/ui/overlay"
// import { Department } from "@/app/types/department"

// // Schema for form validation
// const departmentSchema = z.object({
//     name: z.string().nonempty("Name is required"),
//     description: z.string().nonempty("Description is required"),
// })

// type DepartmentFormValues = z.infer<typeof departmentSchema>

// interface DepartmentFormProps {
//     onSubmit: (data: Omit<Department, '_id'>) => void
// }

// export function DepartmentForm({ onSubmit }: DepartmentFormProps) {
//     const [isOpen, setIsOpen] = React.useState(false)
//     const {
//         register,
//         handleSubmit,
//         formState: { errors },
//     } = useForm<DepartmentFormValues>({
//         resolver: zodResolver(departmentSchema),
//     })

//     const handleFormSubmit = (data: DepartmentFormValues) => {
//         onSubmit(data)
//     }

//     return (
//         <>
//             <Button onClick={() => setIsOpen(true)} className="ml-4">
//                 Add Department
//             </Button>
//             <Overlay isOpen={isOpen} onClose={() => setIsOpen(false)}>
//                 <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
//                     <div>
//                         <label htmlFor="name" className="block text-sm font-medium">
//                             Name
//                         </label>
//                         <Input id="name" {...register("name")} />
//                         {errors.name && <p className="text-red-600">{errors.name.message}</p>}
//                     </div>
//                     <div>
//                         <label htmlFor="description" className="block text-sm font-medium">
//                             Description
//                         </label>
//                         <Input id="description" {...register("description")} />
//                         {errors.description && <p className="text-red-600">{errors.description.message}</p>}
//                     </div>
//                     <Button type="submit">Submit</Button>
//                 </form>
//             </Overlay>
//         </>
//     )
// }


// "use client"

// import * as React from "react";
// import { useForm } from "react-hook-form";
// import { zodResolver } from "@hookform/resolvers/zod";
// import * as z from "zod";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { Overlay } from "@/components/ui/overlay";
// import { Department } from "@/app/types/department";
// import { LoadingButton } from "@/components/ui/loading-button";

// // Schema for form validation
// const departmentSchema = z.object({
//     name: z.string().nonempty("Name is required"),
//     description: z.string().nonempty("Description is required"),
// });

// type DepartmentFormValues = z.infer<typeof departmentSchema>;

// interface DepartmentFormProps {
//     onSubmit: (data: Omit<Department, '_id'>) => void;
// }

// export function DepartmentForm({ onSubmit }: DepartmentFormProps) {
//     const [isOpen, setIsOpen] = React.useState(false);
//     const [isLoading, setIsLoading] = React.useState(false);
//     const {
//         register,
//         handleSubmit,
//         formState: { errors },
//     } = useForm<DepartmentFormValues>({
//         resolver: zodResolver(departmentSchema),
//     });

//     const handleFormSubmit = async (data: DepartmentFormValues) => {
//         setIsLoading(true);
//         await onSubmit(data);
//         setIsLoading(false);
//     };

//     return (
//         <>
//             <Button onClick={() => setIsOpen(true)} className="ml-4">
//                 Add Department
//             </Button>
//             <Overlay isOpen={isOpen} onClose={() => setIsOpen(false)}>
//                 <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
//                     <div>
//                         <label htmlFor="name" className="block text-sm font-medium">
//                             Name
//                         </label>
//                         <Input id="name" {...register("name")} />
//                         {errors.name && <p className="text-red-600">{errors.name.message}</p>}
//                     </div>
//                     <div>
//                         <label htmlFor="description" className="block text-sm font-medium">
//                             Description
//                         </label>
//                         <Input id="description" {...register("description")} />
//                         {errors.description && <p className="text-red-600">{errors.description.message}</p>}
//                     </div>
//                     <LoadingButton type="submit" isLoading={isLoading}>
//                         Submit
//                     </LoadingButton>
//                 </form>
//             </Overlay>
//         </>
//     );
// }



"use client"

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { LoadingButton } from "@/components/ui/loading-button";
import { Input } from "@/components/ui/input";
import { Overlay } from "@/components/ui/overlay";
import { Department } from "@/app/types/department";

// Schema for form validation
const departmentSchema = z.object({
    name: z.string().nonempty("Name is required"),
    description: z.string().nonempty("Description is required"),
});

type DepartmentFormValues = z.infer<typeof departmentSchema>;

interface DepartmentFormProps {
    onSubmit: (data: Omit<Department, '_id'>) => Promise<void>;
    onClose: () => void;
}

export function DepartmentForm({ onSubmit, onClose }: DepartmentFormProps) {
    const [isLoading, setIsLoading] = React.useState(false);
    const {
        register,
        handleSubmit,
        formState: { errors },
    } = useForm<DepartmentFormValues>({
        resolver: zodResolver(departmentSchema),
    });

    const handleFormSubmit = async (data: DepartmentFormValues) => {
        setIsLoading(true);
        await onSubmit(data);
        setIsLoading(false);
        onClose();  // Close the form overlay on success
    };

    return (
        <>
            <Overlay isOpen={true} onClose={onClose}>
                <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
                    <div>
                        <label htmlFor="name" className="block text-sm font-medium">
                            Name
                        </label>
                        <Input id="name" {...register("name")} />
                        {errors.name && <p className="text-red-600">{errors.name.message}</p>}
                    </div>
                    <div>
                        <label htmlFor="description" className="block text-sm font-medium">
                            Description
                        </label>
                        <Input id="description" {...register("description")} />
                        {errors.description && <p className="text-red-600">{errors.description.message}</p>}
                    </div>
                    <LoadingButton type="submit" isLoading={isLoading}>
                        Submit
                    </LoadingButton>
                </form>
            </Overlay>
        </>
    );
}




// // app/components/departments/department-form.tsx

// "use client"

// import * as React from "react"
// import { useForm } from "react-hook-form"
// import { zodResolver } from "@hookform/resolvers/zod"
// import * as z from "zod"
// import { Button } from "@/components/ui/button"
// import { Input } from "@/components/ui/input"
// import { Overlay } from "@/components/ui/overlay"
// import { Department } from "@/app/types/department"

// // Schema for form validation
// const departmentSchema = z.object({
//     name: z.string().nonempty("Name is required"),
//     description: z.string().nonempty("Description is required"),
// })

// type DepartmentFormValues = z.infer<typeof departmentSchema>

// interface DepartmentFormProps {
//     onSubmit: (data: Omit<Department, 'id'>) => void
// }

// export function DepartmentForm({ onSubmit }: DepartmentFormProps) {
//     const [isOpen, setIsOpen] = React.useState(false)
//     const {
//         register,
//         handleSubmit,
//         formState: { errors },
//     } = useForm<DepartmentFormValues>({
//         resolver: zodResolver(departmentSchema),
//     })

//     const handleFormSubmit = (data: DepartmentFormValues) => {
//         onSubmit(data)
//     }

//     return (
//         <>
//             <Button onClick={() => setIsOpen(true)} className="ml-4">
//                 Add Department
//             </Button>
//             <Overlay isOpen={isOpen} onClose={() => setIsOpen(false)}>
//                 <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
//                     <div>
//                         <label htmlFor="name" className="block text-sm font-medium">
//                             Name
//                         </label>
//                         <Input id="name" {...register("name")} />
//                         {errors.name && <p className="text-red-600">{errors.name.message}</p>}
//                     </div>
//                     <div>
//                         <label htmlFor="description" className="block text-sm font-medium">
//                             Description
//                         </label>
//                         <Input id="description" {...register("description")} />
//                         {errors.description && <p className="text-red-600">{errors.description.message}</p>}
//                     </div>
//                     <Button type="submit">Submit</Button>
//                 </form>
//             </Overlay>
//         </>
//     )
// }
