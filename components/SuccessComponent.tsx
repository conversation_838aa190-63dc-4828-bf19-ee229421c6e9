// import Link from 'next/link';
// import React, { useEffect } from 'react';
// import { useRouter } from 'next/navigation';

// interface SuccessComponentProps {
//   title?: string;
//   message?: string;
//   backUrl?: string;
// }

// const SuccessComponent: React.FC<SuccessComponentProps> = ({ title = "Success!", message, backUrl }) => {
//   const router = useRouter();

//   useEffect(() => {
//     if (backUrl) {
//       const timer = setTimeout(() => {
//         router.push(backUrl);
//       }, 4000);

//       // Cleanup the timer on component unmount
//       return () => clearTimeout(timer);
//     }
//   }, [backUrl, router]);

//   return (
//     <div className="text-center p-4">
//       <h3 className="text-lg font-semibold text-green-600">{title}</h3>
//       {message && <p className="text-gray-600">{message}</p>}
//       {backUrl && (
//         <p className="text-blue-600 underline">
//           <Link href={backUrl}>
//             <a>Go back to team page</a>
//           </Link>
//         </p>
//       )}
//     </div>
//   );
// };

// export default SuccessComponent;


// import Link from 'next/link';
// import React, { useEffect } from 'react';
// import { useRouter } from 'next/navigation';
// import Lottie from 'lottie-react';
// import successAnimation from '../public/Animation.json';

// interface SuccessComponentProps {
//   title?: string;
//   message?: string;
//   backUrl?: string;
// }

// const SuccessComponent: React.FC<SuccessComponentProps> = ({ title = "Success!", message, backUrl }) => {
//   const router = useRouter();

//   useEffect(() => {
//     if (backUrl) {
//       const timer = setTimeout(() => {
//         router.push(backUrl);
//       }, 4000);

//       // Cleanup the timer on component unmount
//       return () => clearTimeout(timer);
//     }
//   }, [backUrl, router]);

//   return (
//     <div className="text-center p-4">
//       <Lottie animationData={successAnimation} style={{ height: 200, margin: '0 auto' }} />
//       <h3 className="text-lg font-semibold text-green-600 mt-4">{title}</h3>
//       {message && <p className="text-gray-600">{message}</p>}
//       {backUrl && (
//         <p className="text-blue-600 underline">
//           <Link href={backUrl}>
//             <a>Go back to team page</a>
//           </Link>
//         </p>
//       )}
//     </div>
//   );
// };

// export default SuccessComponent;


import Link from 'next/link';
import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation'; // Ensure correct import
import Lottie from 'lottie-react';
import successAnimation from '../public/Animation.json';

interface SuccessComponentProps {
  title?: string;
  message?: string;
  backUrl?: string;
}

const SuccessComponent: React.FC<SuccessComponentProps> = ({ title = "Success!", message, backUrl }) => {
  const router = useRouter();

  useEffect(() => {
    if (backUrl) {
      console.log('Redirecting to:', backUrl);
      const timer = setTimeout(() => {
        console.log('Redirecting now...');
        router.push(backUrl);
      }, 2000);

      // Cleanup the timer on component unmount
      return () => clearTimeout(timer);
    }
  }, [backUrl, router]);

  return (
    <div className="text-center p-4">
      <Lottie animationData={successAnimation} style={{ height: 200, margin: '0 auto' }} />
      <h3 className="text-lg font-semibold text-green-600 mt-4">{title}</h3>
      {message && <p className="text-gray-200">{message}</p>}
      {backUrl && (
        <p className="text-blue-600 underline">
          <Link href={backUrl}>
            <span className='text-white text-2xl'>Go to Dashboard</span>
          </Link>
        </p>
      )}
    </div>
  );
};

export default SuccessComponent;
