// // // 'use client';

// // // import {
// // //   Line<PERSON><PERSON>,
// // //   Line,
// // //   XAxis,
// // //   YAxis,
// // //   CartesianGrid,
// // //   ResponsiveContainer,
// // // } from 'recharts';
// // // import {
// // //   Card,
// // //   CardContent,
// // //   CardDescription,
// // //   CardHeader,
// // //   CardTitle,
// // // } from '@/components/ui/card';

// // // // import data from '@/app/data/analytics';

// // // // const AnalyticsChart = () => {
// // // //   return (
// // // //     <>
// // // //       <Card>
// // // //         <CardHeader>
// // // //           <CardTitle>Analytics For This Year</CardTitle>
// // // //           <CardDescription>Views Per Month</CardDescription>
// // // //         </CardHeader>
// // // //         <CardContent>
// // // //           <div style={{ width: '100%', height: 300 }}>
// // // //             <ResponsiveContainer>
// // // //               <LineChart width={1100} height={300} data={data}>
// // // //                 <Line type='monotone' dataKey='uv' stroke='#8884d8' />
// // // //                 <CartesianGrid stroke='#ccc' />
// // // //                 <XAxis dataKey='name' />
// // // //                 <YAxis />
// // // //               </LineChart>
// // // //             </ResponsiveContainer>
// // // //           </div>
// // // //         </CardContent>
// // // //       </Card>
// // // //     </>
// // // //   );
// // // // };

// // // // export default AnalyticsChart;



// // import data from '@/app/data/analytics';
// // import {
// //   LineChart,
// //   Line,
// //   XAxis,
// //   YAxis,
// //   CartesianGrid,
// //   ResponsiveContainer,
// // } from 'recharts';
// // import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';

// // const AnalyticsChart = () => {
// //   return (
// //     <Card>
// //       <CardHeader>
// //         <CardTitle>Employee Attendance For This Year</CardTitle>
// //         <CardDescription>Attendance Per Month</CardDescription>
// //       </CardHeader>
// //       <CardContent>
// //         <div style={{ width: '100%', height: 300 }}>
// //           <ResponsiveContainer>
// //             <LineChart width={1100} height={300} data={data}>
// //               <Line type='monotone' dataKey='presentDays' stroke='#8884d8' />
// //               <Line type='monotone' dataKey='absentDays' stroke='#82ca9d' />
// //               <Line type='monotone' dataKey='leaveDays' stroke='#ffc658' />
// //               <CartesianGrid stroke='#ccc' />
// //               <XAxis dataKey='name' />
// //               <YAxis />
// //             </LineChart>
// //           </ResponsiveContainer>
// //         </div>
// //       </CardContent>
// //     </Card>
// //   );
// // };

// // export default AnalyticsChart;



// // app/components/analytics/AnalyticsChart.tsx

// import data from '@/app/data/analytics';
// import {
//   LineChart,
//   Line,
//   XAxis,
//   YAxis,
//   CartesianGrid,
//   ResponsiveContainer,
// } from 'recharts';
// import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';

// const AnalyticsChart = () => {
//   return (
//     <Card>
//       <CardHeader>
//         <CardTitle>Employee Attendance For This Year</CardTitle>
//         <CardDescription>Attendance Per Month</CardDescription>
//       </CardHeader>
//       <CardContent>
//         <div style={{ width: '100%', height: 300 }}>
//           <ResponsiveContainer>
//             <LineChart width={1100} height={300} data={data}>
//               <Line type='monotone' dataKey='presentDays' stroke='#8884d8' />
//               <Line type='monotone' dataKey='absentDays' stroke='#82ca9d' />
//               <Line type='monotone' dataKey='leaveDays' stroke='#ffc658' />
//               <CartesianGrid stroke='#ccc' />
//               <XAxis dataKey='name' />
//               <YAxis />
//             </LineChart>
//           </ResponsiveContainer>
//         </div>
//       </CardContent>
//     </Card>
//   );
// };

// export default AnalyticsChart;
