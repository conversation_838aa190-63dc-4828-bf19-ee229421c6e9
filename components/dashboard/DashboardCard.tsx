// import { Card, CardContent } from '@/components/ui/card';
// import { Newspaper } from 'lucide-react';

// const DashboardCard = () => {
//   return (
//     <Card className='bg-slate-100 p-4 pb-0'>
//       <CardContent>
//         <h3 className='text-3xl text-center mb-4 font-bold text-slate-500'>
//           Posts
//         </h3>
//         <div className='flex gap-5 justify-center items-center'>
//           <Newspaper size={72} className='text-slate-500' />
//           <h3 className='text-5xl font-semibold text-slate-500'>120</h3>
//         </div>
//       </CardContent>
//     </Card>
//   );
// };

// export default DashboardCard;


import { Card, CardContent } from '@/components/ui/card';
import { LucideIcon } from 'lucide-react';

interface DashboardCardProps {
  title: string;
  count: number;
  icon: React.ReactElement<LucideIcon>;
}

const DashboardCard = ({ title, count, icon }: DashboardCardProps) => {
  return (
    <Card className='bg-slate-100 p-4 pb-0'>
      <CardContent>
        <h3 className='text-3xl text-center mb-4 font-bold text-slate-500'>
          {title}
        </h3>
        <div className='flex gap-5 justify-center items-center'>
          {icon}
          <h3 className='text-5xl font-semibold text-slate-500'>{count}</h3>
        </div>
      </CardContent>
    </Card>
  );
};

export default DashboardCard;