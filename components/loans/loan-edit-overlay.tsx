"use client";

import React, { useCallback, useState, useEffect } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Overlay } from "@/components/ui/overlay";
import { Loan } from "@/app/types/loan";
import { UserRole } from "@/context/types";
import { loanSchema, LoanFormValues } from "@/app/types/loanSchema";
import { Input } from "@/components/ui/input";
import { debounce } from "@/lib/debounce";
import { Employee } from "@/app/types/employee";

// interface LoanEditOverlayProps {
//   isOpen: boolean;
//   onClose: () => void;
//   loan?: Loan;
//   onSubmit: (_id: string, data: Partial<Loan>) => Promise<void>;
//   employees: { _id: string; firstName: string; lastName: string; }[];
//   userRole: UserRole;
// }


interface LoanEditOverlayProps {
  isOpen: boolean;
  onClose: () => void;
  loan?: Loan | null; // Allow loan to be null or undefined
  onSubmit: (id: string, data: Partial<Loan>) => Promise<void>; // Ensure this matches your usage
  employees: Employee[]; // Ensure this is defined if needed
  userRole: string; // Ensure this is defined if needed
}

const LoanEditOverlay: React.FC<LoanEditOverlayProps> = ({ isOpen, onClose, loan, onSubmit, employees, userRole }) => {
  const methods = useForm<LoanFormValues>({
    resolver: zodResolver(loanSchema),
    defaultValues: {
      employee: typeof loan?.employee === 'string' ? loan.employee : loan?.employee._id,
      amount: loan?.amount.toString() || '0',
      interestRate: loan?.interestRate.toString() || '0',
      startDate: loan?.startDate || '',
      endDate: loan?.endDate || '',
      status: loan?.status || 'pending',
      repaymentSchedule: loan?.repaymentSchedule || 'weekly',
    },
  });

  const { handleSubmit, formState: { isSubmitting }, trigger, setValue, watch } = methods;
  const [alertMessage, setAlertMessage] = React.useState<string | null>(null);

  const amount = parseFloat(watch("amount") || '0');
  const interestRate = parseFloat(watch("interestRate") || '0');

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debounceUpdate = useCallback(debounce(() => {
    const totalRepayment = amount + (amount * (interestRate / 100));
    // Local state or useEffect for totalRepayment if needed
  }, 2000), [amount, interestRate]);

  React.useEffect(() => {
    debounceUpdate();
  }, [amount, interestRate, debounceUpdate]);

  const handleFormSubmit = async (data: LoanFormValues) => {
    const amount = parseFloat(data.amount);
    const interestRate = parseFloat(data.interestRate);
    const totalRepayment = amount + (amount * (interestRate / 100));

    const updatedData = {
      employee: data.employee,
      amount: amount,
      interestRate: interestRate,
      startDate: data.startDate,
      endDate: data.endDate,
      status: data.status,
      repaymentSchedule: data.repaymentSchedule,
    };

    try {
      if (loan) {
        await onSubmit(loan._id, updatedData);
      } else {
        await onSubmit("", updatedData);
      }
      onClose();
    } catch (error: any) {
      console.error("Error in handleFormSubmit:", error);
      setAlertMessage('An unexpected error occurred. Please try again.');
    }
  };

  return (
    <Overlay isOpen={isOpen} onClose={onClose}>
      {alertMessage && (
        <div className="alert alert-error">
          <span>{alertMessage}</span>
          <button onClick={() => setAlertMessage(null)}>Close</button>
        </div>
      )}
      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4 overflow-y-auto max-h-[80vh]">
          <div>
            <label htmlFor="employee" className="block text-sm font-medium">Employee</label>
            <select id="employee" {...methods.register('employee')}>
              {employees.map(emp => (
                <option key={emp._id} value={emp._id}>{emp.firstName} {emp.lastName}</option>
              ))}
            </select>
            {methods.formState.errors.employee && <p className="text-red-600">{methods.formState.errors.employee.message}</p>}

            <label htmlFor="amount" className="block text-sm font-medium">Amount</label>
            <Input id="amount" type="number" {...methods.register('amount')} />
            {methods.formState.errors.amount && <p className="text-red-600">{methods.formState.errors.amount.message}</p>}

            <label htmlFor="interestRate" className="block text-sm font-medium">Interest Rate</label>
            <Input id="interestRate" type="number" {...methods.register('interestRate')} />
            {methods.formState.errors.interestRate && <p className="text-red-600">{methods.formState.errors.interestRate.message}</p>}

            <label htmlFor="startDate" className="block text-sm font-medium">Start Date</label>
            <Input id="startDate" type="date" {...methods.register('startDate')} />
            {methods.formState.errors.startDate && <p className="text-red-600">{methods.formState.errors.startDate.message}</p>}

            <label htmlFor="endDate" className="block text-sm font-medium">End Date</label>
            <Input id="endDate" type="date" {...methods.register('endDate')} />
            {methods.formState.errors.endDate && <p className="text-red-600">{methods.formState.errors.endDate.message}</p>}

            <label htmlFor="status" className="block text-sm font-medium">Status</label>
            <select id="status" {...methods.register('status')}>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>
            {methods.formState.errors.status && <p className="text-red-600">{methods.formState.errors.status.message}</p>}

            <label htmlFor="repaymentSchedule" className="block text-sm font-medium">Repayment Schedule</label>
            <select id="repaymentSchedule" {...methods.register('repaymentSchedule')}>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
            </select>
            {methods.formState.errors.repaymentSchedule && <p className="text-red-600">{methods.formState.errors.repaymentSchedule.message}</p>}

            {/* Removed totalRepayment and remarks fields */}
          </div>

          <div className="flex justify-end">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : 'Save'}
            </Button>
          </div>
        </form>
      </FormProvider>
    </Overlay>
  );
};

export default LoanEditOverlay;
