// app/components/loans/loan-error-overlay.tsx

import * as React from "react";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface LoanErrorOverlayProps {
    onClose: () => void;
}

const LoanErrorOverlay: React.FC<LoanErrorOverlayProps> = ({ onClose }) => {
    return (
        <Dialog open={true} onOpenChange={onClose}>
            <DialogTitle>Error</DialogTitle>
            <DialogContent>
                <p>There was an error processing your request.</p>
                <Button onClick={onClose}>Close</Button>
            </DialogContent>
        </Dialog>
    );
};

export default LoanErrorOverlay;
