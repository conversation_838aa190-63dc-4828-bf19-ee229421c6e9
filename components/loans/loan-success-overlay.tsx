// app/components/loans/loan-success-overlay.tsx

import * as React from "react";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface LoanSuccessOverlayProps {
    onClose: () => void;
}

const LoanSuccessOverlay: React.FC<LoanSuccessOverlayProps> = ({ onClose }) => {
    return (
        <Dialog open={true} onOpenChange={onClose}>
            <DialogTitle>Success</DialogTitle>
            <DialogContent>
                <p>Operation successful!</p>
                <Button onClick={onClose}>Close</Button>
            </DialogContent>
        </Dialog>
    );
};

export default LoanSuccessOverlay;
