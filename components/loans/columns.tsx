// // app/components/loans/columns.tsx

// import { Loan } from "@/app/types/loan"
// import { ColumnDef } from "@tanstack/react-table"
// import { MoreHorizontal } from "lucide-react"
// import { Button } from "@/components/ui/button"
// import { Checkbox } from "@/components/ui/checkbox"
// import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

// export const columns: ColumnDef<Loan>[] = [
//   {
//     id: "select",
//     header: ({ table }) => (
//       <Checkbox
//         checked={
//           table.getIsAllPageRowsSelected() ||
//           (table.getIsSomePageRowsSelected() && "indeterminate")
//         }
//         onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
//         aria-label="Select all"
//       />
//     ),
//     cell: ({ row }) => (
//       <Checkbox
//         checked={row.getIsSelected()}
//         onCheckedChange={(value) => row.toggleSelected(!!value)}
//         aria-label="Select row"
//       />
//     ),
//     enableSorting: false,
//     enableHiding: false,
//   },
//   {
//     accessorKey: "borrower",
//     header: () => <div className="text-left">Borrower</div>,
//     cell: ({ row }) => <div className="text-left font-medium">{row.getValue("borrower")}</div>,
//   },
//   {
//     accessorKey: "loanAmount",
//     header: () => <div className="text-left">Loan Amount</div>,
//     cell: ({ row }) => <div className="text-left">{row.getValue("loanAmount")}</div>,
//   },
//   {
//     accessorKey: "interestRate",
//     header: () => <div className="text-left">Interest Rate</div>,
//     cell: ({ row }) => <div className="text-left">{row.getValue("interestRate")}%</div>,
//   },
//   {
//     accessorKey: "dueDate",
//     header: () => <div className="text-left">Due Date</div>,
//     cell: ({ row }) => <div className="text-left">{row.getValue("dueDate")}</div>,
//   },
//   {
//     accessorKey: "status",
//     header: () => <div className="text-left">Status</div>,
//     cell: ({ row }) => {
//       const status = row.getValue("status") as Loan["status"]
//       const statusClass = {
//         "approved": "text-green-600",
//         "pending": "text-yellow-600",
//         "rejected": "text-red-600",
//       }[status]
//       return <div className={`text-left ${statusClass}`}>{status}</div>
//     },
//   },
//   {
//     id: "actions",
//     header: () => <div className="text-left">Actions</div>,
//     cell: ({ row }) => {
//       const loan = row.original

//       return (
//         <DropdownMenu>
//           <DropdownMenuTrigger asChild>
//             <Button variant="ghost" className="h-8 w-8 p-0">
//               <span className="sr-only">Open menu</span>
//               <MoreHorizontal className="h-4 w-4" />
//             </Button>
//           </DropdownMenuTrigger>
//           <DropdownMenuContent align="end">
//             <DropdownMenuLabel>Actions</DropdownMenuLabel>
//             <DropdownMenuItem onClick={() => alert(`Copy loan ID: ${loan.loanId}`)}>
//               Copy loan ID
//             </DropdownMenuItem>
//             <DropdownMenuSeparator />
//             <DropdownMenuItem>View details</DropdownMenuItem>
//             <DropdownMenuItem>Edit</DropdownMenuItem>
//             <DropdownMenuItem>Delete</DropdownMenuItem>
//           </DropdownMenuContent>
//         </DropdownMenu>
//       )
//     },
//   },
// ]



// app/components/loans/loanColumns.tsx
import { Loan } from "@/app/types/loan";
import { ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";

export const columns: ColumnDef<Loan>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "_id",
    header: () => <div className="text-left">ID</div>,
    cell: ({ row }) => {
      const id = row.getValue("_id") as string;
      return <div className="text-left font-medium">{id}</div>;
    },
  },
  {
    accessorKey: "employee",
    header: () => <div className="text-left">Employee</div>,
    cell: ({ row }) => {
      const employee = row.original.employee;
      const employeeName = typeof employee === "object" ? `${employee.firstName} ${employee.lastName}` : employee;
      return <div className="text-left font-medium">{employeeName}</div>;
    },
  },
  {
    accessorKey: "amount",
    header: () => <div className="text-left">Amount</div>,
    cell: ({ row }) => {
      const amount = row.getValue("amount") as number;
      return <div className="text-left font-medium">{amount}</div>;
    },
  },
  {
    accessorKey: "interestRate",
    header: () => <div className="text-left">Interest Rate</div>,
    cell: ({ row }) => {
      const interestRate = row.getValue("interestRate") as number;
      return <div className="text-left font-medium">{interestRate}%</div>;
    },
  },
  {
    accessorKey: "startDate",
    header: () => <div className="text-left">Start Date</div>,
    cell: ({ row }) => {
      const startDate = row.getValue("startDate") as string;
      return <div className="text-left font-medium">{startDate}</div>;
    },
  },
  {
    accessorKey: "endDate",
    header: () => <div className="text-left">End Date</div>,
    cell: ({ row }) => {
      const endDate = row.getValue("endDate") as string;
      return <div className="text-left font-medium">{endDate}</div>;
    },
  },
  {
    accessorKey: "status",
    header: () => <div className="text-left">Status</div>,
    cell: ({ row }) => {
      const status = row.getValue("status") as Loan["status"];
      let statusClass = "text-gray-600";

      if (status === "approved") statusClass = "text-green-600";
      else if (status === "pending") statusClass = "text-yellow-600";
      else if (status === "rejected") statusClass = "text-red-600";

      return <div className={`font-medium ${statusClass}`}>{status}</div>;
    },
  },
  {
    accessorKey: "repaymentSchedule",
    header: () => <div className="text-left">Repayment Schedule</div>,
    cell: ({ row }) => {
      const repaymentSchedule = row.getValue("repaymentSchedule") as Loan["repaymentSchedule"];
      return <div className="text-left font-medium">{repaymentSchedule}</div>;
    },
  },
  {
    id: "actions",
    header: () => <div className="text-left">Actions</div>,
    cell: ({ row }) => {
      const loan = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(loan._id)}
            >
              Copy loan ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>View loan</DropdownMenuItem>
            <DropdownMenuItem>View loan details</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
