// // // app/components/loans/LoanForm.tsx
// // "use client";

// // import React, { useState, useEffect } from "react";
// // import { useForm, SubmitHandler } from "react-hook-form";
// // import { useRouter } from "next/router";
// // import { Loan } from "@/app/types/loan";
// // import axios from "axios";
// // import { Input } from "@/components/ui/input";
// // import { Button } from "@/components/ui/button";

// // interface LoanFormProps {
// //     loan?: Loan;
// //     employees: { _id: string; firstName: string; lastName: string }[];
// // }

// // const LoanForm: React.FC<LoanFormProps> = ({ loan, employees }) => {
// //     const {
// //         register,
// //         handleSubmit,
// //         formState: { errors },
// //         reset,
// //     } = useForm<Loan>({
// //         defaultValues: loan
// //             ? {
// //                 ...loan,
// //                 startDate: new Date(loan.startDate).toISOString().split("T")[0],
// //                 endDate: new Date(loan.endDate).toISOString().split("T")[0],
// //             }
// //             : {
// //                 employee: "",
// //                 amount: 0,
// //                 interestRate: 0,
// //                 startDate: "",
// //                 endDate: "",
// //                 status: "pending",
// //                 repaymentSchedule: "monthly",
// //             },
// //     });

// //     const router = useRouter();

// //     const onSubmit: SubmitHandler<Loan> = async (data) => {
// //         try {
// //             if (loan) {
// //                 await axios.put(`/api/loans/${loan._id}`, data);
// //             } else {
// //                 await axios.post("/api/loans", data);
// //             }
// //             router.push("/loans");
// //         } catch (error) {
// //             console.error("Failed to submit loan form", error);
// //         }
// //     };

// //     return (
// //         <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
// //             <div>
// //                 <label htmlFor="employee" className="block text-sm font-medium">
// //                     Employee
// //                 </label>
// //                 <select
// //                     {...register("employee", { required: true })}
// //                     id="employee"
// //                     className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
// //                 >
// //                     {employees.map((employee) => (
// //                         <option key={employee._id} value={employee._id}>
// //                             {employee.firstName} {employee.lastName}
// //                         </option>
// //                     ))}
// //                 </select>
// //                 {errors.employee && (
// //                     <p className="mt-2 text-sm text-red-600">This field is required</p>
// //                 )}
// //             </div>

// //             <div>
// //                 <label htmlFor="amount" className="block text-sm font-medium">
// //                     Amount
// //                 </label>
// //                 <Input
// //                     type="number"
// //                     {...register("amount", { required: true })}
// //                     id="amount"
// //                     className="mt-1 block w-full"
// //                 />
// //                 {errors.amount && (
// //                     <p className="mt-2 text-sm text-red-600">This field is required</p>
// //                 )}
// //             </div>

// //             <div>
// //                 <label htmlFor="interestRate" className="block text-sm font-medium">
// //                     Interest Rate
// //                 </label>
// //                 <Input
// //                     type="number"
// //                     step="0.01"
// //                     {...register("interestRate", { required: true })}
// //                     id="interestRate"
// //                     className="mt-1 block w-full"
// //                 />
// //                 {errors.interestRate && (
// //                     <p className="mt-2 text-sm text-red-600">This field is required</p>
// //                 )}
// //             </div>

// //             <div>
// //                 <label htmlFor="startDate" className="block text-sm font-medium">
// //                     Start Date
// //                 </label>
// //                 <Input
// //                     type="date"
// //                     {...register("startDate", { required: true })}
// //                     id="startDate"
// //                     className="mt-1 block w-full"
// //                 />
// //                 {errors.startDate && (
// //                     <p className="mt-2 text-sm text-red-600">This field is required</p>
// //                 )}
// //             </div>

// //             <div>
// //                 <label htmlFor="endDate" className="block text-sm font-medium">
// //                     End Date
// //                 </label>
// //                 <Input
// //                     type="date"
// //                     {...register("endDate", { required: true })}
// //                     id="endDate"
// //                     className="mt-1 block w-full"
// //                 />
// //                 {errors.endDate && (
// //                     <p className="mt-2 text-sm text-red-600">This field is required</p>
// //                 )}
// //             </div>

// //             <div>
// //                 <label htmlFor="status" className="block text-sm font-medium">
// //                     Status
// //                 </label>
// //                 <select
// //                     {...register("status", { required: true })}
// //                     id="status"
// //                     className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
// //                 >
// //                     <option value="pending">Pending</option>
// //                     <option value="approved">Approved</option>
// //                     <option value="rejected">Rejected</option>
// //                 </select>
// //                 {errors.status && (
// //                     <p className="mt-2 text-sm text-red-600">This field is required</p>
// //                 )}
// //             </div>

// //             <div>
// //                 <label htmlFor="repaymentSchedule" className="block text-sm font-medium">
// //                     Repayment Schedule
// //                 </label>
// //                 <select
// //                     {...register("repaymentSchedule", { required: true })}
// //                     id="repaymentSchedule"
// //                     className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
// //                 >
// //                     <option value="weekly">Weekly</option>
// //                     <option value="monthly">Monthly</option>
// //                 </select>
// //                 {errors.repaymentSchedule && (
// //                     <p className="mt-2 text-sm text-red-600">This field is required</p>
// //                 )}
// //             </div>

// //             <div>
// //                 <Button type="submit" className="w-full">
// //                     {loan ? "Update Loan" : "Create Loan"}
// //                 </Button>
// //             </div>
// //         </form>
// //     );
// // };

// // export default LoanForm;





// // app/components/loans/loan-form.tsx

// import * as React from "react";
// import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
// import { Button} from "@/components/ui/button";
// import { Input } from "../ui/input";
// import { Select} from "../ui/select";
// // import { Option } from "lucide-react";
// import { Loan } from "@/app/types/loan";

// interface LoanFormProps {
//     onSubmit: (loan: Omit<Loan, "_id">) => void;
//     isOpen: boolean;
//     onClose: () => void;
// }

// const LoanForm: React.FC<LoanFormProps> = ({ onSubmit, isOpen, onClose }) => {
//     const [employee, setEmployee] = React.useState<string>("");
//     const [amount, setAmount] = React.useState<number>(0);
//     const [interestRate, setInterestRate] = React.useState<number>(0);
//     const [startDate, setStartDate] = React.useState<string>("");
//     const [endDate, setEndDate] = React.useState<string>("");
//     const [status, setStatus] = React.useState<'pending' | 'approved' | 'rejected'>('pending');
//     const [repaymentSchedule, setRepaymentSchedule] = React.useState<'weekly' | 'monthly'>('monthly');

//     const handleSubmit = () => {
//         onSubmit({
//             employee,
//             amount,
//             interestRate,
//             startDate,
//             endDate,
//             status,
//             repaymentSchedule,
//         });
//         onClose();
//     };

//     return (
//         <Dialog open={isOpen} onOpenChange={onClose}>
//             <DialogTitle>Add New Loan</DialogTitle>
//             <DialogContent>
//                 <Input
//                     type="text"
//                     value={employee}
//                     onChange={(e) => setEmployee(e.target.value)}
//                     placeholder="Employee ID"
//                 />
//                 <Input
//                     type="number"
//                     value={amount}
//                     onChange={(e) => setAmount(Number(e.target.value))}
//                     placeholder="Amount"
//                 />
//                 <Input
//                     type="number"
//                     value={interestRate}
//                     onChange={(e) => setInterestRate(Number(e.target.value))}
//                     placeholder="Interest Rate"
//                 />
//                 <Input
//                     type="date"
//                     value={startDate}
//                     onChange={(e) => setStartDate(e.target.value)}
//                 />
//                 <Input
//                     type="date"
//                     value={endDate}
//                     onChange={(e) => setEndDate(e.target.value)}
//                 />
//                 <Select
//                     value={status}
//                     onChange={(e) => setStatus(e.target.value as 'pending' | 'approved' | 'rejected')}
//                 >
//                     <option value="pending">Pending</option>
//                     <option value="approved">Approved</option>
//                     <option value="rejected">Rejected</option>
//                 </Select>
//                 <Select
//                     value={repaymentSchedule}
//                     onChange={(e) => setRepaymentSchedule(e.target.value as 'weekly' | 'monthly')}
//                 >
//                     <option value="weekly">Weekly</option>
//                     <option value="monthly">Monthly</option>
//                 </Select>
//                 <Button onClick={handleSubmit}>Submit</Button>
//             </DialogContent>
//         </Dialog>

