// // app/components/attendance/columns.tsx

// "use client"

// import { Attendance } from "@/app/types/attendance"
// import { ColumnDef } from "@tanstack/react-table"
// import { MoreHorizontal, ArrowUpDown } from "lucide-react"

// import { But<PERSON> } from "@/components/ui/button"
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuItem,
//   DropdownMenuLabel,
//   DropdownMenuSeparator,
//   DropdownMenuTrigger,
// } from "@/components/ui/dropdown-menu"
// import { Checkbox } from "@/components/ui/checkbox"

// export const columns: ColumnDef<Attendance>[] = [
//   {
//     id: "select",
//     header: ({ table }) => (
//       <Checkbox
//         checked={
//           table.getIsAllPageRowsSelected() ||
//           (table.getIsSomePageRowsSelected() && "indeterminate")
//         }
//         onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
//         aria-label="Select all"
//       />
//     ),
//     cell: ({ row }) => (
//       <Checkbox
//         checked={row.getIsSelected()}
//         onCheckedChange={(value) => row.toggleSelected(!!value)}
//         aria-label="Select row"
//       />
//     ),
//     enableSorting: false,
//     enableHiding: false,
//   },
//   {
//     accessorKey: "id",
//     header: () => <div className="text-left">ID</div>,
//     cell: ({ row }) => {
//       const id = row.getValue("id") as string
//       return <div className="text-left font-medium">{id}</div>
//     },
//   },
//   {
//     accessorKey: "date",
//     header: () => <div className="text-left">Date</div>,
//     cell: ({ row }) => {
//       const date = row.getValue("date") as string
//       return <div className="text-left font-medium">{date}</div>
//     },
//   },
//   {
//     accessorKey: "firstname",
//     header: () => <div className="text-left">First Name</div>,
//     cell: ({ row }) => {
//       const firstname = row.getValue("firstname") as string
//       return <div className="text-left font-medium">{firstname}</div>
//     },
//   },
//   {
//     accessorKey: "lastname",
//     header: () => <div className="text-left">Last Name</div>,
//     cell: ({ row }) => {
//       const lastname = row.getValue("lastname") as string
//       return <div className="text-left font-medium">{lastname}</div>
//     },
//   },
//   {
//     accessorKey: "status",
//     header: () => <div className="text-left">Status</div>,
//     cell: ({ row }) => {
//       const status = row.getValue("status") as Attendance["status"]
//       let statusClass = "text-gray-600"

//       if (status === "present") statusClass = "text-green-600"
//       else if (status === "absent") statusClass = "text-red-600"
//       else if (status === "late") statusClass = "text-yellow-600"
//       else if (status === "excused") statusClass = "text-blue-600"

//       return <div className={`font-medium ${statusClass}`}>{status}</div>
//     },
//   },
//   {
//     id: "actions",
//     header: () => <div className="text-left">Actions</div>,
//     cell: ({ row }) => {
//       const attendance = row.original

//       return (
//         <DropdownMenu>
//           <DropdownMenuTrigger asChild>
//             <Button variant="ghost" className="h-8 w-8 p-0">
//               <span className="sr-only">Open menu</span>
//               <MoreHorizontal className="h-4 w-4" />
//             </Button>
//           </DropdownMenuTrigger>
//           <DropdownMenuContent align="end">
//             <DropdownMenuLabel>Actions</DropdownMenuLabel>
//             <DropdownMenuItem
//               onClick={() => navigator.clipboard.writeText(attendance.id)}
//             >
//               Copy attendance ID
//             </DropdownMenuItem>
//             <DropdownMenuSeparator />
//             <DropdownMenuItem>View employee</DropdownMenuItem>
//             <DropdownMenuItem>View attendance details</DropdownMenuItem>
//           </DropdownMenuContent>
//         </DropdownMenu>
//       )
//     },
//   },
// ]





// app/components/attendance/columns.tsx

"use client"

import { Attendance } from "@/app/types/attendance"
import { ColumnDef } from "@tanstack/react-table"
import { MoreHorizontal, ArrowUpDown } from "lucide-react"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Checkbox } from "@/components/ui/checkbox"

export const columns: ColumnDef<Attendance>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "id",
    header: () => <div className="text-left">ID</div>,
    cell: ({ row }) => {
      const id = row.getValue("id") as string
      return <div className="text-left font-medium">{id}</div>
    },
  },
  {
    accessorKey: "date",
    header: () => <div className="text-left">Date</div>,
    cell: ({ row }) => {
      const date = row.getValue("date") as string
      return <div className="text-left font-medium">{date}</div>
    },
  },
  {
    accessorKey: "firstname",
    header: () => <div className="text-left">First Name</div>,
    cell: ({ row }) => {
      const firstname = row.getValue("firstname") as string
      return <div className="text-left font-medium">{firstname}</div>
    },
  },
  {
    accessorKey: "lastname",
    header: () => <div className="text-left">Last Name</div>,
    cell: ({ row }) => {
      const lastname = row.getValue("lastname") as string
      return <div className="text-left font-medium">{lastname}</div>
    },
  },
  {
    accessorKey: "status",
    header: () => <div className="text-left">Status</div>,
    cell: ({ row }) => {
      const status = row.getValue("status") as Attendance["status"]
      let statusClass = "text-gray-600"

      if (status === "present") statusClass = "text-green-600"
      else if (status === "absent") statusClass = "text-red-600"
      else if (status === "late") statusClass = "text-yellow-600"
      else if (status === "excused") statusClass = "text-blue-600"

      return <div className={`font-medium ${statusClass}`}>{status}</div>
    },
  },
  {
    id: "actions",
    header: () => <div className="text-left">Actions</div>,
    cell: ({ row }) => {
      const attendance = row.original

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(attendance.id)}
            >
              Copy attendance ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>View employee</DropdownMenuItem>
            <DropdownMenuItem>View attendance details</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
