import React from 'react';

interface LoadingSpinnerProps {
  size?: number;
  color?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ size = 4, color = "white" }) => {
  return (
    <>
      <div className="flex justify-center items-center">
        <div className={`animate-spin rounded-full h-${size} w-${size} border-b-2 border-${color}`}></div>
      </div>

    </>

  );
};

export default LoadingSpinner;




