/**
 * v0 by Vercel.
 * @see https://v0.dev/t/ZNdQGn0fp9P
 * Documentation: https://v0.dev/docs#integrating-generated-code-into-your-nextjs-app
 */
import Link from "next/link"
import { Command, CommandList, CommandGroup, CommandItem, CommandSeparator } from "@/components/ui/command"
import { JSX, SVGProps } from "react"

export default function Component() {
  return (
    <div className="flex min-h-screen">
      <aside className="fixed inset-y-0 left-0 z-10 flex w-64 flex-col border-r bg-background">
        <div className="flex h-16 shrink-0 items-center px-6">
          <Link href="#" className="flex items-center gap-2 font-bold" prefetch={false}>
            <MountainIcon className="h-6 w-6" />
            <span>Admin Dashboard</span>
          </Link>
        </div>
        <nav className="flex flex-1 flex-col overflow-y-auto">
          <Command>
            <CommandList>
              <CommandGroup>
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <LayoutDashboardIcon className="h-4 w-4" />
                    <span>Dashboard</span>
                  </Link>
                </CommandItem>
              </CommandGroup>
              <CommandSeparator />
              <CommandGroup heading="Employees">
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <UsersIcon className="h-4 w-4" />
                    <span>All Employees</span>
                  </Link>
                </CommandItem>
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <UserPlusIcon className="h-4 w-4" />
                    <span>Add Employees</span>
                  </Link>
                </CommandItem>
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <UserCheckIcon className="h-4 w-4" />
                    <span>Full Time Employees</span>
                  </Link>
                </CommandItem>
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <ClockIcon className="h-4 w-4" />
                    <span>Attendance Employees</span>
                  </Link>
                </CommandItem>
              </CommandGroup>
              <CommandSeparator />
              <CommandGroup heading="Attendance">
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <CalendarIcon className="h-4 w-4" />
                    <span>Today</span>
                  </Link>
                </CommandItem>
                <CommandGroup heading="Today">
                  <CommandItem>
                    <Link href="#" className="flex items-center gap-2" prefetch={false}>
                      <CircleCheckIcon className="h-4 w-4" />
                      <span>On Time</span>
                    </Link>
                  </CommandItem>
                  <CommandItem>
                    <Link href="#" className="flex items-center gap-2" prefetch={false}>
                      <ClockIcon className="h-4 w-4" />
                      <span>Late</span>
                    </Link>
                  </CommandItem>
                  <CommandItem>
                    <Link href="#" className="flex items-center gap-2" prefetch={false}>
                      <CircleXIcon className="h-4 w-4" />
                      <span>Absent</span>
                    </Link>
                  </CommandItem>
                  <CommandItem>
                    <Link href="#" className="flex items-center gap-2" prefetch={false}>
                      <UserCheckIcon className="h-4 w-4" />
                      <span>On Leave</span>
                    </Link>
                  </CommandItem>
                  <CommandItem>
                    <Link href="#" className="flex items-center gap-2" prefetch={false}>
                      <UserMinusIcon className="h-4 w-4" />
                      <span>Sick</span>
                    </Link>
                  </CommandItem>
                </CommandGroup>
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <CalendarIcon className="h-4 w-4" />
                    <span>Monthly Attendance</span>
                  </Link>
                </CommandItem>
              </CommandGroup>
              <CommandSeparator />
              <CommandGroup heading="Tasks">
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <ListIcon className="h-4 w-4" />
                    <span>All Tasks</span>
                  </Link>
                </CommandItem>
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <UserCheckIcon className="h-4 w-4" />
                    <span>Assigned Tasks</span>
                  </Link>
                </CommandItem>
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <CircleCheckIcon className="h-4 w-4" />
                    <span>Completed Tasks</span>
                  </Link>
                </CommandItem>
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <CircleXIcon className="h-4 w-4" />
                    <span>Canceled Tasks</span>
                  </Link>
                </CommandItem>
              </CommandGroup>
              <CommandSeparator />
              <CommandGroup heading="Payments">
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <DollarSignIcon className="h-4 w-4" />
                    <span>Attendance Payments</span>
                  </Link>
                </CommandItem>
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <DollarSignIcon className="h-4 w-4" />
                    <span>Task Payments</span>
                  </Link>
                </CommandItem>
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <DollarSignIcon className="h-4 w-4" />
                    <span>Bonus Payments</span>
                  </Link>
                </CommandItem>
              </CommandGroup>
              <CommandSeparator />
              <CommandGroup heading="Leave">
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <CalendarIcon className="h-4 w-4" />
                    <span>Annual Leave</span>
                  </Link>
                </CommandItem>
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <CalendarIcon className="h-4 w-4" />
                    <span>Maternity Leave</span>
                  </Link>
                </CommandItem>
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <CalendarIcon className="h-4 w-4" />
                    <span>Education Leave</span>
                  </Link>
                </CommandItem>
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <CalendarIcon className="h-4 w-4" />
                    <span>Sick Leave</span>
                  </Link>
                </CommandItem>
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <CalendarIcon className="h-4 w-4" />
                    <span>Funeral Leave</span>
                  </Link>
                </CommandItem>
              </CommandGroup>
              <CommandSeparator />
              <CommandGroup heading="Loans">
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <CreditCardIcon className="h-4 w-4" />
                    <span>All Loans</span>
                  </Link>
                </CommandItem>
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <CreditCardIcon className="h-4 w-4" />
                    <span>Loan Requests</span>
                  </Link>
                </CommandItem>
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <CreditCardIcon className="h-4 w-4" />
                    <span>Approved Loans</span>
                  </Link>
                </CommandItem>
                <CommandItem>
                  <Link href="#" className="flex items-center gap-2" prefetch={false}>
                    <CreditCardIcon className="h-4 w-4" />
                    <span>Declined Loans</span>
                  </Link>
                </CommandItem>
              </CommandGroup>
            </CommandList>
          </Command>
        </nav>
      </aside>
      <div className="flex-1 bg-muted/40 p-6 sm:p-10" />
    </div>
  )
}

function CalendarIcon(props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M8 2v4" />
      <path d="M16 2v4" />
      <rect width="18" height="18" x="3" y="4" rx="2" />
      <path d="M3 10h18" />
    </svg>
  )
}


function CircleCheckIcon(props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="12" cy="12" r="10" />
      <path d="m9 12 2 2 4-4" />
    </svg>
  )
}


function CircleXIcon(props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="12" cy="12" r="10" />
      <path d="m15 9-6 6" />
      <path d="m9 9 6 6" />
    </svg>
  )
}


function ClockIcon(props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="12" cy="12" r="10" />
      <polyline points="12 6 12 12 16 14" />
    </svg>
  )
}


function CreditCardIcon(props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect width="20" height="14" x="2" y="5" rx="2" />
      <line x1="2" x2="22" y1="10" y2="10" />
    </svg>
  )
}


function DollarSignIcon(props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <line x1="12" x2="12" y1="2" y2="22" />
      <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
    </svg>
  )
}


function LayoutDashboardIcon(props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect width="7" height="9" x="3" y="3" rx="1" />
      <rect width="7" height="5" x="14" y="3" rx="1" />
      <rect width="7" height="9" x="14" y="12" rx="1" />
      <rect width="7" height="5" x="3" y="16" rx="1" />
    </svg>
  )
}


function ListIcon(props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <line x1="8" x2="21" y1="6" y2="6" />
      <line x1="8" x2="21" y1="12" y2="12" />
      <line x1="8" x2="21" y1="18" y2="18" />
      <line x1="3" x2="3.01" y1="6" y2="6" />
      <line x1="3" x2="3.01" y1="12" y2="12" />
      <line x1="3" x2="3.01" y1="18" y2="18" />
    </svg>
  )
}


function MountainIcon(props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="m8 3 4 8 5-5 5 15H2L8 3z" />
    </svg>
  )
}


function UserCheckIcon(props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
      <circle cx="9" cy="7" r="4" />
      <polyline points="16 11 18 13 22 9" />
    </svg>
  )
}


function UserMinusIcon(props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
      <circle cx="9" cy="7" r="4" />
      <line x1="22" x2="16" y1="11" y2="11" />
    </svg>
  )
}


function UserPlusIcon(props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
      <circle cx="9" cy="7" r="4" />
      <line x1="19" x2="19" y1="8" y2="14" />
      <line x1="22" x2="16" y1="11" y2="11" />
    </svg>
  )
}


function UsersIcon(props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
      <circle cx="9" cy="7" r="4" />
      <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
    </svg>
  )
}