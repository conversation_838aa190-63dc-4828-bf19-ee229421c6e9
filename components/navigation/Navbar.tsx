// 'use client';
// import kawandamatpLogo from '../../public/kawandamatpLogo.png';
// import logo from '../../public/logo.jpg';
// import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
// import {
//     DropdownMenu,
//     DropdownMenuContent,
//     DropdownMenuItem,
//     DropdownMenuLabel,
//     DropdownMenuSeparator,
//     DropdownMenuTrigger,
//   } from '@/components/ui/dropdown-menu';


// import Image from 'next/image';
// import Link from 'next/link';
// import ThemeToggler from '../ThemeToggler';

// const Navbar = () => {
//   return (
//     // <div className='bg-primary  dark:bg-slate-700 py-2 px-5 flex justify-between'>
//     <div className='bg-primary  dark:bg-slate-700 py-2 px-5 flex justify-between'>
//       <Link href='/'>
//         <Image src={logo} alt='' width={40} />
//       </Link>

//       <div className='flex items-center'>
//       <ThemeToggler />
//       <DropdownMenu>
//         <DropdownMenuTrigger>
//           {' '}
//           <Avatar>
//             <AvatarImage src='https://github.com/shadcn.png' alt='@shadcn' />
//             <AvatarFallback>kw</AvatarFallback>
//           </Avatar>
//         </DropdownMenuTrigger>
//         <DropdownMenuContent>
//           <DropdownMenuLabel>My Account</DropdownMenuLabel>
//           <DropdownMenuSeparator />
//           <DropdownMenuItem>
//             <Link href='/profile'>Profile</Link>
//           </DropdownMenuItem>
//           <DropdownMenuItem>
//             {' '}
//             <Link href='/login'>Logout</Link>
//           </DropdownMenuItem>
//         </DropdownMenuContent>
//       </DropdownMenu>

//       </div>

//     </div>
//   );
// };

// export default Navbar;


'use client';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import kawandamatpLogo from '../../public/kawandamatpLogo.png';
import logo from '../../public/logo.jpg';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import Image from 'next/image';
import Link from 'next/link';
import ThemeToggler from '../ThemeToggler';

const Navbar = () => {
  const router = useRouter();

  const handleLogout = async () => {
    try {
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        // router.push('/auth/auth');
        router.push('/login');
      } else {
        console.error('Failed to logout');
      }
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  return (
    <div className='bg-primary dark:bg-slate-700 py-2 px-5 flex justify-between'>
      <Link href='/'>
        <Image src={logo} alt='' width={40} />
      </Link>

      <div className='flex items-center'>
        <ThemeToggler />
        <DropdownMenu>
          <DropdownMenuTrigger>
            <Avatar>
              <AvatarImage src='https://github.com/shadcn.png' alt='@shadcn' />
              <AvatarFallback>kw</AvatarFallback>
            </Avatar>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>My Account</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <Link href='/profile'>Profile</Link>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleLogout}>
              Logout
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

export default Navbar;



// // components/Navbar.js

// 'use client';
// import { useRouter } from 'next/navigation';
// import { useAuth } from '@/context/AuthContext';
// import logo from '../../public/logo.jpg';
// // import kawandamatpLogo from '../../public/kawandamatpLogo.png';
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuItem,
//   DropdownMenuLabel,
//   DropdownMenuSeparator,
//   DropdownMenuTrigger,
// } from '@/components/ui/dropdown-menu';

// import Image from 'next/image';
// import Link from 'next/link';
// import ThemeToggler from '../ThemeToggler';

// const Navbar = () => {
//   const router = useRouter();
//   const { user, logout } = useAuth();

//   const handleLogout = async () => {
//     try {
//       const response = await fetch('/api/auth/logout', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//       });

//       if (response.ok) {
//         logout();
//         router.push('/login');
//       } else {
//         console.error('Failed to logout');
//       }
//     } catch (error) {
//       console.error('Error during logout:', error);
//     }
//   };

//   return (
//     <div className='bg-primary dark:bg-slate-700 py-2 px-5 flex justify-between'>
//       <Link href='/'>
//         <Image src={logo} alt='' width={40} />
//       </Link>

//       <div className='flex items-center'>
//         <ThemeToggler />
//         <DropdownMenu>
//           <DropdownMenuTrigger>
//             <span className="text-white">{user ? user.username : 'Profile'}</span>
//           </DropdownMenuTrigger>
//           <DropdownMenuContent>
//             <DropdownMenuLabel>My Account</DropdownMenuLabel>
//             <DropdownMenuSeparator />
//             <DropdownMenuItem>
//               <Link href='/profile'>Profile</Link>
//             </DropdownMenuItem>
//             <DropdownMenuItem onClick={handleLogout}>
//               Logout
//             </DropdownMenuItem>
//           </DropdownMenuContent>
//         </DropdownMenu>
//       </div>
//     </div>
//   );
// };

// export default Navbar;



// // components/Navbar.js

// 'use client';
// import { useRouter } from 'next/navigation';
// import { useAuth } from '@/context/AuthContext';
// import logo from '../../public/logo.jpg';
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuItem,
//   DropdownMenuLabel,
//   DropdownMenuSeparator,
//   DropdownMenuTrigger,
// } from '@/components/ui/dropdown-menu';

// import Image from 'next/image';
// import Link from 'next/link';
// import ThemeToggler from '../ThemeToggler';

// const Navbar = () => {
//   const router = useRouter();
//   const { user, logout } = useAuth();

//   const handleLogout = async () => {
//     try {
//       const response = await fetch('/api/auth/logout', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//       });

//       if (response.ok) {
//         logout();
//         router.push('/login');
//       } else {
//         console.error('Failed to logout');
//       }
//     } catch (error) {
//       console.error('Error during logout:', error);
//     }
//   };

//   return (
//     <div className='bg-primary dark:bg-slate-700 py-2 px-5 flex justify-between'>
//       <Link href='/'>
//         <Image src={logo} alt='' width={40} />
//       </Link>

//       <div className='flex items-center'>
//         <ThemeToggler />
//         <DropdownMenu>
//           <DropdownMenuTrigger>
//             <span className="text-gray-700">{user ? user.username : 'Profile'}</span>
//           </DropdownMenuTrigger>
//           <DropdownMenuContent>
//             <DropdownMenuLabel>My Account</DropdownMenuLabel>
//             <DropdownMenuSeparator />
//             <DropdownMenuItem>
//               <Link href='/profile'>Profile</Link>
//             </DropdownMenuItem>
//             <DropdownMenuItem onClick={handleLogout}>
//               Logout
//             </DropdownMenuItem>
//           </DropdownMenuContent>
//         </DropdownMenu>
//       </div>
//     </div>
//   );
// };

// export default Navbar;
