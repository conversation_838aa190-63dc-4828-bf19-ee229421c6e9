// /**
//  * v0 by Vercel.
//  * @see https://v0.dev/t/i7vl1P1SBZL
//  * Documentation: https://v0.dev/docs#integrating-generated-code-into-your-nextjs-app
//  */


"use client";
import Link from "next/link";
import { useState } from "react";
import { usePathname } from "next/navigation";
import { useAuth } from "@/context/AuthContext"; // Import the AuthContext hook
import {
  Collapsible,
  CollapsibleTrigger,
  CollapsibleContent,
} from "@/components/ui/collapsible";
import {
  Calendar,
  ChevronRight,
  CheckCircle,
  XCircle,
  Clock,
  CreditCard,
  DollarSign,
  LayoutDashboard,
  ListChecks,
  List,
  Users,
  User,
} from "lucide-react"; // Importing Lucide icons

interface SidenavItemProps {
  href: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  label: string;
}

interface SidenavCollapsibleItemProps {
  collapsible: true;
  triggerIcon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  label: string;
  items: SidenavItemProps[];
}

type SidenavItemOrCollapsible = SidenavItemProps | SidenavCollapsibleItemProps;

interface RoleBasedItems {
  [key: string]: SidenavItemOrCollapsible[];
}

function SidenavItem({ href, icon: Icon, label }: SidenavItemProps) {
  const pathname = usePathname();
  const isActive = pathname === href;

  return (
    <Link href={href} prefetch={false}>
      <span
        className={`flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground ${isActive ? "bg-green-600 text-gray-100" : ""
          }`}
      >
        <Icon className="h-5 w-5" />
        {label}
      </span>
    </Link>
  );
}


function SidenavCollapsible({
  triggerIcon: TriggerIcon,
  label,
  collapsible,
  items,
  children,
}: SidenavCollapsibleItemProps & { children: React.ReactNode }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Collapsible className="grid gap-2" open={isOpen} onOpenChange={setIsOpen}>
      <CollapsibleTrigger className="flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground">
        <TriggerIcon className="h-5 w-5" />
        {label}
        <ChevronRight
          className={`ml-auto h-5 w-5 transition-all ${isOpen ? "rotate-90" : ""}`}
        />
      </CollapsibleTrigger>
      <CollapsibleContent>
        <div className="-mx-3 grid gap-1">{children}</div>
      </CollapsibleContent>
    </Collapsible>
  );
}


type UserRole = "admin" | "ceoadmin" | "accountant" | "hrmanager" | "fieldsupervisor";

const roleBasedItems: RoleBasedItems = {
  admin: [
    { href: "/dashboard", icon: LayoutDashboard, label: "Dashboard" },
    {
      href: "/dashboard/departments",
      icon: Users,
      label: "Departments"
    },
    {
      collapsible: true,
      triggerIcon: Users,
      label: "Employees",
      items: [
        { href: "/dashboard/allemployees", icon: Users, label: "All Employees" },
        { href: "/dashboard/fulltimeemployees", icon: CheckCircle, label: "Full-Time Employees" },
        { href: "/dashboard/attendanceemployees", icon: Clock, label: "Attendance Employees" },
      ],
    },
    {
      collapsible: true,
      triggerIcon: Calendar,
      label: "Attendance",
      items: [
        { href: "/dashboard/attendance", icon: Calendar, label: "Today" },
        { href: "/dashboard/monthly-attendance", icon: Calendar, label: "Monthly Attendance" },
      ],
    },
    {
      collapsible: true,
      triggerIcon: List,
      label: "Tasks",
      items: [
        { href: "/dashboard/alltasks", icon: List, label: "All Tasks" },
        { href: "/dashboard/asignedtasks", icon: ListChecks, label: "Assigned Tasks" },
        { href: "/dashboard/completedtasks", icon: CheckCircle, label: "Completed Tasks" },
        { href: "/dashboard/canceledtasks", icon: XCircle, label: "Canceled Tasks" },
      ],
    },
    {
      collapsible: true,
      triggerIcon: DollarSign,
      label: "Payments",
      items: [
        { href: "/dashboard/attendancepayments", icon: DollarSign, label: "Attendance Payments" },
        { href: "/dashboard/approvedMonthlyPayments", icon: DollarSign, label: "Approved Payments" },
        { href: "/dashboard/taskpayments", icon: DollarSign, label: "Task Payments" },
        { href: "/dashboard/bonuspayments", icon: DollarSign, label: "Bonus Payments" },
      ],
    },
    {
      collapsible: true,
      triggerIcon: Calendar,
      label: "Leave",
      items: [
        { href: "/dashboard/anualleave", icon: Calendar, label: "Annual Leave" },
        { href: "/dashboard/maternityleave", icon: Calendar, label: "Maternity Leave" },
        { href: "/dashboard/educationleave", icon: Calendar, label: "Education Leave" },
        { href: "/dashboard/sickleave", icon: Calendar, label: "Sick Leave" },
        { href: "/dashboard/funeralleave", icon: Calendar, label: "Funeral Leave" },
      ],
    },
    {
      collapsible: true,
      triggerIcon: CreditCard,
      label: "Loans",
      items: [
        { href: "/dashboard/allloans", icon: CreditCard, label: "All Loans" },
        { href: "/dashboard/loanrequest", icon: CreditCard, label: "Loan Requests" },
        { href: "/dashboard/appovedloans", icon: CreditCard, label: "Approved Loans" },
        { href: "/dashboard/declinedloans", icon: CreditCard, label: "Declined Loans" },
      ],
    },
    {
      collapsible: true,
      triggerIcon: CreditCard,
      label: "Inventory",
      items: [
        { href: "/dashboard/inventory", icon: CreditCard, label: "All Stock" },
        { href: "/dashboard/warehouses", icon: CreditCard, label: "Warehouses" },
      ],
    },
    {
      collapsible: true,
      triggerIcon: User,
      label: "Add user",
      items: [
        { href: "/dashboard/addAdmin", icon: User, label: "Add AdminUser" },
      ],
    },
    // {
    //   href: "/dashboard/departments",
    //   icon: Users, // Replace with the appropriate icon
    //   label: "Departments"
    // },
  ],
  ceoadmin: [
    { href: "/dashboard", icon: LayoutDashboard, label: "Dashboard" },
    {
      href: "/dashboard/departments",
      icon: Users,
      label: "Departments"
    },
    {
      collapsible: true,
      triggerIcon: Users,
      label: "Employees",
      items: [
        { href: "/dashboard/allemployees", icon: Users, label: "All Employees" },
        { href: "/dashboard/fulltimeemployees", icon: CheckCircle, label: "Full-Time Employees" },
        { href: "/dashboard/attendanceemployees", icon: Clock, label: "Attendance Employees" },
      ],
    },
    {
      collapsible: true,
      triggerIcon: Calendar,
      label: "Attendance",
      items: [
        { href: "/dashboard/attendance", icon: Calendar, label: "Today" },
        { href: "/dashboard/monthly-attendance", icon: Calendar, label: "Monthly Attendance" },
      ],
    },
    {
      collapsible: true,
      triggerIcon: List,
      label: "Tasks",
      items: [
        { href: "/dashboard/alltasks", icon: List, label: "All Tasks" },
        { href: "/dashboard/asignedtasks", icon: ListChecks, label: "Assigned Tasks" },
        { href: "/dashboard/completedtasks", icon: CheckCircle, label: "Completed Tasks" },
        { href: "/dashboard/canceledtasks", icon: XCircle, label: "Canceled Tasks" },
      ],
    },
    {
      collapsible: true,
      triggerIcon: DollarSign,
      label: "Payments",
      items: [
        { href: "/dashboard/attendancepayments", icon: DollarSign, label: "Attendance Payments" },
        { href: "/dashboard/approvedMonthlyPayments", icon: DollarSign, label: "Approved Payments" },
        { href: "/dashboard/taskpayments", icon: DollarSign, label: "Task Payments" },
        { href: "/dashboard/bonuspayments", icon: DollarSign, label: "Bonus Payments" },
      ],
    },
    {
      collapsible: true,
      triggerIcon: Calendar,
      label: "Leave",
      items: [
        { href: "/dashboard/anualleave", icon: Calendar, label: "Annual Leave" },
        { href: "/dashboard/maternityleave", icon: Calendar, label: "Maternity Leave" },
        { href: "/dashboard/educationleave", icon: Calendar, label: "Education Leave" },
        { href: "/dashboard/sickleave", icon: Calendar, label: "Sick Leave" },
        { href: "/dashboard/funeralleave", icon: Calendar, label: "Funeral Leave" },
      ],
    },
    {
      collapsible: true,
      triggerIcon: CreditCard,
      label: "Loans",
      items: [
        { href: "/dashboard/allloans", icon: CreditCard, label: "All Loans" },
        { href: "/dashboard/loanrequest", icon: CreditCard, label: "Loan Requests" },
        { href: "/dashboard/appovedloans", icon: CreditCard, label: "Approved Loans" },
        { href: "/dashboard/declinedloans", icon: CreditCard, label: "Declined Loans" },
      ],
    },
    {
      collapsible: true,
      triggerIcon: CreditCard,
      label: "Inventory",
      items: [
        { href: "/dashboard/inventory", icon: CreditCard, label: "All Stock" },
        { href: "/dashboard/warehouses", icon: CreditCard, label: "Warehouses" },
      ],
    },
    {
      collapsible: true,
      triggerIcon: User,
      label: "Add user",
      items: [
        { href: "/dashboard/addAdmin", icon: User, label: "Add AdminUser" },
      ],
    },
    // {
    //   href: "/dashboard/departments",
    //   icon: Users, 
    //   label: "Departments"
    // },
  ],
  accountant: [
    { href: "/dashboard", icon: LayoutDashboard, label: "Dashboard" },
    { href: "/dashboard/attendancepayments", icon: DollarSign, label: "Attendance Payments" },
    { href: "/dashboard/approvedMonthlyPayments", icon: DollarSign, label: "Approved Payments" },

    { href: "/dashboard/taskpayments", icon: DollarSign, label: "Task Payments" },
    { href: "/dashboard/bonuspayments", icon: DollarSign, label: "Bonus Payments" },
    { href: "/dashboard/allloans", icon: CreditCard, label: "All Loans" },
    { href: "/dashboard/loanrequest", icon: CreditCard, label: "Loan Requests" },
    { href: "/dashboard/appovedloans", icon: CreditCard, label: "Approved Loans" },
    { href: "/dashboard/declinedloans", icon: CreditCard, label: "Declined Loans" },
  ],
  hrmanager: [
    { href: "/dashboard", icon: LayoutDashboard, label: "Dashboard" },
    { href: "/dashboard/anualleave", icon: Calendar, label: "Annual Leave" },
    { href: "/dashboard/maternityleave", icon: Calendar, label: "Maternity Leave" },
    { href: "/dashboard/educationleave", icon: Calendar, label: "Education Leave" },
    { href: "/dashboard/sickleave", icon: Calendar, label: "Sick Leave" },
    { href: "/dashboard/funeralleave", icon: Calendar, label: "Funeral Leave" },
  ],
  fieldsupervisor: [
    { href: "/dashboard", icon: LayoutDashboard, label: "Dashboard" },
    { href: "/dashboard/alltasks", icon: List, label: "All Tasks" },
    { href: "/dashboard/asignedtasks", icon: ListChecks, label: "Assigned Tasks" },
    { href: "/dashboard/completedtasks", icon: CheckCircle, label: "Completed Tasks" },
    { href: "/dashboard/canceledtasks", icon: XCircle, label: "Canceled Tasks" },
  ],
};


export default function Sidenav() {
  const { user } = useAuth();

  if (!user) return null;

  const items = roleBasedItems[user.role as UserRole] || [];

  return (
    <div className="flex h-screen w-full flex-col gap-1 p-2">
      {items.map((item) =>
        "collapsible" in item ? (
          <SidenavCollapsible
            key={item.label}
            triggerIcon={item.triggerIcon}
            label={item.label}
            collapsible={item.collapsible}
            items={item.items}
          >
            {item.items.map((subItem) => (
              <SidenavItem key={subItem.href} href={subItem.href} icon={subItem.icon} label={subItem.label} />
            ))}
          </SidenavCollapsible>
        ) : (
          <SidenavItem key={item.href} href={item.href} icon={item.icon} label={item.label} />
        )
      )}
    </div>
  );

}
