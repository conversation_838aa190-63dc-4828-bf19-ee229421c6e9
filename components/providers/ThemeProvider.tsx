'use client';

import { ThemeProvider as NextThemesProvider } from 'next-themes';
import { type ThemeProviderProps } from 'next-themes/dist/types';

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;
}
// Now, bring it into the layout at app/layout.tsx:


// Wrap the app:

// return (
//   <html lang='en'>
//     <body className={inter.className}>
//       <ThemeProvider
//         attribute='class'
//         defaultTheme='light'
//         enableSystem={true}
//         storageKey='dashboard-theme'
//       >
//         <Navbar />
//         <div className='flex'>
//           <div className='hidden md:block h-[100vh]'>
//             <Sidebar />
//           </div>
//           <div className='p-5 w-full md:max-w-[1140px]'>{children}</div>
//         </div>
//         <Toaster />
//       </ThemeProvider>
//     </body>
//   </html>
// );