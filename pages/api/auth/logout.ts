// // pages/api/auth/logout.js

// import type { NextApiRequest, NextApiResponse } from 'next';

// interface ApiResponse {
//     message: string;
// }

// export default function handler(req: NextApiRequest, res: NextApiResponse<ApiResponse>) {
//     if (req.method !== 'POST') {
//         return res.status(405).json({ message: 'Method not allowed' });
//     }

//     res.setHeader('Set-Cookie', 'token=; Max-Age=0; Path=/; HttpOnly');
//     res.status(200).json({ message: 'Logout successful' });
// }


// pages/api/auth/logout.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import { runMiddleware } from '@/lib/cors';
import cors from '@/lib/cors';

interface ApiResponse {
    message: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse<ApiResponse>) {
    await runMiddleware(req, res, cors);

    if (req.method !== 'POST') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    res.setHeader('Set-Cookie', 'token=; Max-Age=0; Path=/; HttpOnly');
    res.status(200).json({ message: 'Logout successful' });
}
