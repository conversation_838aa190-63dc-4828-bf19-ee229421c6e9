
// // pages/api/auth/login.js

// import type { NextApiRequest, NextApiResponse } from 'next';

// import dbConnect from '@/lib/mongoose';
// import User from '@/app/models/User';
// import bcrypt from 'bcryptjs';
// import jwt from 'jsonwebtoken';

// // Define the expected request body
// interface LoginRequestBody {
//   email: string;
//   password: string;
// }

// // Define the response structure
// interface ApiResponse {
//   message: string;
//   token?: string;
// }

// export default async function userLogin(req: NextApiRequest, res: NextApiResponse<ApiResponse>) {
//   console.log('Login request received:', req.method, req.body);

//   if (req.method !== 'POST') {
//     console.log('Method not allowed for login');
//     return res.status(405).json({ message: 'Method not allowed' });
//   }

//   await dbConnect();

//   const { email, password } = req.body as LoginRequestBody;

//   try {
//     console.log('Finding user with email:', email);
//     const user = await User.findOne({ email });

//     if (!user) {
//       console.log('No user found for email:', email);
//       return res.status(401).json({ message: 'Email not found' });
//     }

//     console.log('Comparing provided password with stored password');
//     const isMatch = await bcrypt.compare(password, user.password);

//     if (!isMatch) {
//       console.log('Password does not match for user:', email);
//       return res.status(401).json({ message: 'Incorrect password' });
//     }

//     console.log('Generating JWT token for user:', user._id);
//     const token = jwt.sign(
//       { userId: user._id, role: user.role },
//       process.env.JWT_SECRET || '', // Fallback to an empty string if JWT_SECRET is undefined
//       { expiresIn: '10h' }
//     );

//     console.log('Login successful for user:', email);
//     res.status(200).json({ message: 'Login successful', token });
//   } catch (error) {
//     console.error('Error during login:', error);
//     if (error instanceof Error) {
//       res.status(500).json({ message: `Internal Server Error: ${error.message}` });
//     } else {
//       res.status(500).json({ message: 'Internal Server Error' });
//     }
//   }
// }





// pages/api/auth/login.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import User from '@/app/models/User';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { runMiddleware } from '@/lib/cors';
import cors from '@/lib/cors';

// Define the expected request body
interface LoginRequestBody {
  email: string;
  password: string;
}

// Define the response structure
interface ApiResponse {
  message: string;
  token?: string;
}

export default async function userLogin(req: NextApiRequest, res: NextApiResponse<ApiResponse>) {
  await runMiddleware(req, res, cors);

  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  await dbConnect();

  const { email, password } = req.body as LoginRequestBody;

  try {
    const user = await User.findOne({ email });

    if (!user) {
      return res.status(401).json({ message: 'Email not found' });
    }

    const isMatch = await bcrypt.compare(password, user.password);

    if (!isMatch) {
      return res.status(401).json({ message: 'Incorrect password' });
    }

    const token = jwt.sign(
      { userId: user._id, role: user.role },
      process.env.JWT_SECRET || '',
      { expiresIn: '10h' }
    );

    res.status(200).json({ message: 'Login successful', token });
  } catch (error) {
    console.error('Error during login:', error);
    if (error instanceof Error) {
      res.status(500).json({ message: `Internal Server Error: ${error.message}` });
    } else {
      res.status(500).json({ message: 'Internal Server Error' });
    }
  }
}

