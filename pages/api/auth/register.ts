// // pages/api/auth/register.ts
// import type { NextApiRequest, NextApiResponse } from 'next';
// import dbConnect from '@/lib/mongoose';
// import User from '@/app/models/User'; // Adjust the import path according to your setup
// import bcrypt from 'bcryptjs';

// // Define the expected request body
// interface RegistrationRequestBody {
//   username: string;
//   password: string;
//   email: string;
//   role: string; // Adjust if you have specific roles defined
// }

// // Define the response structure
// interface ApiResponse {
//   message: string;
// }

// export default async function userRegistration(req: NextApiRequest, res: NextApiResponse<ApiResponse>) {
//   if (req.method !== 'POST') {
//     return res.status(405).json({ message: 'Method not allowed' });
//   }

//   await dbConnect();

//   const { username, password, email, role } = req.body as RegistrationRequestBody;

//   try {
//     const existingUser = await User.findOne({ email });
//     if (existingUser) {
//       return res.status(400).json({ message: 'User already exists' });
//     }

//     const user = new User({ username, password, email, role });
//     await user.save();

//     res.status(201).json({ message: 'User created successfully' });
//   } 

//   catch (error) {
//     if (error instanceof Error) {
//       res.status(500).json({ message: `Internal Server Error: ${error.message}` });
//     } else {
//       res.status(500).json({ message: 'Internal Server Error' });
//     }
//   }

// }




// pages/api/auth/register.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import User from '@/app/models/User';
import bcrypt from 'bcryptjs';
import { runMiddleware } from '@/lib/cors';
import cors from '@/lib/cors';

// Define the expected request body
interface RegistrationRequestBody {
  username: string;
  password: string;
  email: string;
  role: string;
}

// Define the response structure
interface ApiResponse {
  message: string;
}

export default async function userRegistration(req: NextApiRequest, res: NextApiResponse<ApiResponse>) {
  await runMiddleware(req, res, cors);

  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  await dbConnect();

  const { username, password, email, role } = req.body as RegistrationRequestBody;

  try {
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ message: 'User already exists' });
    }

    const user = new User({ username, password, email, role });
    await user.save();

    res.status(201).json({ message: 'User created successfully' });
  } catch (error) {
    if (error instanceof Error) {
      res.status(500).json({ message: `Internal Server Error: ${error.message}` });
    } else {
      res.status(500).json({ message: 'Internal Server Error' });
    }
  }

}
