// // import type { NextApiRequest, NextApiResponse } from 'next';
// // import dbConnect from '@/lib/mongoose';
// // import Employee from '@/app/models/Employee';

// // export default async function deleteEmployee(req: NextApiRequest, res: NextApiResponse) {
// //     if (req.method !== 'DELETE') {
// //         res.setHeader('Allow', ['DELETE']);
// //         return res.status(405).end(`Method ${req.method} Not Allowed`);
// //     }

// //     try {
// //         await dbConnect();
// //         const { id } = req.body;

// //         const employee = await Employee.findByIdAndDelete(id);

// //         if (!employee) {
// //             return res.status(404).json({ message: 'Employee not found' });
// //         }

// //         res.status(200).json({ message: 'Employee deleted successfully' });
// //     } catch (error) {
// //         console.error("Error in deleteEmployee:", error);
// //         res.status(500).json({ message: 'An unexpected error occurred', error });
// //     }
// // }


// // pages/api/employees/deleteEmployee.ts
// import type { NextApiRequest, NextApiResponse } from 'next';
// import dbConnect from '@/lib/mongoose';
// import Employee from '@/app/models/Employee';

// export default async function deleteEmployee(req: NextApiRequest, res: NextApiResponse) {
//     if (req.method !== 'DELETE') {
//         res.setHeader('Allow', ['DELETE']);
//         return res.status(405).end(`Method ${req.method} Not Allowed`);
//     }

//     try {
//         await dbConnect();
//         const { id } = req.body;

//         if (!id) {
//             return res.status(400).json({ message: 'ID is required' });
//         }

//         const employee = await Employee.findByIdAndDelete(id);

//         if (!employee) {
//             return res.status(404).json({ message: 'Employee not found' });
//         }

//         res.status(200).json({ message: 'Employee deleted successfully' });
//     } catch (error) {
//         console.error("Error in deleteEmployee:", error);
//         res.status(500).json({ message: 'An unexpected error occurred', error });
//     }
// }



// import type { NextApiRequest, NextApiResponse } from 'next';
// import dbConnect from '@/lib/mongoose';
// import Employee from '@/app/models/Employee';

// export default async function deleteEmployee(req: NextApiRequest, res: NextApiResponse) {
//     if (req.method !== 'DELETE') {
//         res.setHeader('Allow', ['DELETE']);
//         return res.status(405).end(`Method ${req.method} Not Allowed`);
//     }

//     try {
//         await dbConnect();
//         const { id } = req.query; 

//         if (!id) {
//             return res.status(400).json({ message: 'ID is required' });
//         }

//         const employee = await Employee.findByIdAndDelete(id);

//         if (!employee) {
//             return res.status(404).json({ message: 'Employee not found' });
//         }

//         res.status(200).json({ message: 'Employee deleted successfully' });
//     } catch (error) {
//         console.error("Error in deleteEmployee:", error);
//         res.status(500).json({ message: 'An unexpected error occurred', error });
//     }
// }


import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import Employee from '@/app/models/Employee';

export default async function deleteEmployee(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'DELETE') {
        res.setHeader('Allow', ['DELETE']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    try {
        await dbConnect();
        const { id } = req.body; // Get ID from the request body

        if (!id) {
            return res.status(400).json({ message: 'ID is required' });
        }

        const employee = await Employee.findByIdAndDelete(id);

        if (!employee) {
            return res.status(404).json({ message: 'Employee not found' });
        }

        res.status(200).json({ message: 'Employee deleted successfully' });
    } catch (error) {
        console.error("Error in deleteEmployee:", error);
        res.status(500).json({ message: 'An unexpected error occurred', error });
    }
}
