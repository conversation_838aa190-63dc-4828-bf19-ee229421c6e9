
// pages/api/employees/index.ts
import { NextApiRequest, NextApiResponse } from 'next';
import Employee from '@/app/models/Employee';
import dbConnect from '@/lib/mongoose'; // Import the dbConnect function

// eslint-disable-next-line import/no-anonymous-default-export
export default async (req: NextApiRequest, res: NextApiResponse) => {
    try {
        await dbConnect(); // Connect to the database
        if (req.method === 'GET') {
            const employees = await Employee.find().populate('department');
            res.status(200).json(employees);
        } else if (req.method === 'POST') {
            const { firstName, lastName, jobTitle, salary, hireDate, department, email, phone, gender, dateOfBirth, homeOrigin, residence, maritalStatus, nextOfKin, nextOfKinContact, status } = req.body;
            const newEmployee = new Employee({
                firstName,
                lastName,
                jobTitle,
                salary,
                hireDate: new Date(hireDate),
                department,
                email,
                phone,
                gender,
                dateOfBirth: new Date(dateOfBirth),
                homeOrigin,
                residence,
                maritalStatus,
                nextOfKin,
                nextOfKinContact,
                status
            });
            await newEmployee.save();
            res.status(201).json(newEmployee);
        } else {
            res.status(405).json({ message: 'Method not allowed' });
        }
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Internal server error' });
    }
};



