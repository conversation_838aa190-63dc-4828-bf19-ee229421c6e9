// pages/api/employees/getAllEmployees.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import Employee from '@/app/models/Employee';

export default async function getAllEmployees(req: NextApiRequest, res: NextApiResponse) {
    try {
        await dbConnect();

        console.log("Fetching employee records...");

        // Attempt to find employee records
        const employeeRecords = await Employee.find().select('_id firstName lastName employee_rate phone gender age profile_image_base64 employee_type is_active');

        console.log("Employee records fetched:", employeeRecords.length);

        // Ensure the response structure matches the frontend expectations
        res.status(200).json({ employees: employeeRecords });
    } catch (error) {
        if (error instanceof Error) {
            console.error("Error in getAllEmployees:", error.message, error.stack);
            res.status(500).json({ message: 'An unexpected error occurred', error: error.message });
        } else {
            console.error("Unknown error in getAllEmployees:", error);
            res.status(500).json({ message: 'An unexpected error occurred' });
        }
    }
}
