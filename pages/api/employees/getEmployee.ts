// pages/api/employees/getEmployee.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import Employee from '@/app/models/Employee';

export default async function getEmployee(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    try {
        await dbConnect();

        const { id } = req.query;
        if (!id || typeof id !== 'string') {
            return res.status(400).json({ message: 'Invalid Employee ID' });
        }

        const employee = await Employee.findById(id).populate('department');
        if (!employee) {
            return res.status(404).json({ message: 'Employee not found' });
        }

        res.status(200).json({ employee });
    } catch (error) {
        console.error("Error in getEmployee:", error);
        res.status(500).json({ message: 'An unexpected error occurred', error });
    }
}
