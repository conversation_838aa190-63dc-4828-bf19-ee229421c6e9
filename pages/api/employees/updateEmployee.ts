// // pages/api/employees/updateEmployee.ts
// import type { NextApiRequest, NextApiResponse } from 'next';
// import dbConnect from '@/lib/mongoose';
// import Employee from '@/app/models/Employee';

// export default async function updateEmployee(req: NextApiRequest, res: NextApiResponse) {
//     if (req.method !== 'PUT') {
//         res.setHeader('Allow', ['PUT']);
//         return res.status(405).end(`Method ${req.method} Not Allowed`);
//     }

//     try {
//         await dbConnect();

//         const { id, firstName, lastName, jobTitle, salary, hireDate, department, email, phone, gender, dateOfBirth, homeOrigin, residence, maritalStatus, nextOfKin, nextOfKinContact, status } = req.body;

//         if (!id) {
//             return res.status(400).json({ message: 'ID is required' });
//         }

//         const employee = await Employee.findById(id);

//         if (!employee) {
//             return res.status(404).json({ message: 'Employee not found' });
//         }

//         if (firstName !== undefined) employee.firstName = firstName;
//         if (lastName !== undefined) employee.lastName = lastName;
//         if (jobTitle !== undefined) employee.jobTitle = jobTitle;
//         if (salary !== undefined) employee.salary = salary;
//         if (hireDate !== undefined) employee.hireDate = hireDate;
//         if (department !== undefined) employee.department = department;
//         if (email !== undefined) employee.email = email;
//         if (phone !== undefined) employee.phone = phone;
//         if (gender !== undefined) employee.gender = gender;
//         if (dateOfBirth !== undefined) employee.dateOfBirth = dateOfBirth;
//         if (homeOrigin !== undefined) employee.homeOrigin = homeOrigin;
//         if (residence !== undefined) employee.residence = residence;
//         if (maritalStatus !== undefined) employee.maritalStatus = maritalStatus;
//         if (nextOfKin !== undefined) employee.nextOfKin = nextOfKin;
//         if (nextOfKinContact !== undefined) employee.nextOfKinContact = nextOfKinContact;
//         if (status !== undefined) employee.status = status;


//         await employee.save();

//         res.status(200).json({ message: 'Employee updated successfully', employee });
//     } catch (error) {
//         console.error("Error in updateEmployee:", error);
//         res.status(500).json({ message: 'An unexpected error occurred', error });
//     }
// }





// pages/api/employees/updateEmployee.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import Employee from '@/app/models/Employee';

export default async function updateEmployee(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'PUT') {
        res.setHeader('Allow', ['PUT']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    try {
        await dbConnect();

        const { id, ...updateData } = req.body;

        if (!id) {
            return res.status(400).json({ message: 'ID is required' });
        }

        const employee = await Employee.findByIdAndUpdate(id, updateData, { new: true }).populate('department');

        if (!employee) {
            return res.status(404).json({ message: 'Employee not found' });
        }

        res.status(200).json({ message: 'Employee updated successfully', employee });
    } catch (error) {
        console.error("Error in updateEmployee:", error);
        res.status(500).json({ message: 'An unexpected error occurred', error });
    }
}


// import type { NextApiRequest, NextApiResponse } from 'next';
// import dbConnect from '@/lib/mongoose';
// import Employee from '@/app/models/Employee';

// export default async function updateEmployee(req: NextApiRequest, res: NextApiResponse) {
//     if (req.method !== 'PUT') {
//         res.setHeader('Allow', ['PUT']);
//         return res.status(405).end(`Method ${req.method} Not Allowed`);
//     }

//     try {
//         await dbConnect();
//         const { id, ...updateData } = req.body;

//         const employee = await Employee.findByIdAndUpdate(id, updateData, { new: true }).populate('department');

//         if (!employee) {
//             return res.status(404).json({ message: 'Employee not found' });
//         }

//         res.status(200).json({ message: 'Employee updated successfully', employee });
//     } catch (error) {
//         console.error("Error in updateEmployee:", error);
//         res.status(500).json({ message: 'An unexpected error occurred', error });
//     }
// }
