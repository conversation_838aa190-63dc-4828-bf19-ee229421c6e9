// // pages/api/employees/receiveEmployee.ts
// import type { NextApiRequest, NextApiResponse } from 'next';
// import dbConnect from '@/lib/mongoose';
// import Employee from '@/app/models/Employee';

// export default async function receiveEmployee(req: NextApiRequest, res: NextApiResponse) {
//     if (req.method !== 'POST') {
//         res.setHeader('Allow', ['POST']);
//         return res.status(405).end(`Method ${req.method} Not Allowed`);
//     }

//     try {
//         await dbConnect();

//         const {
//             firstname,
//             lastname,
//             phone,
//             gender,
//             age,
//             profile_image_base64,
//             employee_type,
//             jobTitle,
//             salary,
//             email,
//             hireDate,
//             dateOfBirth,
//             homeOrigin,
//             residence,
//             maritalStatus,
//             nextOfKin,
//             nextOfKinContact,
//             status,
//         } = req.body;

//         // Create the employee record with all fields optional
//         const employeeRecord = new Employee({
//             firstName: firstname?.trim(),  // Use optional chaining to handle undefined
//             lastName: lastname?.trim(),
//             phone: phone?.trim(),
//             gender: gender?.trim(),
//             age,
//             profile_image_base64,
//             employee_type: employee_type?.trim(),
//             jobTitle: jobTitle?.trim(),
//             salary,
//             email: email?.trim(),
//             hireDate: hireDate ? new Date(hireDate) : undefined,
//             dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined,
//             homeOrigin: homeOrigin?.trim(),
//             residence: residence?.trim(),
//             maritalStatus: maritalStatus?.trim(),
//             nextOfKin: nextOfKin?.trim(),
//             nextOfKinContact: nextOfKinContact?.trim(),
//             status: status?.trim(),
//         });

//         // Save the employee record to the database
//         await employeeRecord.save();

//         res.status(201).json({ message: 'Employee record received and saved successfully', employeeRecord });
//     } catch (error) {
//         if (error instanceof Error) {
//             console.error("Error in receiveEmployee:", error.message, error.stack);
//             res.status(500).json({ message: `An unexpected error occurred: ${error.message}`, stack: error.stack });
//         } else {
//             console.error("Unknown error in receiveEmployee:", error);
//             res.status(500).json({ message: 'An unexpected error occurred' });
//         }
//     }
// }


// // pages/api/employees/receiveEmployee.ts
// import type { NextApiRequest, NextApiResponse } from 'next';
// import dbConnect from '@/lib/mongoose';
// import Employee from '@/app/models/Employee';

// export default async function receiveEmployee(req: NextApiRequest, res: NextApiResponse) {
//     if (req.method !== 'POST') {
//         res.setHeader('Allow', ['POST']);
//         return res.status(405).end(`Method ${req.method} Not Allowed`);
//     }

//     try {
//         await dbConnect();

//         const {
//             employeeIdentity,
//             firstname,
//             lastname,
//             phone,
//             gender,
//             age,
//             profile_image_base64,
//             employee_type,
//             jobTitle,
//             salary,
//             email,
//             hireDate,
//             dateOfBirth,
//             homeOrigin,
//             residence,
//             maritalStatus,
//             nextOfKin,
//             nextOfKinContact,
//             status,
//         } = req.body;

//         // Check if an employee with the same email already exists
//         const existingEmployee = await Employee.findOne({ email: email?.trim() });

//         if (existingEmployee) {
//             // If the employee exists, update the existing record
//             existingEmployee.employeeIdentity = employeeIdentity;
//             existingEmployee.firstName = firstname?.trim();
//             existingEmployee.lastName = lastname?.trim();
//             existingEmployee.phone = phone?.trim();
//             existingEmployee.gender = gender?.trim();
//             existingEmployee.age = age;
//             existingEmployee.profile_image_base64 = profile_image_base64;
//             existingEmployee.employee_type = employee_type?.trim();
//             existingEmployee.jobTitle = jobTitle?.trim();
//             existingEmployee.salary = salary;
//             existingEmployee.hireDate = hireDate ? new Date(hireDate) : undefined;
//             existingEmployee.dateOfBirth = dateOfBirth ? new Date(dateOfBirth) : undefined;
//             existingEmployee.homeOrigin = homeOrigin?.trim();
//             existingEmployee.residence = residence?.trim();
//             existingEmployee.maritalStatus = maritalStatus?.trim();
//             existingEmployee.nextOfKin = nextOfKin?.trim();
//             existingEmployee.nextOfKinContact = nextOfKinContact?.trim();
//             existingEmployee.status = status?.trim();

//             await existingEmployee.save();

//             res.status(200).json({ message: 'Employee record updated successfully', employeeRecord: existingEmployee });
//         } else {
//             // If the employee doesn't exist, create a new record
//             const employeeRecord = new Employee({
//                 employeeIdentity,
//                 firstName: firstname?.trim(),
//                 lastName: lastname?.trim(),
//                 phone: phone?.trim(),
//                 gender: gender?.trim(),
//                 age,
//                 profile_image_base64,
//                 employee_type: employee_type?.trim(),
//                 jobTitle: jobTitle?.trim(),
//                 salary,
//                 email: email?.trim(),
//                 hireDate: hireDate ? new Date(hireDate) : undefined,
//                 dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined,
//                 homeOrigin: homeOrigin?.trim(),
//                 residence: residence?.trim(),
//                 maritalStatus: maritalStatus?.trim(),
//                 nextOfKin: nextOfKin?.trim(),
//                 nextOfKinContact: nextOfKinContact?.trim(),
//                 status: status?.trim(),
//             });

//             await employeeRecord.save();

//             res.status(201).json({ message: 'Employee record created successfully', employeeRecord });
//         }
//     } catch (error) {
//         if (error instanceof Error) {
//             console.error("Error in receiveEmployee:", error.message, error.stack);
//             res.status(500).json({ message: `An unexpected error occurred: ${error.message}`, stack: error.stack });
//         } else {
//             console.error("Unknown error in receiveEmployee:", error);
//             res.status(500).json({ message: 'An unexpected error occurred' });
//         }
//     }
// }




// pages/api/employees/receiveEmployee.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import Employee from '@/app/models/Employee';

export default async function receiveEmployee(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    try {
        await dbConnect();

        const {
            employeeIdentity, // Make sure to include this in the API handler
            firstname,
            lastname,
            phone,
            gender,
            age,
            profile_image_base64,
            employee_type,
            jobTitle,
            salary,
            email,
            hireDate,
            dateOfBirth,
            homeOrigin,
            residence,
            maritalStatus,
            nextOfKin,
            nextOfKinContact,
            status,
        } = req.body;

        // Create the employee record with all fields optional
        const employeeRecord = new Employee({
            employeeIdentity, // Include employeeIdentity in the document
            firstName: firstname?.trim(),
            lastName: lastname?.trim(),
            phone: phone?.trim(),
            gender: gender?.trim(),
            age,
            profile_image_base64,
            employee_type: employee_type?.trim(),
            jobTitle: jobTitle?.trim(),
            salary,
            email: email?.trim(),
            hireDate: hireDate ? new Date(hireDate) : undefined,
            dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined,
            homeOrigin: homeOrigin?.trim(),
            residence: residence?.trim(),
            maritalStatus: maritalStatus?.trim(),
            nextOfKin: nextOfKin?.trim(),
            nextOfKinContact: nextOfKinContact?.trim(),
            status: status?.trim(),
        });

        // Save the employee record to the database
        await employeeRecord.save();

        res.status(201).json({ message: 'Employee record received and saved successfully', employeeRecord });
    } catch (error) {
        if (error instanceof Error) {
            console.error("Error in receiveEmployee:", error.message, error.stack);
            res.status(500).json({ message: `An unexpected error occurred: ${error.message}`, stack: error.stack });
        } else {
            console.error("Unknown error in receiveEmployee:", error);
            res.status(500).json({ message: 'An unexpected error occurred' });
        }
    }
}
