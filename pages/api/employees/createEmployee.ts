// pages/api/employees/createEmployee.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import Employee from '@/app/models/Employee';

export default async function createEmployee(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    try {
        await dbConnect();

        const { firstName, lastName, jobTitle, salary, hireDate, department, email, phone, gender, dateOfBirth, homeOrigin, residence, maritalStatus, nextOfKin, nextOfKinContact, status } = req.body;

        if (!firstName || !lastName || !jobTitle || !salary || !hireDate || !department || !email || !phone || !status) {
            return res.status(400).json({ message: 'Required fields are missing' });
        }

        const employee = new Employee({
            firstName, lastName, jobTitle, salary, hireDate, department, email, phone, gender, dateOfBirth, homeOrigin, residence, maritalStatus, nextOfKin, nextOfKinContact, status
        });
        await employee.save();

        res.status(201).json({ message: 'Employee created successfully', employee });
    } catch (error) {
        console.error("Error in createEmployee:", error);
        res.status(500).json({ message: 'An unexpected error occurred', error });
    }
}



// import type { NextApiRequest, NextApiResponse } from 'next';
// import dbConnect from '@/lib/mongoose';
// import Employee from '@/app/models/Employee';

// export default async function createEmployee(req: NextApiRequest, res: NextApiResponse) {
//     if (req.method !== 'POST') {
//         res.setHeader('Allow', ['POST']);
//         return res.status(405).end(`Method ${req.method} Not Allowed`);
//     }

//     try {
//         await dbConnect();
//         const { firstName, lastName, jobTitle, salary, hireDate, department, email, phone, gender, dateOfBirth, homeOrigin, residence, maritalStatus, nextOfKin, nextOfKinContact } = req.body;

//         const employee = new Employee({
//             firstName, lastName, jobTitle, salary, hireDate, department, email, phone, gender, dateOfBirth, homeOrigin, residence, maritalStatus, nextOfKin, nextOfKinContact
//         });

//         await employee.save();

//         res.status(201).json({ message: 'Employee created successfully', employee });
//     } catch (error) {
//         console.error("Error in createEmployee:", error);
//         res.status(500).json({ message: 'An unexpected error occurred', error });
//     }
// }
