// pages/api/attendance/getAllAttendance.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import Attendance from '@/app/models/Attendance';
import Employee from '@/app/models/Employee';
import Shift from '@/app/models/Shift';

export default async function getAllAttendance(req: NextApiRequest, res: NextApiResponse) {
    try {
        await dbConnect();

        console.log("Fetching attendance records...");

        // Attempt to find and populate attendance records
        const attendanceRecords = await Attendance.find()
            .populate({
                path: 'employee',
                select: '_id firstName lastName', // Populate only the necessary fields
                model: Employee // Ensure the correct model is referenced
            })
            .populate({
                path: 'shift',
                select: '_id name startTime endTime', // Populate only the necessary fields
                model: Shift // Ensure the correct model is referenced
            });

        console.log("Attendance records fetched:", attendanceRecords.length);

        res.status(200).json({ attendanceRecords });
    } catch (error) {
        if (error instanceof Error) {
            console.error("Error in getAllAttendance:", error.message, error.stack);
            res.status(500).json({ message: 'An unexpected error occurred', error: error.message });
        } else {
            console.error("Unknown error in getAllAttendance:", error);
            res.status(500).json({ message: 'An unexpected error occurred' });
        }
    }
}
