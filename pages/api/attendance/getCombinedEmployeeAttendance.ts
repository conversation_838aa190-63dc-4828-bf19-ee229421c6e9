import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import Attendance from '@/app/models/Attendance';
import Employee from '@/app/models/Employee';

export default async function getCombinedEmployeeAttendance(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    try {
        await dbConnect();

        // Fetch all employees
        const employees = await Employee.find();

        // Fetch all attendances
        const attendances = await Attendance.find();

        // Combine employees with their respective attendance records based on employeeIdentity
        const combinedData = employees.map(employee => {
            // Filter attendances that match the employeeIdentity
            const employeeAttendances = attendances.filter(attendance => attendance.employee.employeeIdentity === employee.employeeIdentity);

            return {
                employee: {
                    _id: employee._id,
                    employeeIdentity: employee.employeeIdentity,
                    firstName: employee.firstName,
                    lastName: employee.lastName,
                    salary: employee.salary,
                    jobTitle: employee.jobTitle,
                    phone: employee.phone,
                    gender: employee.gender,
                    dateOfBirth: employee.dateOfBirth,
                    email: employee.email,
                    hireDate: employee.hireDate,
                    status: employee.status,
                    residence: employee.residence,
                    maritalStatus: employee.maritalStatus,
                    nextOfKin: employee.nextOfKin,
                    nextOfKinContact: employee.nextOfKinContact
                },
                attendances: employeeAttendances.map(attendance => ({
                    _id: attendance._id,
                    date: attendance.date,
                    isPresent: attendance.isPresent,
                    clockInTime: attendance.clockInTime,
                    clockOutTime: attendance.clockOutTime,
                    shift: attendance.shift,
                    salary: attendance.salary
                }))
            };
        });

        res.status(200).json({ combinedData });
    } 
    
    // catch (error) {
    //     console.error("Error in getCombinedEmployeeAttendance:", error);
    //     res.status(500).json({ message: 'An unexpected error occurred', error: error.message });
    // }

    catch (error) {
        if (error instanceof Error) {
            console.error("Error in getCombinedEmployeeAttendance:", error.message, error.stack);
            res.status(500).json({ message: `An unexpected error occurred: ${error.message}`, stack: error.stack });
        } else {
            console.error("Unknown error in getCombinedEmployeeAttendance:", error);
            res.status(500).json({ message: 'An unexpected error occurred' });
        }
    }
}
