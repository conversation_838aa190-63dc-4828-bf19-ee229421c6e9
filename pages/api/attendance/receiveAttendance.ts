// pages/api/attendance/receiveAttendance.ts


import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import Attendance from '@/app/models/Attendance';
import { saveAttendancePayment } from '@/lib/saveAttendancePayment';
import { MongoServerError } from 'mongodb'; // Import MongoServerError for type checking

export default async function receiveAttendance(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    try {
        await dbConnect();

        const { _id, owner_id, employee, date, isPresent, clockInTime, clockOutTime, shift, salary } = req.body;

        const attendanceRecord = new Attendance({
            _id,
            owner_id,
            employee: {
                _id: employee._id,
                employeeIdentity: employee.employeeIdentity, // Save employeeIdentity
                firstName: employee.firstName,
                lastName: employee.lastName,
            },
            date: new Date(date),
            isPresent,
            clockInTime: clockInTime ? new Date(clockInTime) : undefined,
            clockOutTime: clockOutTime ? new Date(clockOutTime) : undefined,
            shift: {
                _id: shift._id,
                name: shift.name,
                startTime: new Date(shift.startTime),
                endTime: new Date(shift.endTime),
            },
            salary,
        });

        try {
            // Save the attendance record to the database
            await attendanceRecord.save();

            // Save the corresponding payment record
            const paymentRecord = await saveAttendancePayment(attendanceRecord);

            res.status(201).json({ message: 'Attendance and payment records saved successfully', attendanceRecord, paymentRecord });
        } catch (error) {
            // Check if error is an instance of MongoServerError
            if (error instanceof MongoServerError && error.code === 11000) {
                console.error('Duplicate key error:', error.message);
                res.status(400).json({ message: 'Attendance record with this ID already exists.' });
            } else if (error instanceof Error) {
                console.error('An unexpected error occurred:', error.message);
                res.status(500).json({ message: 'An unexpected error occurred', error: error.message });
            } else {
                throw error; // Re-throw if it's not an Error instance
            }
        }
    } catch (error) {
        if (error instanceof Error) {
            console.error("Error in receiveAttendance:", error.message, error.stack);
            res.status(500).json({ message: 'An unexpected error occurred', error: error.message });
        } else {
            console.error("Unknown error in receiveAttendance:", error);
            res.status(500).json({ message: 'An unexpected error occurred' });
        }
    }
}
