// // import type { NextApiRequest, NextApiResponse } from 'next';
// // import dbConnect from '@/lib/mongoose';
// // import Attendance from '@/app/models/Attendance';

// // export default async function getAttendanceByEmployee(req: NextApiRequest, res: NextApiResponse) {
// //     if (req.method !== 'GET') {
// //         res.setHeader('Allow', ['GET']);
// //         return res.status(405).end(`Method ${req.method} Not Allowed`);
// //     }

// //     try {
// //         await dbConnect();

// //         const { employeeIdentity } = req.query;

// //         if (!employeeIdentity || typeof employeeIdentity !== 'string') {
// //             return res.status(400).json({ message: 'Employee identity is required and must be a string' });
// //         }

// //         // Fetch attendance records for the employee with the specified employeeIdentity
// //         const attendanceRecords = await Attendance.find({ 'employee.employeeIdentity': employeeIdentity });

// //         if (attendanceRecords.length === 0) {
// //             return res.status(404).json({ message: 'No attendance records found for this employee' });
// //         }

// //         res.status(200).json({ attendances: attendanceRecords });
// //     } catch (error) {
// //         if (error instanceof Error) {
// //             console.error("Error in getAttendanceByEmployee:", error.message, error.stack);
// //             res.status(500).json({ message: `An unexpected error occurred: ${error.message}`, stack: error.stack });
// //         } else {
// //             console.error("Unknown error in getAttendanceByEmployee:", error);
// //             res.status(500).json({ message: 'An unexpected error occurred' });
// //         }
// //     }
// // }



// import type { NextApiRequest, NextApiResponse } from 'next';
// import dbConnect from '@/lib/mongoose';
// import Attendance from '@/app/models/Attendance';

// export default async function getAttendanceByEmployee(req: NextApiRequest, res: NextApiResponse) {
//     if (req.method !== 'POST') {
//         res.setHeader('Allow', ['POST']);
//         return res.status(405).end(`Method ${req.method} Not Allowed`);
//     }

//     try {
//         await dbConnect();

//         const { employeeIdentity } = req.body;

//         if (!employeeIdentity || typeof employeeIdentity !== 'string') {
//             return res.status(400).json({ message: 'Employee identity is required and must be a string' });
//         }

//         // Fetch attendance records for the employee with the specified employeeIdentity
//         const attendanceRecords = await Attendance.find({ 'employee.employeeIdentity': employeeIdentity });

//         if (attendanceRecords.length === 0) {
//             return res.status(404).json({ message: 'No attendance records found for this employee' });
//         }

//         res.status(200).json({ attendances: attendanceRecords });
//     } catch (error) {
//         if (error instanceof Error) {
//             console.error("Error in getAttendanceByEmployee:", error.message, error.stack);
//             res.status(500).json({ message: `An unexpected error occurred: ${error.message}`, stack: error.stack });
//         } else {
//             console.error("Unknown error in getAttendanceByEmployee:", error);
//             res.status(500).json({ message: 'An unexpected error occurred' });
//         }
//     }
// }




import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import Attendance from '@/app/models/Attendance';

export default async function getAttendanceByEmployee(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    try {
        await dbConnect();

        const { employeeIdentity } = req.body;

        console.log('Received employeeIdentity:', employeeIdentity); // Log the received employeeIdentity

        if (!employeeIdentity || typeof employeeIdentity !== 'string') {
            console.error('Invalid employeeIdentity:', employeeIdentity);
            return res.status(400).json({ message: 'Employee identity is required and must be a string' });
        }

        // Fetch attendance records for the employee with the specified employeeIdentity
        const attendanceRecords = await Attendance.find({ 'employee.employeeIdentity': employeeIdentity });

        if (attendanceRecords.length === 0) {
            return res.status(404).json({ message: 'No attendance records found for this employee' });
        }

        res.status(200).json({ attendances: attendanceRecords });
    } catch (error) {
        if (error instanceof Error) {
            console.error("Error in getAttendanceByEmployee:", error.message, error.stack);
            res.status(500).json({ message: `An unexpected error occurred: ${error.message}`, stack: error.stack });
        } else {
            console.error("Unknown error in getAttendanceByEmployee:", error);
            res.status(500).json({ message: 'An unexpected error occurred' });
        }
    }
}
