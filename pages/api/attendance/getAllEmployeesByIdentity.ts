import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import Employee from '@/app/models/Employee';

export default async function getAllEmployeesByIdentity(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    try {
        await dbConnect();

        // Fetch all employee records with specific fields
        const employees = await Employee.find({}, 'employeeIdentity firstName lastName salary');

        res.status(200).json({ employees });
        
    } catch (error) {
        if (error instanceof Error) {
            console.error("Error in getAllEmployeesByIdentity:", error.message, error.stack);
            res.status(500).json({ message: `An unexpected error occurred: ${error.message}`, stack: error.stack });
        } else {
            console.error("Unknown error in getAllEmployeesByIdentity:", error);
            res.status(500).json({ message: 'An unexpected error occurred' });
        }
    }
}
