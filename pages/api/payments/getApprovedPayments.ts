import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import Payment from '@/app/models/Payment';

export default async function getApprovedPayments(req: NextApiRequest, res: NextApiResponse) {
    try {
        await dbConnect();

        const approvedPayments = await Payment.find({ status: 'approved' }).populate('employee');
        res.status(200).json({ payments: approvedPayments });
    } catch (error) {
        console.error("Error in getApprovedPayments:", error);
        res.status(500).json({ message: 'An unexpected error occurred', error });
    }
}
