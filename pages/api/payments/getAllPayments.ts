

// pages/api/payments/getAllPayments.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import Payment from '@/app/models/Payment';

export default async function getAllPayments(req: NextApiRequest, res: NextApiResponse) {
    try {
        await dbConnect();

        const payments = await Payment.find().populate('employee');
        res.status(200).json({ payments });
    } catch (error) {
        console.error("Error in getAllPayments:", error);
        res.status(500).json({ message: 'An unexpected error occurred', error });
    }
}
