// // pages/api/payments/updatePayment.ts
// import type { NextApiRequest, NextApiResponse } from 'next';
// import dbConnect from '@/lib/mongoose';
// import Payment from '@/app/models/Payment';

// export default async function updatePayment(req: NextApiRequest, res: NextApiResponse) {
//     if (req.method !== 'PUT') {
//         res.setHeader('Allow', ['PUT']);
//         return res.status(405).end(`Method ${req.method} Not Allowed`);
//     }

//     try {
//         await dbConnect();

//         const { id, ...updateData } = req.body;

//         if (!id) {
//             return res.status(400).json({ message: 'ID is required' });
//         }

//         const payment = await Payment.findByIdAndUpdate(id, updateData, { new: true }).populate('employee');

//         if (!payment) {
//             return res.status(404).json({ message: 'Payment not found' });
//         }

//         res.status(200).json({ message: 'Payment updated successfully', payment });
//     } catch (error) {
//         console.error("Error in updatePayment:", error);
//         res.status(500).json({ message: 'An unexpected error occurred', error });
//     }
// }



// pages/api/payments/updatePayment.ts

import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import Payment from '@/app/models/Payment';

export default async function updatePayment(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'PUT') {
        res.setHeader('Allow', ['PUT']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    try {
        await dbConnect();

        const { id } = req.query; // Get id from the query string
        const updateData = req.body;

        if (!id) {
            return res.status(400).json({ message: 'ID is required' });
        }

        const payment = await Payment.findByIdAndUpdate(id, updateData, { new: true }).populate('employee');

        if (!payment) {
            return res.status(404).json({ message: 'Payment not found' });
        }

        res.status(200).json({ message: 'Payment updated successfully', payment });
    } catch (error) {
        console.error("Error in updatePayment:", error);
        res.status(500).json({ message: 'An unexpected error occurred', error });
    }
}
