// pages/api/payments/deletePayment.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import Payment from '@/app/models/Payment';

export default async function deletePayment(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'DELETE') {
        res.setHeader('Allow', ['DELETE']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    try {
        await dbConnect();
        const { id } = req.body; // Get ID from the request body

        if (!id) {
            return res.status(400).json({ message: 'ID is required' });
        }

        const payment = await Payment.findByIdAndDelete(id);

        if (!payment) {
            return res.status(404).json({ message: 'Payment not found' });
        }

        res.status(200).json({ message: 'Payment deleted successfully' });
    } catch (error) {
        console.error("Error in deletePayment:", error);
        res.status(500).json({ message: 'An unexpected error occurred', error });
    }
}
