
// pages/api/payments/createPayment.ts


import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import Payment from '@/app/models/Payment';
import Attendance from '@/app/models/Attendance';
import Employee from '@/app/models/Employee';
import { calculateTax } from '@/lib/taxCalculation';

export default async function createPayment(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    try {
        await dbConnect();

        const { 
            employeeIdentity, 
            month, 
            year, 
            bonus_payment = '0', 
            loan_deduction = {}, 
            insurance = '0', 
            final_amount = '0', 
            tax, 
            day_rate, 
            working_days, 
            attendance_days 
        } = req.body;

        console.log("Request body:", req.body);

        // Validate employeeIdentity
        if (!employeeIdentity || typeof employeeIdentity !== 'string') {
            console.error('Invalid or missing employeeIdentity:', employeeIdentity);
            return res.status(400).json({ message: 'Invalid or missing employeeIdentity' });
        }

        // Validate month and year
        if (!month || !year) {
            console.error('Month and year are required:', { month, year });
            return res.status(400).json({ message: 'Month and year are required' });
        }

        // Find the employee by their employeeIdentity
        const employeeRecord = await Employee.findOne({ employeeIdentity });

        if (!employeeRecord) {
            console.error('Employee not found:', employeeIdentity);
            return res.status(404).json({ message: 'Employee not found' });
        }

        console.log(`Employee found: ${employeeRecord.firstName} ${employeeRecord.lastName}`);

        // Retrieve attendance records for the employee based on the given month and year
        const startDate = new Date(year, month - 1, 1);
        const endDate = new Date(year, month, 0);

        console.log(`Fetching attendance records between: ${startDate} and ${endDate}`);

        const attendanceRecords = await Attendance.find({
            'employee.employeeIdentity': employeeIdentity,
            date: {
                $gte: startDate,  // First day of the month
                $lte: endDate,    // Last day of the month
            },
            isPresent: true
        });

        console.log(`Found ${attendanceRecords.length} attendance records for employee ${employeeIdentity}`);

        // Validate that attendance records were found
        if (attendanceRecords.length === 0) {
            console.error('No attendance records found for this employee:', employeeIdentity);
            return res.status(404).json({ message: 'No attendance records found for this employee' });
        }

        // Handle loan_deduction.extra properly by providing default values if necessary
        const defaultExtraLoan = [{ type: 'N/A', amount: '0' }];  // Default structure
        const loanDeductions = {
            ...loan_deduction,
            extra: loan_deduction.extra?.map((extraItem: { type: any; amount: any; }) => ({
                type: extraItem.type || 'N/A',  // Provide default 'type' if missing
                amount: extraItem.amount || '0' // Provide default 'amount' if missing
            })) || defaultExtraLoan
        };

        // Calculate the pension deduction if not provided in the request
        const calculatedDayRate = parseFloat(day_rate) * attendance_days;
        const calculatedPensionDeduction = (calculatedDayRate * 0.07).toFixed(2);

        // Prepare the payment record
        const paymentRecord = new Payment({
            amount: calculatedDayRate.toFixed(2),
            bonus_payment,
            status: 'pending',
            employee: employeeRecord._id,
            tax: tax || calculateTax(calculatedDayRate).toString(), // Calculate tax if not provided
            loan_deduction: loanDeductions,  // Use the processed loan_deduction
            insurance,
            pension_deduction: calculatedPensionDeduction,
            final_amount,
            attendances: attendanceRecords.map(a => a._id), // Link attendance records
            day_rate,
            working_days,
            attendance_days
        });

        console.log("Saving payment record:", paymentRecord);

        // Save the payment record
        await paymentRecord.save();

        // Respond with success message
        res.status(201).json({ message: 'Payment created successfully', paymentRecord });
    } catch (error) {
        if (error instanceof Error) {
            console.error("Error in createPayment:", error.message, error.stack);
            res.status(500).json({ message: `An unexpected error occurred: ${error.message}`, stack: error.stack });
        } else {
            console.error("Unknown error in createPayment:", error);
            res.status(500).json({ message: 'An unexpected error occurred' });
        }
    }
}
