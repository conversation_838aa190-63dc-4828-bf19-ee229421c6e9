// import type { NextApiRequest, NextApiResponse } from 'next';
// import dbConnect from '@/lib/mongoose';
// import Payment from '@/app/models/Payment';
// import { createPDF } from '@/lib/pdfGenerator';
// import { Readable } from 'stream';

// export default async function downloadPayslip(req: NextApiRequest, res: NextApiResponse) {
//     try {
//         await dbConnect();

//         const { paymentId } = req.query;
//         if (!paymentId) {
//             return res.status(400).json({ message: 'Payment ID is required' });
//         }

//         const payment = await Payment.findById(paymentId).populate('employee');
//         if (!payment) {
//             return res.status(404).json({ message: 'Payment not found' });
//         }

//         const pdfBuffer = await createPDF(payment);

//         res.setHeader('Content-Disposition', `attachment; filename=payslip_${payment._id}.pdf`);
//         res.setHeader('Content-Type', 'application/pdf');

//         const stream = new Readable();
//         stream.push(pdfBuffer);
//         stream.push(null);
//         stream.pipe(res);
//     } catch (error) {
//         console.error("Error in downloadPayslip:", error);
//         res.status(500).json({ message: 'An unexpected error occurred', error });
//     }
// }



// import type { NextApiRequest, NextApiResponse } from 'next';
// import dbConnect from '@/lib/mongoose';
// import Payment from '@/app/models/Payment';
// import { createPDF } from '@/lib/pdfGenerator';
// import { Readable } from 'stream';

// export default async function downloadPayslip(req: NextApiRequest, res: NextApiResponse) {
//     try {
//         await dbConnect();

//         const { paymentId } = req.query;
//         if (!paymentId) {
//             return res.status(400).json({ message: 'Payment ID is required' });
//         }

//         const payment = await Payment.findById(paymentId).populate('employee');
//         if (!payment) {
//             return res.status(404).json({ message: 'Payment not found' });
//         }

//         const pdfBuffer = await createPDF(payment);

//         res.setHeader('Content-Disposition', `attachment; filename=payslip_${payment._id}.pdf`);
//         res.setHeader('Content-Type', 'application/pdf');

//         const stream = new Readable();
//         stream.push(pdfBuffer);
//         stream.push(null);
//         stream.pipe(res);
//     } catch (error) {
//         console.error("Error in downloadPayslip:", error);
//         res.status(500).json({ message: 'An unexpected error occurred', error });
//     }
// }
