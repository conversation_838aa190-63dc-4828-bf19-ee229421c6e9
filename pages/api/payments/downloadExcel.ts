// import { NextApiRequest, NextApiResponse } from 'next';
// import { utils, write } from 'xlsx';
// import { Payment } from '@/app/types/payment';

// export default async function handler(req: NextApiRequest, res: NextApiResponse) {
//   if (req.method === 'POST') {
//     const { selectedColumns, data }: { selectedColumns: string[], data: Payment[] } = req.body;

//     const filteredData = data.map(item => {
//       const filteredItem: any = {};
//       selectedColumns.forEach(column => {
//         filteredItem[column] = item[column];
//       });
//       return filteredItem;
//     });

//     const worksheet = utils.json_to_sheet(filteredData);
//     const workbook = utils.book_new();
//     utils.book_append_sheet(workbook, worksheet, 'Report');

//     const buffer = write(workbook, { type: 'buffer', bookType: 'xlsx' });

//     res.setHeader('Content-Disposition', 'attachment; filename="report.xlsx"');
//     res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
//     res.send(buffer);
//   } else {
//     res.status(405).end(); // Method Not Allowed
//   }
// }

// import { NextApiRequest, NextApiResponse } from 'next';
// import { utils, write } from 'xlsx';
// import dbConnect from '@/lib/mongoose';
// import Payment from '@/app/models/Payment';

// export default async function handler(req: NextApiRequest, res: NextApiResponse) {
//   if (req.method === 'POST') {
//     const { selectedColumns }: { selectedColumns: string[] } = req.body;

//     await dbConnect();
//     const payments = await Payment.find({}).lean(); // Fetch all payment documents

//     const filteredData = payments.map(item => {
//       const filteredItem: any = {};
//       selectedColumns.forEach(column => {
//         filteredItem[column] = item[column];
//       });
//       return filteredItem;
//     });

//     const worksheet = utils.json_to_sheet(filteredData);
//     const workbook = utils.book_new();
//     utils.book_append_sheet(workbook, worksheet, 'Report');

//     const buffer = write(workbook, { type: 'buffer', bookType: 'xlsx' });

//     res.setHeader('Content-Disposition', 'attachment; filename="report.xlsx"');
//     res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
//     res.send(buffer);
//   } else {
//     res.status(405).end(); // Method Not Allowed
//   }
// }


import { NextApiRequest, NextApiResponse } from 'next';
import { utils, write } from 'xlsx';
import dbConnect from '@/lib/mongoose';
import Payment from '@/app/models/Payment';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    const { selectedColumns }: { selectedColumns: string[] } = req.body;

    await dbConnect();
    const payments = await Payment.find({}).populate('employee').lean();

    const formattedData = payments.map(payment => {
      const formattedPayment: any = {};
      selectedColumns.forEach(column => {
        if (column.includes('.')) {
          const [parent, child] = column.split('.');
          formattedPayment[column] = payment[parent] && payment[parent][child];
        } else {
          formattedPayment[column] = payment[column];
        }
      });
      return formattedPayment;
    });

    const worksheet = utils.json_to_sheet(formattedData);
    const workbook = utils.book_new();
    utils.book_append_sheet(workbook, worksheet, 'Report');

    const buffer = write(workbook, { type: 'buffer', bookType: 'xlsx' });

    res.setHeader('Content-Disposition', 'attachment; filename="report.xlsx"');
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.send(buffer);
  } else {
    res.status(405).end(); // Method Not Allowed
  }
}
