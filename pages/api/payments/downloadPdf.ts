// import { NextApiRequest, NextApiResponse } from 'next';
// import dbConnect from '@/lib/mongoose';
// import Payment from '@/app/models/Payment';
// import { generate } from '@pdfme/generator';
// import fs from 'fs';
// import path from 'path';

// export default async function handler(req: NextApiRequest, res: NextApiResponse) {
//     if (req.method === 'POST') {
//         const { selectedColumns }: { selectedColumns: string[] } = req.body;

//         await dbConnect();
//         const payments = await Payment.find({}).lean(); // Fetch all payment documents

//         const filteredData = payments.map(item => {
//             const filteredItem: any = {};
//             selectedColumns.forEach(column => {
//                 filteredItem[column] = item[column];
//             });
//             return filteredItem;
//         });

//         // Define the template for PDF generation
//         const template = {
//             basePdf: fs.readFileSync(path.resolve('./path-to-your-template-pdf.pdf')),
//             schemas: [
//                 {
//                     firstName: { type: 'text', position: { x: 50, y: 50 }, width: 100, height: 10 },
//                     lastName: { type: 'text', position: { x: 50, y: 70 }, width: 100, height: 10 },
//                     final_payment: { type: 'text', position: { x: 50, y: 90 }, width: 100, height: 10 },
//                     status: { type: 'text', position: { x: 50, y: 110 }, width: 100, height: 10 },
//                     // Add other fields as necessary
//                 }
//             ]
//         };

//         // Prepare data for the template
//         const inputs = filteredData.map(data => ({
//             firstName: data.firstName,
//             lastName: data.lastName,
//             final_payment: data.final_payment.toString(),
//             status: data.status,
//             // Add other fields as necessary
//         }));

//         try {
//             const pdfBuffer = await generate({ template, inputs });
//             res.setHeader('Content-Disposition', 'attachment; filename="report.pdf"');
//             res.setHeader('Content-Type', 'application/pdf');
//             res.send(pdfBuffer);
//         } catch (error) {
//             console.error("Error generating PDF report:", error);
//             res.status(500).json({ error: 'Error generating PDF report' });
//         }
//     } else {
//         res.status(405).end(); // Method Not Allowed
//     }
// }



// import { NextApiRequest, NextApiResponse } from 'next';
// import puppeteer from 'puppeteer';
// import React from 'react';
// import ReactDOMServer from 'react-dom/server';
// // import { ServerStyleSheet, StyleSheetManager } from 'styled-components';
// import dbConnect from '@/lib/mongoose';
// import Payment from '@/app/models/Payment';
// import PdfTemplate from '@/components/PdfTemplate';
// import { ServerStyleSheet, StyleSheetManager as StyleSheetManagerComponent } from 'styled-components';

// export default async function handler(req: NextApiRequest, res: NextApiResponse) {
//     if (req.method === 'POST') {
//         const { selectedColumns }: { selectedColumns: string[] } = req.body;

//         await dbConnect();
//         const payments = await Payment.find({}).populate('employee').lean(); // Fetch all payment documents and populate employee details

//         const filteredData = payments.map(item => {
//             const filteredItem: any = {};
//             selectedColumns.forEach(column => {
//                 if (column.includes('.')) {
//                     const [parent, child] = column.split('.');
//                     filteredItem[column] = item[parent] && item[parent][child];
//                 } else {
//                     filteredItem[column] = item[column];
//                 }
//             });
//             return filteredItem;
//         });

//         // Generate HTML content
//         const sheet = new ServerStyleSheet();
//         const htmlContent = ReactDOMServer.renderToString(
//             <StyleSheetManagerComponent sheet={ sheet.instance } >
//         <div>
//         {
//             filteredData.map((payment, index) => (
//                 <PdfTemplate key= { index } payment = { payment } />
//                     ))
//     }
//     </div>
//         </StyleSheetManagerComponent>
//         );
//     const styleTags = sheet.getStyleTags();

//     const fullHtml = `
//             <html>
//             <head>
//                 <style>
//                     body { font-family: Arial, sans-serif; }
//                     ${styleTags}
//                 </style>
//             </head>
//             <body>
//                 ${htmlContent}
//             </body>
//             </html>
//         `;

//     try {
//         // Launch puppeteer
//         const browser = await puppeteer.launch();
//         const page = await browser.newPage();
//         await page.setContent(fullHtml);
//         const pdfBuffer = await page.pdf({ format: 'A4' });
//         await browser.close();

//         res.setHeader('Content-Disposition', 'attachment; filename="report.pdf"');
//         res.setHeader('Content-Type', 'application/pdf');
//         res.send(pdfBuffer);
//     } catch (error) {
//         console.error("Error generating PDF report:", error);
//         res.status(500).json({ error: 'Error generating PDF report' });
//     }
// } else {
//     res.status(405).end(); // Method Not Allowed
// }
// }
