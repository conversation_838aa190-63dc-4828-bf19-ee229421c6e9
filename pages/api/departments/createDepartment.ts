// // pages/api/departments/createDepartment.ts
// import type { NextApiRequest, NextApiResponse } from 'next';
// import dbConnect from '@/lib/mongoose';
// import Department from '@/app/models/Department';

// export default async function createDepartment(req: NextApiRequest, res: NextApiResponse) {
//     if (req.method !== 'POST') {
//         res.setHeader('Allow', ['POST']);
//         return res.status(405).end(`Method ${req.method} Not Allowed`);
//     }

//     try {
//         await dbConnect();

//         const { name, description } = req.body;

//         if (!name) {
//             return res.status(400).json({ message: 'Name is required' });
//         }

//         const department = new Department({ name, description });
//         await department.save();

//         res.status(201).json({ message: 'Department created successfully', department });
//     } catch (error) {
//         console.error("Error in createDepartment:", error);
//         res.status(500).json({ message: 'An unexpected error occurred', error });
//     }
// }


// pages/api/departments/createDepartment.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import Department from '@/app/models/Department';

export default async function createDepartment(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    try {
        await dbConnect();
        const { name, description } = req.body;

        if (!name || !description) {
            return res.status(400).json({ message: 'Name and description are required' });
        }

        const department = new Department({ name, description });
        await department.save();

        res.status(201).json({ message: 'Department created successfully', department });
    } catch (error) {
        console.error("Error in createDepartment:", error);
        res.status(500).json({ message: 'An unexpected error occurred', error });
    }
}
