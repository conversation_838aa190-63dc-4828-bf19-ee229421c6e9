// pages/api/departments/updateDepartment.ts


import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import Department from '@/app/models/Department';

export default async function updateDepartment(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'PUT') {
        res.setHeader('Allow', ['PUT']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    try {
        await dbConnect();
        const { id, name, description } = req.body;

        if (!id || (!name && !description)) {
            return res.status(400).json({ message: 'ID and at least one field to update are required' });
        }

        const updateData: Partial<{ name: string; description: string }> = {};
        if (name) updateData.name = name;
        if (description) updateData.description = description;

        const department = await Department.findByIdAndUpdate(id, updateData, { new: true });

        if (!department) {
            return res.status(404).json({ message: 'Department not found' });
        }

        res.status(200).json({ message: 'Department updated successfully', department });
    } catch (error) {
        console.error("Error in updateDepartment:", error);
        res.status(500).json({ message: 'An unexpected error occurred', error });
    }
}
