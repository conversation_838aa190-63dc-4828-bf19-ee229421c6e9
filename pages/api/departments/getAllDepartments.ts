// // pages/api/departments/getAllDepartments.ts
// import type { NextApiRequest, NextApiResponse } from 'next';
// import dbConnect from '@/lib/mongoose';
// import Department from '@/app/models/Department';

// export default async function getAllDepartments(req: NextApiRequest, res: NextApiResponse) {
//     if (req.method !== 'GET') {
//         res.setHeader('Allow', ['GET']);
//         return res.status(405).end(`Method ${req.method} Not Allowed`);
//     }

//     try {
//         await dbConnect();
//         const departments = await Department.find({}).sort({ _id: -1 }); // Sort by _id in descending order

//         res.status(200).json({ departments });
//     } catch (error) {
//         console.error("Error in getAllDepartments:", error);
//         res.status(500).json({ message: 'An unexpected error occurred', error });
//     }
// }


// // pages/api/departments/getAllDepartments.ts
// import type { NextApiRequest, NextApiResponse } from 'next';
// import dbConnect from '@/lib/mongoose';
// import Department from '@/app/models/Department';

// export default async function getAllDepartments(req: NextApiRequest, res: NextApiResponse) {
//     if (req.method !== 'GET') {
//         res.setHeader('Allow', ['GET']);
//         return res.status(405).end(`Method ${req.method} Not Allowed`);
//     }

//     try {
//         await dbConnect();
//         const departments = await Department.find({}).sort({ _id: -1 });

//         res.status(200).json({ departments });
//     } catch (error) {
//         console.error("Error in getAllDepartments:", error);
//         res.status(500).json({ message: 'An unexpected error occurred', error });
//     }
// }


// import type { NextApiRequest, NextApiResponse } from 'next';
// import dbConnect from '@/lib/mongoose';
// import Employee from '@/app/models/Employee';

// export default async function getAllEmployees(req: NextApiRequest, res: NextApiResponse) {
//     if (req.method !== 'GET') {
//         res.setHeader('Allow', ['GET']);
//         return res.status(405).end(`Method ${req.method} Not Allowed`);
//     }

//     try {
//         await dbConnect();
//         const employees = await Employee.find().populate('department');
//         res.status(200).json({ employees });
//     } catch (error) {
//         console.error("Error in getAllEmployees:", error);
//         res.status(500).json({ message: 'An unexpected error occurred', error });
//     }
// }


import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import Department from '@/app/models/Department';

export default async function getAllDepartments(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    try {
        await dbConnect();
        const departments = await Department.find();
        res.status(200).json({ departments });
    } catch (error) {
        console.error("Error in getAllDepartments:", error);
        res.status(500).json({ message: 'An unexpected error occurred', error });
    }
}
