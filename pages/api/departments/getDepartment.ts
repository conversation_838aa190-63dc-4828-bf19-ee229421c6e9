// pages/api/departments/getDepartment.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '@/lib/mongoose';
import Department from '@/app/models/Department';

export default async function getDepartment(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    try {
        await dbConnect();

        const { id } = req.query;
        if (!id || typeof id !== 'string') {
            return res.status(400).json({ message: 'Invalid Department ID' });
        }

        const department = await Department.findById(id);
        if (!department) {
            return res.status(404).json({ message: 'Department not found' });
        }

        res.status(200).json({ department });
    } catch (error) {
        console.error("Error in getDepartment:", error);
        res.status(500).json({ message: 'An unexpected error occurred', error });
    }
}
