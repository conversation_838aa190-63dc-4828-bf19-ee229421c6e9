
// components/UserRegistration.tsx
"use client";
import React, { useState, ChangeEvent, FormEvent } from 'react';
import { useRouter } from 'next/navigation';
import SuccessComponent from '@/components/SuccessComponent';
import LoadingSpinner from '@/components/LoadingSpinner';
import { UserRole } from '@/context/types'; 

interface FormData {
  username: string;
  email: string;
  password: string;
  role: UserRole; 
}

interface FormErrors {
  username?: string;
  email?: string;
  password?: string;
  role?: string;
  form?: string;
}

const UserRegistration: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({
    username: '',
    email: '',
    password: '',
    role: 'fieldsupervisor' // Default role
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const router = useRouter();

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    setErrors({ ...errors, [e.target.name]: '' });
  };

  const validateForm = (): boolean => {
    let isValid = true;
    let newErrors: FormErrors = {};

    if (!formData.username) {
      newErrors.username = 'Username is required';
      isValid = false;
    }
    if (!formData.email) {
      newErrors.email = 'Email is required';
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
      isValid = false;
    }
    if (!formData.password) {
      newErrors.password = 'Password is required';
      isValid = false;
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!validateForm()) return;
    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || 'Something went wrong');
      }

      setIsLoading(false);
      setIsSuccess(true);

      // Redirect to login page after successful registration
      setTimeout(() => {
        router.push('/dashboard');
      }, 2000);
    } catch (err) {
      if (err instanceof Error) {
        setErrors({ ...errors, form: err.message });
      }
      setIsLoading(false);
    }
  };

  if (isSuccess) {
    return <SuccessComponent title="Registration Successful" message="You have been successfully registered." />;
  }

  return (
    <div className='h-screen'>
      <div className="max-w-md mx-auto mt-20 mb-20 relative z-10 h-auto p-8 py-10 overflow-hidden bg-white border-b-2 rounded-lg shadow-2xl px-7">
        <form onSubmit={handleSubmit} className="bg-white rounded px-8 pt-6 pb-8 mb-4">
          {/* Username Field */}
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="username">
              Username
            </label>
            <input
              className="block w-full px-4 py-3 mb-2 border-2 border-gray-200 rounded-lg focus:ring focus:ring-blue-500 focus:outline-none"
              id="username" type="text" name="username" placeholder="Username" value={formData.username} onChange={handleChange} />
            {errors.username && <p className="text-red-500 text-xs italic">{errors.username}</p>}
          </div>

          {/* Email Field */}
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="email">
              Email
            </label>
            <input
              className="block w-full px-4 py-3 mb-2 border-2 border-gray-200 rounded-lg focus:ring focus:ring-blue-500 focus:outline-none"
              id="email" type="email" name="email" placeholder="Email" value={formData.email} onChange={handleChange} />
            {errors.email && <p className="text-red-500 text-xs italic">{errors.email}</p>}
          </div>

          {/* Password Field */}
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="password">
              Password
            </label>
            <input
              className="block w-full px-4 py-3 mb-2 border-2 border-gray-200 rounded-lg focus:ring focus:ring-blue-500 focus:outline-none"
              id="password" type="password" name="password" placeholder="Password" value={formData.password} onChange={handleChange} />
            {errors.password && <p className="text-red-500 text-xs italic">{errors.password}</p>}
          </div>

          {/* Role Selection */}
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="role">
              Role
            </label>
            <select
           
              className="shadow  rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              id="role" name="role" value={formData.role} onChange={handleChange}>
              <option value="admin">Admin</option>
              <option value="ceoadmin">ceoadmin</option>
              <option value="accountant">accountant</option>
              <option value="hrmanager">hrmanager</option>
              <option value="editor">Editor</option>
              <option value="accountant">Accountant</option>
              <option value="fieldsupervisor">fieldsupervisor</option>
              
              
              
            </select>
          </div>

          {/* Submit Button */}
          <div className="flex items-center justify-between">
            <button className="bg-green-700 hover:bg-orange-700 text-white font-bold py-2 w-full px-4 rounded focus:outline-none focus:shadow-outline" type="submit" disabled={isLoading}>
              {isLoading ? <LoadingSpinner /> : 'Register'}
            </button>
          </div>
          {errors.form && <p className="text-red-500 text-xs italic mt-4">{errors.form}</p>}
        </form>
      </div>
    </div>
  );
};

// Wrap UserRegistration component with withAuth HOC for admin access only
export default UserRegistration
