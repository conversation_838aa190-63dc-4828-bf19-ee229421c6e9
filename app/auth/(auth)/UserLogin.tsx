

//app/auth/(auth)/UserLogin.tsx
"use client";

import React, { useState, ChangeEvent, FormEvent } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import SuccessComponent from '@/components/SuccessComponent';
import LoadingSpinner from '@/components/LoadingSpinner';
import Cookies from 'js-cookie';

interface FormData {
  email: string;
  password: string;
}

interface FormErrors {
  email: string;
  password: string;
}

const UserLogin: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({ email: '', password: '' });
  const [errors, setErrors] = useState<FormErrors>({ email: '', password: '' });
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const { login, getUser } = useAuth();
  const router = useRouter();

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    setErrors({ ...errors, [e.target.name]: '' });
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setErrors({ email: '', password: '' });
    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      const data = await response.json();
      if (!response.ok) {
        if (data.message === 'Email not found') {
          setErrors({ ...errors, email: 'Email does not exist' });
        } else if (data.message === 'Incorrect password') {
          setErrors({ ...errors, password: 'Password is incorrect' });
        } else {
          throw new Error(data.message || 'Something went wrong');
        }
        setIsLoading(false);
        return;
      }

      login(data.token);
      Cookies.set('token', data.token); // Set the token in cookies
      setIsLoading(false);
      setIsSuccess(true);

      // Ensure user is set properly after login
      const user = getUser();
      if (!user) {
        throw new Error('Failed to get user after login');
      }

    } catch (err) {
      if (err instanceof Error) {
        console.error('Login Error:', err.message);
      } else {
        console.error('Login Error:', err);
      }
      setIsLoading(false);
    }
  };

  if (isSuccess) {
    return <SuccessComponent title="Login Successful" message="You have been successfully logged in." backUrl="/dashboard" />;
  }

  return (
    <div className="flex items-center justify-center bg-cover bg-center bg-gradient-to-b from-white">
      <div className="max-w-md mx-auto p-8 overflow-hidden bg-white border-b-2 border-gray-300 rounded-lg shadow-2xl">
        {/* <div className='flex justify-center'>
          <img src="/logo1.png" alt="Logo" className="h-40 w-40" />
        </div> */}
        <form onSubmit={handleSubmit} className="rounded w-96 px-8 pt-6 pb-8 mb-4">
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="email">
              Email
            </label>
            <input
              className="block w-full px-2 py-2 mb-2 border-2 border-gray-200 rounded-lg focus:ring focus:ring-blue-500 focus:outline-none"
              id="email" type="email" name="email" placeholder="Email" value={formData.email} onChange={handleChange} />
            {errors.email && <p className="text-red-500 text-xs italic">{errors.email}</p>}
          </div>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="password">
              Password
            </label>
            <input
              className="block w-full px-2 py-2 mb-2 border-2 border-gray-200 rounded-lg focus:ring focus:ring-blue-500 focus:outline-none"
              id="password" type="password" name="password" placeholder="Password" value={formData.password} onChange={handleChange} />
            {errors.password && <p className="text-red-500 text-xs italic">{errors.password}</p>}
          </div>
          <div className="flex items-center justify-between">
            <button className="bg-green-700 hover:bg-green-500 w-full text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline" type="submit" disabled={isLoading}>
              {isLoading ? <LoadingSpinner /> : 'Login'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UserLogin;
