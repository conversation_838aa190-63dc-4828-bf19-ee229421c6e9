import ThemeToggler from '@/components/ThemeToggler';

const AuthLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className='h-[100vh] flex items-center justify-center relative bg-gray-800 bg-cover bg-center bg-no-repeat' style={{ backgroundImage: 'url("/bg.jpg")' }}>
      <div className='absolute inset-0 bg-gradient-to-b from-black to-black opacity-20 z-10'></div>
      <div className='relative z-20 w-full max-w-md'>
        <div className='absolute bottom-5 right-0'>
          <ThemeToggler />
        </div>
        {children}
      </div>
    </div>
  );
};

export default AuthLayout;
