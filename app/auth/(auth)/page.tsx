import { AuthProvider } from '@/context/AuthContext';
import UserLogin from './UserLogin';
import UserRegistration from './UserRegistration';

const AuthPage = () => {
  return (
    <AuthProvider>
      <UserLogin />
      {/* <UserRegistration/> */}
    </AuthProvider>
  );
};

export default AuthPage;

// import AuthTabs from '@/components/auth/AuthTabs';
// // import React from 'react';
// import UserLogin from './UserLogin';
// import { AuthProvider } from '@/context/AuthContext';

// const AuthPage = () => {
//   return (
//     <>
//       <AuthProvider>
//         <section className="relative py-20 overflow-hidden bg-gray-800 bg-cover bg-center bg-no-repeat" style={{ backgroundImage: 'url("/bg.jpg")' }}>
//           {/* <div className="absolute inset-0 bg-gradient-to-b from-black to-black opacity-20 z-10"></div> */}
//           <div className='relative z-20 h-screen'>
//             <div className='flex justify-center'>
//               <img src="/kawandamatpLogo.png" alt="Logo" className="h-40 w-40" />
//             </div>
//             <div className='flex justify-center items-center mt-10 '>
//               <UserLogin />
//             </div>
//           </div>
//         </section>
//       </AuthProvider>
//     </>
//   );
// };

// export default AuthPage;
