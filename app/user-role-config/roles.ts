// user-role-config/roles.ts
// import { UserRole } from '@/context/types';

// interface RouteRoleConfig {
//   [path: string]: UserRole[];
// }

// const routeRoleConfig: RouteRoleConfig = {
//   '/dashboard/blogs': ['admin', 'editor'],
//   '/dashboard/createblog': ['admin', 'editor'],
//   '/dashboard/schoolfees': ['admin', 'accountant'],
//   '/dashboard/feespayments': ['admin', 'accountant'],
//   '/dashboard/examreports': ['admin', 'teacher'],
//   '/dashboard/examresults': ['admin', 'teacher'],
//   '/dashboard/staffmembers': ['admin', 'accountant'],
//   '/dashboard/createteacher': ['admin', 'accountant'],
//   '/dashboard/calendaar': ['admin', 'headteacher'],
//   '/dashboard': ['admin'], // Admin has access to all dashboard routes
//   // Add more routes and roles as needed
// };

// export default routeRoleConfig;
