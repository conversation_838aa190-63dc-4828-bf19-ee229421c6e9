
// "use client"

// import type { Metadata } from 'next';
// import { Inter } from 'next/font/google';
// import './globals.css';
// import { Toaster } from '@/components/ui/toaster';
// import { ThemeProvider } from '@/components/providers/ThemeProvider';
// import { AuthProvider } from '@/context/AuthContext';
// import { usePathname } from 'next/navigation';
// import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// const inter = Inter({ subsets: ['latin'] });

// const queryClient = new QueryClient();
// const metadata: Metadata = {
//   title: 'Kawandama hr',
//   description: 'Kawandama hr system',
// };

// export default function RootLayout({
//   children,
// }: Readonly<{
//   children: React.ReactNode;
// }>) {

//   const pathname = usePathname();
//   const isDashboard = pathname ? pathname.startsWith("/dashboard") : false;

//   return (
//     <html lang='en' suppressHydrationWarning>
//       <AuthProvider>
//         <body className={inter.className} >
//           <ThemeProvider
//             attribute='class'
//             defaultTheme='light'
//             enableSystem={false}
//             storageKey='dashboard-theme'
//           >
//             <QueryClientProvider client={queryClient}>
//               {children}
//               <Toaster />
//             </QueryClientProvider>
//           </ThemeProvider>
//         </body>
//       </AuthProvider>
//     </html>
//   );
// }




"use client"

import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { Toaster } from '@/components/ui/toaster';
import { ThemeProvider } from '@/components/providers/ThemeProvider';
import { AuthProvider } from '@/context/AuthContext';
import { usePathname } from 'next/navigation';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { HydrationBoundary } from '@tanstack/react-query';

const inter = Inter({ subsets: ['latin'] });

const queryClient = new QueryClient();
const metadata: Metadata = {
  title: 'Kawandama hr',
  description: 'Kawandama hr system',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

  const pathname = usePathname();
  const isDashboard = pathname ? pathname.startsWith("/dashboard") : false;

  return (
    <html lang='en' suppressHydrationWarning>
      <AuthProvider>
        <body className={inter.className} >
          <ThemeProvider
            attribute='class'
            defaultTheme='light'
            enableSystem={false}
            storageKey='dashboard-theme'
          >
            <QueryClientProvider client={queryClient}>
              <HydrationBoundary>
                {children}
                <Toaster />
              </HydrationBoundary>
            </QueryClientProvider>
          </ThemeProvider>
        </body>
      </AuthProvider>
    </html>
  );
}
