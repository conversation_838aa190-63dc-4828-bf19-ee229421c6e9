// app/services/employeeService.ts

// export const fetchEmployees = async () => {
//     const response = await fetch('/api/employees/getAllEmployees');
//     if (!response.ok) {
//         throw new Error('Failed to fetch employees');
//     }
//     return response.json();
// };

// export const fetchDepartments = async () => {
//     const response = await fetch('/api/departments/getAllDepartments');
//     if (!response.ok) {
//         throw new Error('Failed to fetch departments');
//     }
//     return response.json();
// };


// app/services/employeeService.ts
import { Employee } from '@/app/types/employee';

// export const fetchEmployees = async (): Promise<Employee[]> => {
//     const response = await fetch('/api/employees');
//     if (!response.ok) {
//         throw new Error('Failed to fetch employees');
//     }
//     return response.json();
// };

// export const fetchDepartments = async (): Promise<{ _id: string; name: string }[]> => {
//     const response = await fetch('/api/departments');
//     if (!response.ok) {
//         throw new Error('Failed to fetch departments');
//     }
//     return response.json();
// };


// Fetch employees function
const fetchEmployees = async () => {
    const response = await fetch('/api/employees/getAllEmployees');
    if (!response.ok) {
        throw new Error('Failed to fetch employees');
    }
    return response.json();
};

// Fetch departments function
const fetchDepartments = async () => {
    const response = await fetch('/api/departments/getAllDepartments');
    if (!response.ok) {
        throw new Error('Failed to fetch departments');
    }
    return response.json();
};
