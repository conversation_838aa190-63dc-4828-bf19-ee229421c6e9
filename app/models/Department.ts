// // models/Department.ts
// import mongoose, { Document, Schema } from 'mongoose';

// export interface IDepartment extends Document {
//     name: string;
//     description: string;
// }

// const departmentSchema: Schema<IDepartment> = new Schema({
//     name: { type: String, required: true, unique: true },
//     description: { type: String, required: false }
// }, { timestamps: true });

// const Department = mongoose.model<IDepartment>('Department', departmentSchema);
// export default Department;


// models/Department.ts
import mongoose, { Schema, Document } from 'mongoose';

interface IDepartment extends Document {
    name: string;
    description: string;
}

const DepartmentSchema: Schema = new Schema({
    name: { type: String, required: true },
    description: { type: String, required: true }
}, {
    timestamps: true
});

const Department = mongoose.models.Department || mongoose.model<IDepartment>('Department', DepartmentSchema);

export default Department;
