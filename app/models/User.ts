
// models/User.ts
import mongoose, { Document, Schema } from 'mongoose';
import bcrypt from 'bcryptjs';

export interface IUser extends Document {
  username: string;
  password: string;
  email: string;
  role: 'admin' | 'ceoadmin' | 'accountant' | 'hrmanager' | 'fieldsupervisor' | 'author' | 'editor' | 'sales' | 'manager' | 'marketing';
  comparePassword(candidatePassword: string): Promise<boolean>;
}

const userSchema: Schema<IUser> = new Schema({
  username: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  role: {
    type: String,
    required: true,
    enum: ['admin', 'ceoadmin', 'accountant', 'hrmanager', 'fieldsupervisor', 'author', 'editor',  'sales', 'manager',  'marketing']
  }
}, { timestamps: true });

userSchema.pre('save', async function(next) {
  if (this.isModified('password') || this.isNew) {
    const hashedPassword = await bcrypt.hash(this.password, 12);
    this.password = hashedPassword;
  }
  next();
});

userSchema.methods.comparePassword = async function(candidatePassword: string) {
  return bcrypt.compare(candidatePassword, this.password);
};

const User = mongoose.model<IUser>('User', userSchema);
export default User;
