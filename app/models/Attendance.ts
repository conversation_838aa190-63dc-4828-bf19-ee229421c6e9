// // Interface for Attendance Document
// import mongoose, { Schema, Document } from 'mongoose';
// interface IAttendance extends Document {
//   _id: mongoose.Types.ObjectId;
//   owner_id: string;
//   employee: {
//     _id: mongoose.Types.ObjectId;
//     firstName: string;
//     lastName: string;
//   };
//   date: Date;
//   isPresent: boolean;
//   clockInTime?: Date;
//   clockOutTime?: Date;
//   shift?: {
//     _id: mongoose.Types.ObjectId;
//     name: string;
//     startTime: Date;
//     endTime: Date;
//   };
// }

// // Attendance Schema Definition
// const AttendanceSchema: Schema = new Schema({
//   _id: { type: mongoose.Types.ObjectId, required: true, auto: true },
//   owner_id: { type: String, required: true },
//   employee: {
//     _id: { type: mongoose.Types.ObjectId, required: true },
//     firstName: { type: String, required: true },
//     lastName: { type: String, required: true },
//   },
//   date: { type: Date, required: true },
//   isPresent: { type: Boolean, required: true },
//   clockInTime: { type: Date, default: null },
//   clockOutTime: { type: Date, default: null },
//   shift: {
//     _id: { type: mongoose.Types.ObjectId, required: true },
//     name: { type: String, required: true },
//     startTime: { type: Date, required: true },
//     endTime: { type: Date, required: true },
//   },
// }, {
//   timestamps: true, // Automatically add createdAt and updatedAt fields
// });

// // Attendance Model
// const Attendance = mongoose.models.Attendance || mongoose.model<IAttendance>('Attendance', AttendanceSchema);

// export default Attendance;


import mongoose, { Schema, Document } from 'mongoose';

interface IAttendance extends Document {
  _id: mongoose.Types.ObjectId;
  owner_id: string;
  employee: {
    _id: mongoose.Types.ObjectId;
    employeeIdentity: string; // Add employeeIdentity field
    firstName: string;
    lastName: string;
  };
  date: Date;
  isPresent: boolean;
  clockInTime?: Date;
  clockOutTime?: Date;
  shift?: {
    _id: mongoose.Types.ObjectId;
    name: string;
    startTime: Date;
    endTime: Date;
  };
}

// Attendance Schema Definition
const AttendanceSchema: Schema = new Schema({
  _id: { type: mongoose.Types.ObjectId, required: true, auto: true },
  owner_id: { type: String, required: true },
  employee: {
    _id: { type: mongoose.Types.ObjectId, required: true },
    employeeIdentity: { type: String, required: true }, // Ensure this field is stored
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
  },
  date: { type: Date, required: true },
  isPresent: { type: Boolean, required: true },
  clockInTime: { type: Date, default: null },
  clockOutTime: { type: Date, default: null },
  shift: {
    _id: { type: mongoose.Types.ObjectId, required: true },
    name: { type: String, required: true },
    startTime: { type: Date, required: true },
    endTime: { type: Date, required: true },
  },
}, {
  timestamps: true, // Automatically add createdAt and updatedAt fields
});

// Attendance Model
const Attendance = mongoose.models.Attendance || mongoose.model<IAttendance>('Attendance', AttendanceSchema);

export default Attendance;
