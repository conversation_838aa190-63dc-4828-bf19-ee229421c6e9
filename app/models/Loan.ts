// models/Loan.ts
import mongoose, { Schema, Document } from 'mongoose';

interface ILoan extends Document {
    employee: mongoose.Schema.Types.ObjectId | { _id: string; firstName: string; lastName: string };
    amount: number;
    interestRate: number;
    startDate: Date;
    endDate: Date;
    status: 'pending' | 'approved' | 'rejected';
    repaymentSchedule: 'weekly' | 'monthly';
}

const LoanSchema: Schema = new Schema({
    employee: { type: Schema.Types.ObjectId, ref: 'Employee', required: true },
    amount: { type: Number, required: true },
    interestRate: { type: Number, required: true },
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    status: { type: String, enum: ['pending', 'approved', 'rejected'], required: true },
    repaymentSchedule: { type: String, enum: ['weekly', 'monthly'], required: true },
}, {
    timestamps: true
});

const Loan = mongoose.models.Loan || mongoose.model<ILoan>('Loan', LoanSchema);

export default Loan;
