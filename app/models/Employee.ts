

// // // // // models/Employee.ts
// // // // import mongoose, { Schema, Document } from 'mongoose';

// // // // interface IEmployee extends Document {
// // // //     firstName: string;
// // // //     lastName: string;
// // // //     jobTitle: string;
// // // //     salary: string;
// // // //     hireDate: Date;
// // // //     department: mongoose.Schema.Types.ObjectId | { _id: string; name: string }; // Adjusted for population
// // // //     email: string;
// // // //     phone: string;
// // // //     gender: string;
// // // //     dateOfBirth: Date;
// // // //     homeOrigin: string;
// // // //     residence: string;
// // // //     maritalStatus: string;
// // // //     nextOfKin: string;
// // // //     nextOfKinContact: string;
// // // //     status: 'active' | 'inactive' | 'terminated'; // Added status field
// // // // }

// // // // const EmployeeSchema: Schema = new Schema({
// // // //     firstName: { type: String, required: true },
// // // //     lastName: { type: String, required: true },
// // // //     jobTitle: { type: String, required: true },
// // // //     salary: { type: String, required: true },
// // // //     hireDate: { type: Date, required: true },
// // // //     department: { type: Schema.Types.ObjectId, ref: 'Department', required: true },
// // // //     email: { type: String, required: true },
// // // //     phone: { type: String, required: true },
// // // //     gender: { type: String, required: true },
// // // //     dateOfBirth: { type: Date, required: true },
// // // //     homeOrigin: { type: String, required: true },
// // // //     residence: { type: String, required: true },
// // // //     maritalStatus: { type: String, required: true },
// // // //     nextOfKin: { type: String, required: true },
// // // //     nextOfKinContact: { type: String, required: true },
// // // //     status: { type: String, enum: ['active', 'inactive', 'terminated'], required: true }, // Added status field
// // // // }, {
// // // //     timestamps: true
// // // // });

// // // // const Employee = mongoose.models.Employee || mongoose.model<IEmployee>('Employee', EmployeeSchema);

// // // // export default Employee;







// // // // models/Employee.ts
// // // import mongoose, { Schema, Document } from 'mongoose';

// // // interface IEmployee extends Document {
// // //     firstName: string;
// // //     lastName: string;
// // //     jobTitle: string;
// // //     salary: string;
// // //     hireDate: Date;
// // //     department?: mongoose.Schema.Types.ObjectId | { _id: string; name: string }; // Adjusted for population and made optional
// // //     email: string;
// // //     phone: string;
// // //     gender: string;
// // //     dateOfBirth: Date;
// // //     homeOrigin: string;
// // //     residence: string;
// // //     maritalStatus: string;
// // //     nextOfKin: string;
// // //     nextOfKinContact: string;
// // //     status: 'active' | 'inactive' | 'terminated';
// // // }

// // // const EmployeeSchema: Schema = new Schema({
// // //     firstName: { type: String, required: true },
// // //     lastName: { type: String, required: true },
// // //     jobTitle: { type: String, required: true },
// // //     salary: { type: String, required: true },
// // //     hireDate: { type: Date, required: true },
// // //     department: { type: Schema.Types.ObjectId, ref: 'Department', required: false }, // Made optional
// // //     email: { type: String, required: true },
// // //     phone: { type: String, required: true },
// // //     gender: { type: String, required: true },
// // //     dateOfBirth: { type: Date, required: true },
// // //     homeOrigin: { type: String, required: true },
// // //     residence: { type: String, required: true },
// // //     maritalStatus: { type: String, required: true },
// // //     nextOfKin: { type: String, required: true },
// // //     nextOfKinContact: { type: String, required: true },
// // //     status: { type: String, enum: ['active', 'inactive', 'terminated'], required: true },
// // // }, {
// // //     timestamps: true
// // // });

// // // const Employee = mongoose.models.Employee || mongoose.model<IEmployee>('Employee', EmployeeSchema);

// // // export default Employee;




// // // // models/Employee.ts
// // // import mongoose, { Schema, Document } from 'mongoose';

// // // interface IEmployee extends Document {
// // //     firstName: string;
// // //     lastName: string;
// // //     jobTitle: string;
// // //     salary: string;
// // //     hireDate: Date;
// // //     email: string;
// // //     phone: string;
// // //     gender: string;
// // //     dateOfBirth: Date;
// // //     homeOrigin: string;
// // //     residence: string;
// // //     maritalStatus: string;
// // //     nextOfKin: string;
// // //     nextOfKinContact: string;
// // //     status: 'active' | 'inactive' | 'terminated'; // Added status field
// // // }

// // // const EmployeeSchema: Schema = new Schema({
// // //     firstName: { type: String, required: true },
// // //     lastName: { type: String, required: true },
// // //     jobTitle: { type: String, required: true },
// // //     salary: { type: String, required: true },
// // //     hireDate: { type: Date, required: true },
// // //     email: { type: String, required: true },
// // //     phone: { type: String, required: true },
// // //     gender: { type: String, required: true },
// // //     dateOfBirth: { type: Date, required: true },
// // //     homeOrigin: { type: String, required: true },
// // //     residence: { type: String, required: true },
// // //     maritalStatus: { type: String, required: true },
// // //     nextOfKin: { type: String, required: true },
// // //     nextOfKinContact: { type: String, required: true },
// // //     status: { type: String, enum: ['active', 'inactive', 'terminated'], required: true }, // Added status field
// // // }, {
// // //     timestamps: true
// // // });

// // // const Employee = mongoose.models.Employee || mongoose.model<IEmployee>('Employee', EmployeeSchema);

// // // export default Employee;




// // // import type { NextApiRequest, NextApiResponse } from 'next';
// // // import dbConnect from '@/lib/mongoose';
// // // import Employee from '@/app/models/Employee';

// // // export default async function receiveEmployee(req: NextApiRequest, res: NextApiResponse) {
// // //     if (req.method !== 'POST') {
// // //         res.setHeader('Allow', ['POST']);
// // //         return res.status(405).end(`Method ${req.method} Not Allowed`);
// // //     }

// // //     try {
// // //         await dbConnect();

// // //         const {
// // //             firstname,
// // //             lastname,
// // //             phone,
// // //             gender,
// // //             age,
// // //             profile_image_base64,
// // //             employee_type,
// // //             jobTitle,
// // //             salary,
// // //             email,
// // //             hireDate,
// // //             dateOfBirth,
// // //             homeOrigin,
// // //             residence,
// // //             maritalStatus,
// // //             nextOfKin,
// // //             nextOfKinContact,
// // //             status,
// // //         } = req.body;

// // //         // Validate required fields
// // //         if (!firstname || !lastname || !phone || !gender || !age || !employee_type || !email) {
// // //             return res.status(400).json({ message: 'Required employee data is missing' });
// // //         }

// // //         // Create the employee record
// // //         const employeeRecord = new Employee({
// // //             firstName: firstname.trim(),     // Map to correct schema fields
// // //             lastName: lastname.trim(),       // Map to correct schema fields
// // //             phone: phone.trim(),
// // //             gender: gender.trim(),
// // //             age,
// // //             profile_image_base64,
// // //             employee_type: employee_type.trim(),
// // //             jobTitle: jobTitle.trim(),
// // //             salary,
// // //             email: email.trim(),
// // //             hireDate: new Date(hireDate),
// // //             dateOfBirth: new Date(dateOfBirth),
// // //             homeOrigin: homeOrigin.trim(),
// // //             residence: residence.trim(),
// // //             maritalStatus: maritalStatus.trim(),
// // //             nextOfKin: nextOfKin.trim(),
// // //             nextOfKinContact: nextOfKinContact.trim(),
// // //             status: status.trim(),
// // //         });

// // //         // Save the employee record to the database
// // //         await employeeRecord.save();

// // //         res.status(201).json({ message: 'Employee record received and saved successfully', employeeRecord });
// // //     } catch (error) {
// // //         if (error instanceof Error) {
// // //             console.error("Error in receiveEmployee:", error.message, error.stack);
// // //             res.status(500).json({ message: 'An unexpected error occurred', error: error.message });
// // //         } else {
// // //             console.error("Unknown error in receiveEmployee:", error);
// // //             res.status(500).json({ message: 'An unexpected error occurred' });
// // //         }
// // //     }
// // // }



// // // models/Employee.ts
// // import mongoose, { Schema, Document, Model } from 'mongoose';

// // interface IEmployee extends Document {
// //     firstName: string;
// //     lastName: string;
// //     jobTitle: string;
// //     salary: string;
// //     hireDate: Date;
// //     email: string;
// //     phone: string;
// //     gender: string;
// //     dateOfBirth: Date;
// //     homeOrigin: string;
// //     residence: string;
// //     maritalStatus: string;
// //     nextOfKin: string;
// //     nextOfKinContact: string;
// //     status: 'active' | 'inactive' | 'terminated';
// // }

// // const EmployeeSchema: Schema<IEmployee> = new Schema({
// //     firstName: { type: String, required: true },
// //     lastName: { type: String, required: true },
// //     jobTitle: { type: String, required: true },
// //     salary: { type: String, required: true },
// //     hireDate: { type: Date, required: true },
// //     email: { type: String, required: true },
// //     phone: { type: String, required: true },
// //     gender: { type: String, required: true },
// //     dateOfBirth: { type: Date, required: true },
// //     homeOrigin: { type: String, required: true },
// //     residence: { type: String, required: true },
// //     maritalStatus: { type: String, required: true },
// //     nextOfKin: { type: String, required: true },
// //     nextOfKinContact: { type: String, required: true },
// //     status: { type: String, enum: ['active', 'inactive', 'terminated'], required: true },
// // }, {
// //     timestamps: true
// // });

// // const Employee: Model<IEmployee> = mongoose.models.Employee || mongoose.model<IEmployee>('Employee', EmployeeSchema);

// // export default Employee;



// // // models/Employee.ts
// // import mongoose, { Schema, Document } from 'mongoose';

// // interface IEmployee extends Document {
// //     firstName?: string;
// //     lastName?: string;
// //     jobTitle?: string;
// //     salary?: string;
// //     hireDate?: Date;
// //     email?: string;
// //     phone?: string;
// //     gender?: string;
// //     dateOfBirth?: Date;
// //     homeOrigin?: string;
// //     residence?: string;
// //     maritalStatus?: string;
// //     nextOfKin?: string;
// //     nextOfKinContact?: string;
// //     status?: 'active' | 'inactive' | 'terminated';
// // }

// // const EmployeeSchema: Schema = new Schema({
// //     firstName: { type: String, required: false },
// //     lastName: { type: String, required: false },
// //     jobTitle: { type: String, required: false },
// //     salary: { type: String, required: false },
// //     hireDate: { type: Date, required: false },
// //     email: { type: String, required: false },
// //     phone: { type: String, required: false },
// //     gender: { type: String, required: false },
// //     dateOfBirth: { type: Date, required: false },
// //     homeOrigin: { type: String, required: false },
// //     residence: { type: String, required: false },
// //     maritalStatus: { type: String, required: false },
// //     nextOfKin: { type: String, required: false },
// //     nextOfKinContact: { type: String, required: false },
// //     status: { type: String, enum: ['active', 'inactive', 'terminated'], required: false },
// // }, {
// //     timestamps: true
// // });

// // const Employee = mongoose.models.Employee || mongoose.model<IEmployee>('Employee', EmployeeSchema);

// // export default Employee;



// // models/Employee.ts
// import mongoose, { Schema, Document } from 'mongoose';

// interface IEmployee extends Document {
//     employeeIdentity?: string; // Add the employeeIdentity field
//     firstName?: string;
//     lastName?: string;
//     jobTitle?: string;
//     salary?: string;
//     hireDate?: Date;
//     email?: string;
//     phone?: string;
//     gender?: string;
//     dateOfBirth?: Date;
//     homeOrigin?: string;
//     residence?: string;
//     maritalStatus?: string;
//     nextOfKin?: string;
//     nextOfKinContact?: string;
//     status?: 'active' | 'inactive' | 'terminated';
// }

// const EmployeeSchema: Schema = new Schema({
//     employeeIdentity: { type: String, required: false }, // Add the schema definition
//     firstName: { type: String, required: false },
//     lastName: { type: String, required: false },
//     jobTitle: { type: String, required: false },
//     salary: { type: String, required: false },
//     hireDate: { type: Date, required: false },
//     email: { type: String, required: false },
//     phone: { type: String, required: false },
//     gender: { type: String, required: false },
//     dateOfBirth: { type: Date, required: false },
//     homeOrigin: { type: String, required: false },
//     residence: { type: String, required: false },
//     maritalStatus: { type: String, required: false },
//     nextOfKin: { type: String, required: false },
//     nextOfKinContact: { type: String, required: false },
//     status: { type: String, enum: ['active', 'inactive', 'terminated'], required: false },
// }, {
//     timestamps: true
// });

// const Employee = mongoose.models.Employee || mongoose.model<IEmployee>('Employee', EmployeeSchema);

// export default Employee;




// models/Employee.ts
import mongoose, { Schema, Document } from 'mongoose';

interface IEmployee extends Document {
    employeeIdentity?: string; // Add employeeIdentity field
    firstName?: string;
    lastName?: string;
    jobTitle?: string;
    salary?: string;
    hireDate?: Date;
    email?: string;
    phone?: string;
    gender?: string;
    dateOfBirth?: Date;
    homeOrigin?: string;
    residence?: string;
    maritalStatus?: string;
    nextOfKin?: string;
    nextOfKinContact?: string;
    status?: 'active' | 'inactive' | 'terminated';
}

const EmployeeSchema: Schema = new Schema({
    employeeIdentity: { type: String, required: false }, // Include this field
    firstName: { type: String, required: false },
    lastName: { type: String, required: false },
    jobTitle: { type: String, required: false },
    salary: { type: String, required: false },
    hireDate: { type: Date, required: false },
    email: { type: String, required: false },
    phone: { type: String, required: false },
    gender: { type: String, required: false },
    dateOfBirth: { type: Date, required: false },
    homeOrigin: { type: String, required: false },
    residence: { type: String, required: false },
    maritalStatus: { type: String, required: false },
    nextOfKin: { type: String, required: false },
    nextOfKinContact: { type: String, required: false },
    status: { type: String, enum: ['active', 'inactive', 'terminated'], required: false },
}, {
    timestamps: true
});

const Employee = mongoose.models.Employee || mongoose.model<IEmployee>('Employee', EmployeeSchema);

export default Employee;
