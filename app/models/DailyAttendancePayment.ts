import mongoose, { Schema, Document } from 'mongoose';

interface IEmployeeDetails extends Document {
    _id: string;
    firstName: string;
    lastName: string;
}

interface IDailyAttendancePayment extends Document {
    employee: IEmployeeDetails;
    salary: string;
    date: string; // In format "date, day name, year"
    attendanceId: mongoose.Schema.Types.ObjectId;
    status: 'pending' | 'approved' | 'failed';
}

const EmployeeDetailsSchema: Schema = new Schema({
    _id: { type: String, required: true },
    firstName: { type: String, required: true },
    lastName: { type: String, required: true }
});

const DailyAttendancePaymentSchema: Schema = new Schema({
    employee: { type: EmployeeDetailsSchema, required: true },
    salary: { type: String, required: true },
    date: { type: String, required: true },
    attendanceId: { type: Schema.Types.ObjectId, ref: 'Attendance', required: true },
    status: { type: String, enum: ['pending', 'approved', 'failed'], default: 'pending', required: true }
}, {
    timestamps: true
});

const DailyAttendancePayment = mongoose.models.DailyAttendancePayment || mongoose.model<IDailyAttendancePayment>('DailyAttendancePayment', DailyAttendancePaymentSchema);

export default DailyAttendancePayment;
