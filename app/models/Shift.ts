import mongoose, { Schema, Document } from 'mongoose';

// Interface for Shift Document
interface IShift extends Document {
  _id: mongoose.Types.ObjectId;
  owner_id: string;
  name: string;
  startTime: Date;
  endTime: Date;
}

// Shift Schema Definition
const ShiftSchema: Schema = new Schema({
  _id: { type: mongoose.Types.ObjectId, required: true, auto: true },
  owner_id: { type: String, required: true },
  name: { type: String, required: true },
  startTime: { type: Date, required: true },
  endTime: { type: Date, required: true },
}, {
  timestamps: true, // Automatically add createdAt and updatedAt fields
});

// Shift Model
const Shift = mongoose.models.Shift || mongoose.model<IShift>('Shift', ShiftSchema);

export default Shift;
