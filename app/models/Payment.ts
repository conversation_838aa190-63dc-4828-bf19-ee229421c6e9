
// // models/Payment.ts
// import mongoose, { Schema, Document } from 'mongoose';

// interface IPayment extends Document {
//     amount: string;
//     status: 'pending' | 'revised' | 'review' | 'approved' | 'processing' | 'failed' | 'cancel';
//     employee: mongoose.Schema.Types.ObjectId | { _id: string; firstName: string; lastName: string; phone_number: string };
//     tax?: string;
//     loan_deduction?: string;
//     insurance?: string;
//     final_amount?: string;
// }

// const PaymentSchema: Schema = new Schema({
//     amount: { type: String, required: true },
//     status: { type: String, enum: ['pending', 'revised', 'review', 'approved', 'processing', 'failed'], required: true },
//     employee: { type: Schema.Types.ObjectId, ref: 'Employee', required: true },
//     tax: { type: String, default: '0' },
//     loan_deduction: { type: String, default: '0' },
//     insurance: { type: String, default: '0' },
//     final_amount: { type: String, default: '0' }
// }, {
//     timestamps: true
// });

// const Payment = mongoose.models.Payment || mongoose.model<IPayment>('Payment', PaymentSchema);

// export default Payment;



// import mongoose, { Schema, Document } from 'mongoose';

// interface ExtraLoan extends Document {
//     type: string;
//     amount: string;
// }

// interface LoanDeduction extends Document {
//     social_welfare: string;
//     salary_advance: string;
//     medical: string;
//     extra: ExtraLoan[];
// }

// interface IPayment extends Document {
//     amount: string;
//     bonus_payment?: string;
//     status: 'pending' | 'revised' | 'review' | 'approved' | 'processing' | 'failed' | 'cancel';
//     employee: mongoose.Schema.Types.ObjectId | { _id: string; firstName: string; lastName: string; phone_number: string };
//     tax?: string;
//     loan_deduction: LoanDeduction;
//     insurance?: string;
//     pension_deduction?: string;
//     final_amount?: string;
// }

// const ExtraLoanSchema: Schema = new Schema({
//     type: { type: String, required: true },
//     amount: { type: String, default: '0' }
// });

// const LoanDeductionSchema: Schema = new Schema({
//     social_welfare: { type: String, default: '0' },
//     salary_advance: { type: String, default: '0' },
//     medical: { type: String, default: '0' },
//     extra: { type: [ExtraLoanSchema], default: [] }
// });

// const PaymentSchema: Schema = new Schema({
//     amount: { type: String, required: true },
//     bonus_payment: { type: String, default: '0' },
//     status: { type: String, enum: ['pending', 'revised', 'review', 'approved', 'processing', 'failed', 'cancel'], required: true },
//     employee: { type: Schema.Types.ObjectId, ref: 'Employee', required: true },
//     tax: { type: String, default: '0' },
//     loan_deduction: { type: LoanDeductionSchema, default: () => ({}) },
//     insurance: { type: String, default: '0' },
//     pension_deduction: { type: String, default: '0' },
//     final_amount: { type: String, default: '0' }
// }, {
//     timestamps: true
// });

// const Payment = mongoose.models.Payment || mongoose.model<IPayment>('Payment', PaymentSchema);

// export default Payment;




// import mongoose, { Schema, Document } from 'mongoose';

// // Define the ExtraLoan interface and schema
// interface ExtraLoan extends Document {
//     type: string;
//     amount: string;
// }

// const ExtraLoanSchema: Schema = new Schema({
//     type: { type: String, required: true },
//     amount: { type: String, default: '0' }
// });

// // Define the LoanDeduction interface and schema
// interface LoanDeduction extends Document {
//     social_welfare: string;
//     salary_advance: string;
//     medical: string;
//     extra: ExtraLoan[];
// }

// const LoanDeductionSchema: Schema = new Schema({
//     social_welfare: { type: String, default: '0' },
//     salary_advance: { type: String, default: '0' },
//     medical: { type: String, default: '0' },
//     extra: { type: [ExtraLoanSchema], default: [] }
// });

// // Define the IPayment interface with a reference to attendance records
// interface IPayment extends Document {
//     amount: string;
//     bonus_payment?: string;
//     status: 'pending' | 'revised' | 'review' | 'approved' | 'processing' | 'failed' | 'cancel';
//     employee: mongoose.Schema.Types.ObjectId | { _id: string; firstName: string; lastName: string; phone_number: string };
//     tax?: string;
//     loan_deduction: LoanDeduction;
//     insurance?: string;
//     pension_deduction?: string;
//     final_amount?: string;
//     attendances?: mongoose.Schema.Types.ObjectId[];  // Reference to attendance records
// }

// // Define the Payment schema with the attendance reference
// const PaymentSchema: Schema = new Schema({
//     amount: { type: String, required: true },
//     bonus_payment: { type: String, default: '0' },
//     status: { type: String, enum: ['pending', 'revised', 'review', 'approved', 'processing', 'failed', 'cancel'], required: true },
//     employee: { type: Schema.Types.ObjectId, ref: 'Employee', required: true },
//     tax: { type: String, default: '0' },
//     loan_deduction: { type: LoanDeductionSchema, default: () => ({}) },
//     insurance: { type: String, default: '0' },
//     pension_deduction: { type: String, default: '0' },
//     final_amount: { type: String, default: '0' },
//     attendances: [{ type: Schema.Types.ObjectId, ref: 'Attendance' }],  // Reference to attendance records
// }, {
//     timestamps: true
// });

// // Create or retrieve the Payment model
// const Payment = mongoose.models.Payment || mongoose.model<IPayment>('Payment', PaymentSchema);

// export default Payment;





// import mongoose, { Schema, Document } from 'mongoose';

// // Define the ExtraLoan interface and schema
// interface ExtraLoan extends Document {
//     type: string;
//     amount: string;
// }

// const ExtraLoanSchema: Schema = new Schema({
//     type: { type: String, required: true },
//     amount: { type: String, default: '0' }
// });

// // Define the LoanDeduction interface and schema
// interface LoanDeduction extends Document {
//     social_welfare: string;
//     salary_advance: string;
//     medical: string;
//     extra: ExtraLoan[];
// }

// const LoanDeductionSchema: Schema = new Schema({
//     social_welfare: { type: String, default: '0' },
//     salary_advance: { type: String, default: '0' },
//     medical: { type: String, default: '0' },
//     extra: { type: [ExtraLoanSchema], default: [] }
// });

// // Define the IPayment interface with a reference to attendance records
// interface IPayment extends Document {
//     amount: string;
//     bonus_payment?: string;
//     status: 'pending' | 'revised' | 'review' | 'approved' | 'processing' | 'failed' | 'cancel';
//     employee: mongoose.Schema.Types.ObjectId | { _id: string; firstName: string; lastName: string; phone_number: string };
//     tax?: string;
//     loan_deduction: LoanDeduction;
//     insurance?: string;
//     pension_deduction?: string;
//     final_amount?: string;
//     attendances?: mongoose.Schema.Types.ObjectId[];  // Reference to attendance records
//     day_rate?: string; // Daily rate based on salary and working days
//     working_days?: number; // Number of working days in the month
//     attendance_days?: number; // Number of days attended by the employee
// }

// // Define the Payment schema with the attendance reference
// const PaymentSchema: Schema = new Schema({
//     amount: { type: String, required: true },
//     bonus_payment: { type: String, default: '0' },
//     status: { type: String, enum: ['pending', 'revised', 'review', 'approved', 'processing', 'failed', 'cancel'], required: true },
//     employee: { type: Schema.Types.ObjectId, ref: 'Employee', required: true },
//     tax: { type: String, default: '0' },
//     loan_deduction: { type: LoanDeductionSchema, default: () => ({}) },
//     insurance: { type: String, default: '0' },
//     pension_deduction: { type: String, default: '0' },
//     final_amount: { type: String, default: '0' },
//     attendances: [{ type: Schema.Types.ObjectId, ref: 'Attendance' }],  // Reference to attendance records
//     day_rate: { type: String, default: '0' },  // Daily rate
//     working_days: { type: Number, default: 0 },  // Working days in the month
//     attendance_days: { type: Number, default: 0 },  // Days attended
// }, {
//     timestamps: true
// });

// // Create or retrieve the Payment model
// const Payment = mongoose.models.Payment || mongoose.model<IPayment>('Payment', PaymentSchema);

// export default Payment;



// import mongoose, { Schema, Document } from 'mongoose';

// interface ExtraLoan extends Document {
//     type: string;
//     amount: string;
// }

// const ExtraLoanSchema: Schema = new Schema({
//     type: { type: String, required: true },
//     amount: { type: String, default: '0' }
// });

// interface LoanDeduction extends Document {
//     social_welfare: string;
//     salary_advance: string;
//     medical: string;
//     extra: ExtraLoan[];
// }

// const LoanDeductionSchema: Schema = new Schema({
//     social_welfare: { type: String, default: '0' },
//     salary_advance: { type: String, default: '0' },
//     medical: { type: String, default: '0' },
//     extra: { type: [ExtraLoanSchema], default: [] }
// });

// interface IPayment extends Document {
//     amount: string;
//     bonus_payment?: string;
//     status: 'pending' | 'revised' | 'review' | 'approved' | 'processing' | 'failed' | 'cancel';
//     employee: mongoose.Schema.Types.ObjectId | { _id: string; firstName: string; lastName: string; phone_number: string };
//     tax?: string;
//     loan_deduction: LoanDeduction;
//     insurance?: string;
//     pension_deduction?: string;
//     final_amount?: string;
//     attendances?: mongoose.Schema.Types.ObjectId[];  // Reference to attendance records
//     day_rate?: string;
//     working_days?: number;
//     attendance_days?: number;
// }

// const PaymentSchema: Schema = new Schema({
//     amount: { type: String, required: true },
//     bonus_payment: { type: String, default: '0' },
//     status: { type: String, enum: ['pending', 'revised', 'review', 'approved', 'processing', 'failed', 'cancel'], required: true },
//     employee: { type: Schema.Types.ObjectId, ref: 'Employee', required: true },
//     tax: { type: String, default: '0' },
//     loan_deduction: { type: LoanDeductionSchema, default: () => ({}) },
//     insurance: { type: String, default: '0' },
//     pension_deduction: { type: String, default: '0' },
//     final_amount: { type: String, default: '0' },
//     attendances: [{ type: Schema.Types.ObjectId, ref: 'Attendance' }],
//     day_rate: { type: String, default: '0' },
//     working_days: { type: Number, default: 0 },
//     attendance_days: { type: Number, default: 0 },
// }, {
//     timestamps: true
// });

// const Payment = mongoose.models.Payment || mongoose.model<IPayment>('Payment', PaymentSchema);

// export default Payment;



import mongoose, { Schema, Document } from 'mongoose';

interface ExtraLoan extends Document {
    type: string;
    amount: string;
}

const ExtraLoanSchema: Schema = new Schema({
    type: { type: String, required: false }, // Updated to optional
    amount: { type: String, default: '0' }
});

interface LoanDeduction extends Document {
    social_welfare: string;
    salary_advance: string;
    medical: string;
    extra: ExtraLoan[];
}

const LoanDeductionSchema: Schema = new Schema({
    social_welfare: { type: String, default: '0' },
    salary_advance: { type: String, default: '0' },
    medical: { type: String, default: '0' },
    extra: { type: [ExtraLoanSchema], default: [] }
});

interface IPayment extends Document {
    amount: string;
    bonus_payment?: string;
    status: 'pending' | 'revised' | 'review' | 'approved' | 'processing' | 'failed' | 'cancel';
    employee: mongoose.Schema.Types.ObjectId | { _id: string; firstName: string; lastName: string; phone_number: string };
    tax?: string;
    loan_deduction: LoanDeduction;
    insurance?: string;
    pension_deduction?: string;
    final_amount?: string;
    attendances?: mongoose.Schema.Types.ObjectId[];  // Reference to attendance records
    day_rate?: string;
    working_days?: number;
    attendance_days?: number;
}

const PaymentSchema: Schema = new Schema({
    amount: { type: String, required: true },
    bonus_payment: { type: String, default: '0' },
    status: { type: String, enum: ['pending', 'revised', 'review', 'approved', 'processing', 'failed', 'cancel'], required: true },
    employee: { type: Schema.Types.ObjectId, ref: 'Employee', required: true },
    tax: { type: String, default: '0' },
    loan_deduction: { type: LoanDeductionSchema, default: () => ({}) },
    insurance: { type: String, default: '0' },
    pension_deduction: { type: String, default: '0' },
    final_amount: { type: String, default: '0' },
    attendances: [{ type: Schema.Types.ObjectId, ref: 'Attendance' }],
    day_rate: { type: String, default: '0' },
    working_days: { type: Number, default: 0 },
    attendance_days: { type: Number, default: 0 },
}, {
    timestamps: true
});

const Payment = mongoose.models.Payment || mongoose.model<IPayment>('Payment', PaymentSchema);

export default Payment;
