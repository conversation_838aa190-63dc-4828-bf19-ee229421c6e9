


// app/dashboard/(main)/approvedMonthlyPayments.tsx

"use client";

import React from 'react';
import { useApprovedPayments } from '@/lib/fetchData';
import { Payment } from '@/app/types/payment';
import { ApprovedpaymentDataTable } from '@/components/payments/approvedpaymentDataTable';

const ApprovedMonthlyPaymentsPage: React.FC = () => {
    const { data: approvedPaymentsData, isLoading, isError } = useApprovedPayments();

    if (isLoading) {
        return <div>Loading...</div>;
    }

    if (isError) {
        return <div>Error loading approved payments.</div>;
    }

    const approvedPayments: Payment[] = approvedPaymentsData ? approvedPaymentsData.payments : [];

    return (
        <div className="container mx-auto py-10">
            <h2 className='text-gray-700 text-2xl mb-4'> <span className='text-green-500'>Approved</span> Monthly Payments</h2>
            <ApprovedpaymentDataTable
                data={approvedPayments}
                userRole={"admin"} // adjust this as needed
                onEdit={() => { }}
                onDelete={() => { }}
            />
        </div>
    );
};

export default ApprovedMonthlyPaymentsPage;
