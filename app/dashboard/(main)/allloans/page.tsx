// app/dashboard(main)/page.tsx

"use client";

import React, { useState } from "react";
import {DataTable} from "@/components/loans/data-table";
import { columns } from "@/components/loans/columns";
import { Loan } from "@/app/types/loan";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import LoanEditOverlay from "@/components/loans/loan-edit-overlay";
import LoanSuccessOverlay from "@/components/loans/loan-success-overlay";
import LoanErrorOverlay from "@/components/loans/loan-error-overlay";

type FetchError = {
  message: string;
};

const fetchLoans = async () => {
  const response = await fetch("/api/loans/getAllLoans");
  if (!response.ok) {
    throw new Error("Failed to fetch loans");
  }
  return response.json();
};


export default function LoansPage() {
  const queryClient = useQueryClient();
  const {
    data: loansData,
    error: loansError,
    isError: isLoansError,
    isLoading: isLoansLoading,
  } = useQuery({
    queryKey: ["loans"],
    queryFn: fetchLoans,
  });

  const [isSuccess, setIsSuccess] = useState(false);
  const [isOverlayOpen, setIsOverlayOpen] = useState(false);
  const [isEditOverlayOpen, setIsEditOverlayOpen] = useState(false);
  const [selectedLoan, setSelectedLoan] = useState<Loan | null>(null);
  const [showErrorOverlay, setShowErrorOverlay] = useState(false);

  const loans = loansData ? loansData.loans : [];

  const loanMutation = useMutation({
    mutationFn: async ({
      method,
      url,
      body,
    }: {
      method: string;
      url: string;
      body: any;
    }) => {
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["loans"] });
      setIsSuccess(true);
      setIsOverlayOpen(false);
      setIsEditOverlayOpen(false);
    },
    onError: () => {
      setShowErrorOverlay(true);
    },
  });

  const handleAddLoan = async (_id: string, loan: Partial<Loan>): Promise<void> => {
    try {
      await loanMutation.mutateAsync({
        method: "POST",
        url: "/api/loans/createLoan",
        body: loan,
      });
    } catch (error) {
      console.error("Error adding loan:", error);
      throw error;
    }
  };

  const handleUpdateLoan = async (loanId: string, loan: Partial<Loan>): Promise<void> => {
    try {
      await loanMutation.mutateAsync({
        method: "PUT",
        url: `/api/loans/updateLoan`,
        body: { _id: loanId, ...loan },
      });
    } catch (error) {
      console.error("Error updating loan:", error);
      throw error;
    }
  };

  const handleEditLoan = (loan: Loan) => {
    setSelectedLoan(loan);
    setIsEditOverlayOpen(true);
  };

  const handleDeleteLoan = (loanId: string) => {
    loanMutation.mutate({
      method: "DELETE",
      url: `/api/loans/deleteLoan`,
      body: { _id: loanId }, // Send the loanId in the request body
    });
  };

  const handleCloseSuccessOverlay = () => {
    setIsSuccess(false);
  };

  const handleCloseErrorOverlay = () => {
    setShowErrorOverlay(false);
  };

  if (isLoansLoading) {
    return <div>Loading...</div>;
  }

  if (isLoansError) {
    return (
      <div>
        Error loading data: {(loansError as FetchError)?.message}
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Loan Management</h1>
        <Button onClick={() => setIsOverlayOpen(true)} className="ml-4">
          Add Loan
        </Button>
      </div>
      <DataTable
        columns={columns}
        data={loans}
        renderRowActions={(row: Loan) => (
          <div className="flex space-x-2">
            <Button size="sm" onClick={() => handleEditLoan(row)}>
              Edit
            </Button>
            <Button size="sm" variant="destructive" onClick={() => handleDeleteLoan(row._id)}>
              Delete
            </Button>
          </div>
        )}
      />
      {isSuccess && (
        <LoanSuccessOverlay onClose={handleCloseSuccessOverlay} />
      )}
      {showErrorOverlay && (
        <LoanErrorOverlay onClose={handleCloseErrorOverlay} />
      )}
      {isOverlayOpen && (
        <LoanEditOverlay
          isOpen={isOverlayOpen}
          onClose={() => setIsOverlayOpen(false)}
          loan={null} // For adding a new loan, the loan prop can be null or an empty object
          onSubmit={handleAddLoan} // Pass handleAddLoan for creating a new loan
          employees={[]} // Pass necessary employees data
          userRole={""} // Pass the correct userRole
        />
      )}
      {selectedLoan && (
        <LoanEditOverlay
          isOpen={isEditOverlayOpen}
          onClose={() => setIsEditOverlayOpen(false)}
          loan={selectedLoan}
          onSubmit={handleUpdateLoan} // Pass handleUpdateLoan for updating an existing loan
          employees={[]} // Pass necessary employees data
          userRole={""} // Pass the correct userRole
        />
      )}
    </div>
  );
}
