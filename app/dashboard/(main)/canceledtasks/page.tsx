


// app/(main)/canceledtasks/page.tsx

import { Task } from "@/app/types/task"
import { columns } from "@/components/tasks/columns"
import { DataTable } from "@/components/tasks/data-table"


// "not started" | "in progress" | "completed" | "canceled"
async function getTaskData(): Promise<Task[]> {
  return [
   
    {
      id: "7",
      title: "Water New Plantations",
      description: "Ensure all new plantations are watered daily.",
      status: "canceled",
      priority: "high",
      assignee: "Daisy Green",
      dueDate: "2024-07-28",
    },
    {
      id: "8",
      title: "Fertilize Soil",
      description: "Apply fertilizer to sector C.",
      status: "canceled",
      priority: "medium",
      assignee: "Edward Blue",
      dueDate: "2024-07-27",
    },
    {
      id: "9",
      title: "Safety Inspection",
      description: "Conduct a safety inspection of the processing plant.",
      status: "canceled",
      priority: "high",
      assignee: "Fiona Yellow",
      dueDate: "2024-07-29",
    },
    {
      id: "10",
      title: "Pack Timber for Shipment",
      description: "Prepare and pack timber for shipment.",
      status: "canceled",
      priority: "medium",
      assignee: "George Red",
      dueDate: "2024-08-01",
    },
  ]
}

export default async function CanceledTasksPage() {
  const tasks = await getTaskData()

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-2xl font-bold mb-4">Canceled Tasks</h1>
      <DataTable columns={columns} data={tasks} />
    </div>
  )
}
