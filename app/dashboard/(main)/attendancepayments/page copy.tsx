// // app/(main)/attendancepayment/page.tsx

// "use client"

// import React, { useState } from "react";
// import { useQueryClient, useMutation, useQuery } from "@tanstack/react-query";
// import PaymentEditOverlay from "@/components/payments/payment-edit-overlay";
// import { getColumns } from "@/components/payments/columns";
// import { useEmployees } from "@/lib/fetchData";
// import { useAuth } from "@/context/AuthContext";
// import { Button } from "@/components/ui/button";
// import { Payment } from "@/app/types/payment";
// import { DataTable } from "@/components/payments/data-table";
// import { Employee } from "@/app/types/employee";

// const fetchPayments = async () => {
//   const response = await fetch("/api/payments/getAllPayments");
//   if (!response.ok) {
//     throw new Error("Failed to fetch payments");
//   }
//   return response.json();
// };

// export default function AttendancePaymentsPage() {
//   const queryClient = useQueryClient();
//   const { user } = useAuth();

//   const { data: employeeData, isLoading: isEmployeeLoading, isError: isEmployeeError, error: employeeError } = useEmployees();
//   const { data: paymentData, error: paymentError, isError: isPaymentError, isLoading: isPaymentLoading } = useQuery({
//     queryKey: ["payments"],
//     queryFn: fetchPayments,
//   });

//   const [isOverlayOpen, setIsOverlayOpen] = useState(false);
//   const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);

//   const payments = paymentData ? paymentData.payments : [];
//   const employees = employeeData ? employeeData.employees : [];

//   const paymentMutation = useMutation({
//     mutationFn: async ({ method, url, body }: { method: string; url: string; body: any }) => {
//       const response = await fetch(url, {
//         method,
//         headers: {
//           "Content-Type": "application/json",
//         },
//         body: JSON.stringify(body),
//       });
//       if (!response.ok) {
//         throw new Error(`HTTP error! status: ${response.status}`);
//       }
//       return response.json();
//     },
//     onSuccess: () => {
//       queryClient.invalidateQueries({ queryKey: ["payments"] });
//       setIsOverlayOpen(false);
//     },
//   });

//   const handleAddPayment = () => {
//     setSelectedPayment({
//       _id: "",
//       amount: '0',
//       lastName: "",
//       firstName: "",
//       status: "pending",
//       employee: "",
//       tax: '0',
//       loan_deduction: '0',
//       insurance: '0',
//       final_amount: '0',
//     });
//     setIsOverlayOpen(true);
//   };

//   const handleEditPayment = (payment: Payment) => {
//     setSelectedPayment(payment);
//     setIsOverlayOpen(true);
//   };

//   const handleDeletePayment = (paymentId: string) => {
//     paymentMutation.mutate({
//       method: "DELETE",
//       url: `/api/payments/deletePayment`,
//       body: { id: paymentId },
//     });
//   };

//   const handlePaymentSubmit = async (id: string, data: Partial<Payment>) => {
//     if (!user) {
//       console.error("User is not authenticated.");
//       return;
//     }

//     if (id === "") {
//       return paymentMutation.mutate({
//         method: "POST",
//         url: "/api/payments/createPayment",
//         body: { ...data, userRole: user.role },
//       });
//     } else {
//       return paymentMutation.mutate({
//         method: "PUT",
//         url: `/api/payments/updatePayment?id=${id}`,
//         body: data,
//       });
//     }
//   };

//   if (isPaymentLoading || isEmployeeLoading) {
//     return <div>Loading...</div>;
//   }

//   if (isPaymentError || isEmployeeError) {
//     return <div>Error loading data: {(paymentError as Error)?.message || (employeeError as Error)?.message}</div>;
//   }

//   const columns = getColumns(handleEditPayment, handleDeletePayment, user?.role || 'accountant');

//   return (
//     <div className="container mx-auto py-10">
//       <div className="flex justify-between items-center mb-4">
//         <h1 className="text-2xl font-bold">Payment Management</h1>
//         <Button onClick={handleAddPayment} className="ml-4">
//           Add Payment
//         </Button>
//       </div>
//       <DataTable
//         columns={columns}
//         data={payments}
//         userRole={user?.role || 'accountant'}
//         onEdit={handleEditPayment}
//         onDelete={handleDeletePayment}
//       />
//       {isOverlayOpen && (
//         <PaymentEditOverlay
//           employees={employees.map((employee: Employee) => ({
//             _id: employee._id,
//             firstName: employee.firstName,
//             lastName: employee.lastName,
//             phone: employee.phone,
//             salary: employee.salary,
//           }))}
//           onSubmit={handlePaymentSubmit}
//           isOpen={isOverlayOpen}
//           onClose={() => setIsOverlayOpen(false)}
//           payment={selectedPayment || {
//             _id: "",
//             amount: '0',
//             status: "pending",
//             employee: "",
//             firstName: "",
//             lastName: "",
//             tax: '0',
//             loan_deduction: '0',
//             insurance: '0',
//             final_amount: '0',
//           }}
//           userRole={user?.role || 'accountant'}
//         />
//       )}
//     </div>
//   );
// }
