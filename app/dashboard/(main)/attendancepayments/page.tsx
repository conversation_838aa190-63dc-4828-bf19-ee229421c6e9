// // app/dashboard/(main)/attendancepayments/page.tsx


// "use client";

// import React, { useState } from "react";
// import { useQueryClient, useMutation, useQuery } from "@tanstack/react-query";
// import PaymentEditOverlay from "@/components/payments/payment-edit-overlay";
// import { getColumns } from "@/components/payments/columns";
// import { useCombinedEmployeeAttendance } from "@/lib/fetchData";
// import { useAuth } from "@/context/AuthContext";
// import { Button } from "@/components/ui/button";
// import { Payment } from "@/app/types/payment";
// import { DataTable } from "@/components/payments/data-table";

// export default function AttendancePaymentsPage() {
//   const queryClient = useQueryClient();
//   const { user } = useAuth();

//   // Fetch combined employee and attendance data
//   const { data: combinedData, isLoading, isError, error } = useCombinedEmployeeAttendance();

//   const [isOverlayOpen, setIsOverlayOpen] = useState(false);
//   const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);

//   const combinedRecords = combinedData ? combinedData.combinedData : [];

//   const paymentMutation = useMutation({
//     mutationFn: async ({ method, url, body }: { method: string; url: string; body: any }) => {
//       const response = await fetch(url, {
//         method,
//         headers: {
//           "Content-Type": "application/json",
//         },
//         body: JSON.stringify(body),
//       });
//       if (!response.ok) {
//         throw new Error(`HTTP error! status: ${response.status}`);
//       }
//       return response.json();
//     },
//     onSuccess: () => {
//       queryClient.invalidateQueries({ queryKey: ["combinedEmployeeAttendance"] });
//       setIsOverlayOpen(false);
//     },
//   });

//   // const handleAddPayment = () => {
//   //   setSelectedPayment(undefined); // Set to undefined instead of null
//   //   setIsOverlayOpen(true);
//   // };

//   const handleAddPayment = () => {
//     setSelectedPayment(null); // Use null instead of undefined
//     setIsOverlayOpen(true);
//   };
  
//   const handleEditPayment = (payment: Payment) => {
//     setSelectedPayment(payment);
//     setIsOverlayOpen(true);
//   };

//   const handleDeletePayment = (paymentId: string) => {
//     paymentMutation.mutate({
//       method: "DELETE",
//       url: `/api/payments/deletePayment`,
//       body: { id: paymentId },
//     });
//   };

//   const handlePaymentSubmit = async (id: string, data: Partial<Payment>) => {
//     if (!user) {
//       console.error("User is not authenticated.");
//       return;
//     }

//     if (id === "") {
//       return paymentMutation.mutate({
//         method: "POST",
//         url: "/api/payments/createPayment",
//         body: { ...data, userRole: user.role },
//       });
//     } else {
//       return paymentMutation.mutate({
//         method: "PUT",
//         url: `/api/payments/updatePayment?id=${id}`,
//         body: data,
//       });
//     }
//   };

//   if (isLoading) {
//     return <div>Loading...</div>;
//   }

//   if (isError) {
//     return <div>Error loading data: {(error as Error)?.message}</div>;
//   }

//   const columns = getColumns(handleEditPayment, handleDeletePayment, user?.role || 'accountant');

//   return (
//     <div className="container mx-auto py-10">
//       <div className="flex justify-between items-center mb-4">
//         <h1 className="text-2xl font-bold">Payment Management</h1>
//         <Button onClick={handleAddPayment} className="ml-4">
//           Add Payment
//         </Button>
//       </div>
//       <DataTable
//         columns={columns}
//         data={combinedRecords.map((record: { employee: any; attendances: any; }) => ({
//           employee: record.employee,
//           attendances: record.attendances,
//         }))}
//         userRole={user?.role || 'accountant'}
//         onEdit={handleEditPayment}
//         onDelete={handleDeletePayment}
//       />
//       {isOverlayOpen && (
//         <PaymentEditOverlay
//           employees={combinedRecords.map((record: { employee: { employeeIdentity: any; firstName: any; lastName: any; phone: any; salary: any; _id: any; }; }) => ({
//             employeeIdentity: record.employee.employeeIdentity,
//             firstName: record.employee.firstName,
//             lastName: record.employee.lastName,
//             phone: record.employee.phone,
//             salary: record.employee.salary,
//             _id: record.employee._id, // Ensure this is included if needed
//           }))}
//           onSubmit={handlePaymentSubmit}
//           isOpen={isOverlayOpen}
//           onClose={() => setIsOverlayOpen(false)}
//           payment={selectedPayment ?? undefined}  // Ensure this is passed as undefined if null
//           userRole={user?.role || 'accountant'}
//         />
//       )}
//     </div>
//   );
// }




// "use client";

// import React, { useState } from "react";
// import { useQueryClient, useMutation, useQuery } from "@tanstack/react-query";
// import PaymentEditOverlay from "@/components/payments/payment-edit-overlay";
// import { getColumns } from "@/components/payments/columns";
// import { useAuth } from "@/context/AuthContext";
// import { Button } from "@/components/ui/button";
// import { Payment } from "@/app/types/payment";
// import { DataTable } from "@/components/payments/data-table";

// export default function AttendancePaymentsPage() {
//   const queryClient = useQueryClient();
//   const { user } = useAuth();

//   // Fetch payments
//   const { data: paymentsData, isLoading, isError, error } = useQuery({
//     queryKey: ["allPayments"],
//     queryFn: async () => {
//       const res = await fetch("/api/payments/getAllPayments");
//       if (!res.ok) {
//         throw new Error("Failed to fetch payments");
//       }
//       return res.json();
//     },
//   });

//   const [isOverlayOpen, setIsOverlayOpen] = useState(false);
//   const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);

//   const payments = paymentsData ? paymentsData.payments : [];

//   const paymentMutation = useMutation({
//     mutationFn: async ({ method, url, body }: { method: string; url: string; body: any }) => {
//       const response = await fetch(url, {
//         method,
//         headers: {
//           "Content-Type": "application/json",
//         },
//         body: JSON.stringify(body),
//       });
//       if (!response.ok) {
//         throw new Error(`HTTP error! status: ${response.status}`);
//       }
//       return response.json();
//     },
//     onSuccess: () => {
//       queryClient.invalidateQueries({ queryKey: ["allPayments"] });
//       setIsOverlayOpen(false);
//     },
//   });

//   const handleAddPayment = () => {
//     setSelectedPayment(null); // Reset selection when adding a new payment
//     setIsOverlayOpen(true);
//   };

//   const handleEditPayment = (payment: Payment) => {
//     setSelectedPayment(payment);
//     setIsOverlayOpen(true);
//   };

//   const handleDeletePayment = (paymentId: string) => {
//     paymentMutation.mutate({
//       method: "DELETE",
//       url: `/api/payments/deletePayment`,
//       body: { id: paymentId },
//     });
//   };

//   const handlePaymentSubmit = async (id: string, data: Partial<Payment>) => {
//     if (!user) {
//       console.error("User is not authenticated.");
//       return;
//     }

//     if (id === "") {
//       return paymentMutation.mutate({
//         method: "POST",
//         url: "/api/payments/createPayment",
//         body: { ...data, userRole: user.role },
//       });
//     } else {
//       return paymentMutation.mutate({
//         method: "PUT",
//         url: `/api/payments/updatePayment?id=${id}`,
//         body: data,
//       });
//     }
//   };

//   if (isLoading) {
//     return <div>Loading...</div>;
//   }

//   if (isError) {
//     return <div>Error loading payments: {(error as Error)?.message}</div>;
//   }

//   const columns = getColumns(handleEditPayment, handleDeletePayment, user?.role || 'accountant');

//   return (
//     <div className="container mx-auto py-10">
//       <div className="flex justify-between items-center mb-4">
//         <h1 className="text-2xl font-bold">Payment Management</h1>
//         <Button onClick={handleAddPayment} className="ml-4">
//           Add Payment
//         </Button>
//       </div>
//       <DataTable
//         columns={columns}
//         data={payments}
//         userRole={user?.role || 'accountant'}
//         onEdit={handleEditPayment}
//         onDelete={handleDeletePayment}
//       />
//       {isOverlayOpen && (
//         <PaymentEditOverlay
//           employees={payments.map((payment: { employee: any; }) => payment.employee)} // Assume employee data is part of payment records
//           onSubmit={handlePaymentSubmit}
//           isOpen={isOverlayOpen}
//           onClose={() => setIsOverlayOpen(false)}
//           payment={selectedPayment ?? undefined}  // Pass the selected payment if editing
//           userRole={user?.role || 'accountant'}
//         />
//       )}
//     </div>
//   );
// }



// "use client";

// import React, { useState } from "react";
// import { useQueryClient, useMutation, useQuery } from "@tanstack/react-query";
// import PaymentEditOverlay from "@/components/payments/payment-edit-overlay";
// import { getColumns } from "@/components/payments/columns";
// import { useAuth } from "@/context/AuthContext";
// import { Button } from "@/components/ui/button";
// import { Payment } from "@/app/types/payment";
// import { DataTable } from "@/components/payments/data-table";
// import { useEmployees } from "@/lib/fetchData"; // Import the hook for fetching employees
// import { useCombinedEmployeeAttendance } from "@/lib/fetchData";

// export default function AttendancePaymentsPage() {
//   const queryClient = useQueryClient();
//   const { user } = useAuth();

//   // Fetch payments
//   const { data: paymentsData, isLoading: isPaymentsLoading, isError: isPaymentsError, error: paymentsError } = useQuery({
//     queryKey: ["allPayments"],
//     queryFn: async () => {
//       const res = await fetch("/api/payments/getAllPayments");
//       if (!res.ok) {
//         throw new Error("Failed to fetch payments");
//       }
//       return res.json();
//     },
//   });

//   // Fetch all employees
//   const { data: employeesData, isLoading: isEmployeesLoading, isError: isEmployeesError, error: employeesError } = useCombinedEmployeeAttendance();

//   const [isOverlayOpen, setIsOverlayOpen] = useState(false);
//   const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);

//   const payments = paymentsData ? paymentsData.payments : [];
//   const employees = employeesData ? employeesData.employees : [];

//   const paymentMutation = useMutation({
//     mutationFn: async ({ method, url, body }: { method: string; url: string; body: any }) => {
//       const response = await fetch(url, {
//         method,
//         headers: {
//           "Content-Type": "application/json",
//         },
//         body: JSON.stringify(body),
//       });
//       if (!response.ok) {
//         throw new Error(`HTTP error! status: ${response.status}`);
//       }
//       return response.json();
//     },
//     onSuccess: () => {
//       queryClient.invalidateQueries({ queryKey: ["allPayments"] });
//       setIsOverlayOpen(false);
//     },
//   });

//   const handleAddPayment = () => {
//     setSelectedPayment(null); // Reset selection when adding a new payment
//     setIsOverlayOpen(true);
//   };

//   const handleEditPayment = (payment: Payment) => {
//     setSelectedPayment(payment);
//     setIsOverlayOpen(true);
//   };

//   const handleDeletePayment = (paymentId: string) => {
//     paymentMutation.mutate({
//       method: "DELETE",
//       url: `/api/payments/deletePayment`,
//       body: { id: paymentId },
//     });
//   };

//   const handlePaymentSubmit = async (id: string, data: Partial<Payment>) => {
//     if (!user) {
//       console.error("User is not authenticated.");
//       return;
//     }

//     if (id === "") {
//       return paymentMutation.mutate({
//         method: "POST",
//         url: "/api/payments/createPayment",
//         body: { ...data, userRole: user.role },
//       });
//     } else {
//       return paymentMutation.mutate({
//         method: "PUT",
//         url: `/api/payments/updatePayment?id=${id}`,
//         body: data,
//       });
//     }
//   };

//   if (isPaymentsLoading || isEmployeesLoading) {
//     return <div>Loading...</div>;
//   }

//   if (isPaymentsError) {
//     return <div>Error loading payments: {(paymentsError as Error)?.message}</div>;
//   }

//   if (isEmployeesError) {
//     return <div>Error loading employees: {(employeesError as Error)?.message}</div>;
//   }

//   const columns = getColumns(handleEditPayment, handleDeletePayment, user?.role || 'accountant');

//   return (
//     <div className="container mx-auto py-10">
//       <div className="flex justify-between items-center mb-4">
//         <h1 className="text-2xl font-bold">Payment Management</h1>
//         <Button onClick={handleAddPayment} className="ml-4">
//           Add Payment
//         </Button>
//       </div>
//       <DataTable
//         columns={columns}
//         data={payments}
//         userRole={user?.role || 'accountant'}
//         onEdit={handleEditPayment}
//         onDelete={handleDeletePayment}
//       />
//       {isOverlayOpen && (
//         <PaymentEditOverlay
//           employees={employees} // Pass all employees from the database
//           onSubmit={handlePaymentSubmit}
//           isOpen={isOverlayOpen}
//           onClose={() => setIsOverlayOpen(false)}
//           payment={selectedPayment ?? undefined}  // Pass the selected payment if editing
//           userRole={user?.role || 'accountant'}
//         />
//       )}
//     </div>
//   );
// }




"use client";

import React, { useState } from "react";
import { useQueryClient, useMutation, useQuery } from "@tanstack/react-query";
import PaymentEditOverlay from "@/components/payments/payment-edit-overlay";
import { getColumns } from "@/components/payments/columns";
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";
import { Payment } from "@/app/types/payment";
import { DataTable } from "@/components/payments/data-table";
import { useCombinedEmployeeAttendance } from "@/lib/fetchData"; // Use the combined employee-attendance hook

export default function AttendancePaymentsPage() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  // Fetch payments
  const { data: paymentsData, isLoading: isPaymentsLoading, isError: isPaymentsError, error: paymentsError } = useQuery({
    queryKey: ["allPayments"],
    queryFn: async () => {
      const res = await fetch("/api/payments/getAllPayments");
      if (!res.ok) {
        throw new Error("Failed to fetch payments");
      }
      return res.json();
    },
  });

  // Fetch combined employee and attendance data
  const { data: combinedData, isLoading: isCombinedLoading, isError: isCombinedError, error: combinedError } = useCombinedEmployeeAttendance();

  const [isOverlayOpen, setIsOverlayOpen] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);

  const payments = paymentsData ? paymentsData.payments : [];
  const employeesWithAttendance = combinedData ? combinedData.combinedData : [];

  const paymentMutation = useMutation({
    mutationFn: async ({ method, url, body }: { method: string; url: string; body: any }) => {
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["allPayments"] });
      setIsOverlayOpen(false);
    },
  });

  const handleAddPayment = () => {
    setSelectedPayment(null); // Reset selection when adding a new payment
    setIsOverlayOpen(true);
  };

  const handleEditPayment = (payment: Payment) => {
    setSelectedPayment(payment);
    setIsOverlayOpen(true);
  };

  const handleDeletePayment = (paymentId: string) => {
    paymentMutation.mutate({
      method: "DELETE",
      url: `/api/payments/deletePayment`,
      body: { id: paymentId },
    });
  };

  const handlePaymentSubmit = async (id: string, data: Partial<Payment>) => {
    if (!user) {
      console.error("User is not authenticated.");
      return;
    }

    if (id === "") {
      return paymentMutation.mutate({
        method: "POST",
        url: "/api/payments/createPayment",
        body: { ...data, userRole: user.role },
      });
    } else {
      return paymentMutation.mutate({
        method: "PUT",
        url: `/api/payments/updatePayment?id=${id}`,
        body: data,
      });
    }
  };

  if (isPaymentsLoading || isCombinedLoading) {
    return <div>Loading...</div>;
  }

  if (isPaymentsError) {
    return <div>Error loading payments: {(paymentsError as Error)?.message}</div>;
  }

  if (isCombinedError) {
    return <div>Error loading employees and attendance data: {(combinedError as Error)?.message}</div>;
  }

  const columns = getColumns(handleEditPayment, handleDeletePayment, user?.role || 'accountant');

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Payment Management</h1>
        <Button onClick={handleAddPayment} className="ml-4">
          Add Payment
        </Button>
      </div>
      <DataTable
        columns={columns}
        data={payments}
        userRole={user?.role || 'accountant'}
        onEdit={handleEditPayment}
        onDelete={handleDeletePayment}
      />
      {isOverlayOpen && (
        <PaymentEditOverlay
          employees={employeesWithAttendance.map((record: { employee: any; }) => record.employee)} // Pass all employees
          onSubmit={handlePaymentSubmit}
          isOpen={isOverlayOpen}
          onClose={() => setIsOverlayOpen(false)}
          payment={selectedPayment ?? undefined}  // Pass the selected payment if editing
          userRole={user?.role || 'accountant'}
        />
      )}
    </div>
  );
}
