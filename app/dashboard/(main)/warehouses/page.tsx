

// app/(main)/warehouses/page.tsx

"use client"
import * as React from "react"
import { DataTable } from "@/components/warehouses/data-table"
import { columns } from "@/components/warehouses/columns"
import { WarehouseForm } from "@/components/warehouses/warehouse-form"
import { Warehouse } from "@/app/types/warehouse"

// Example function to fetch warehouse data
async function getWarehouseData(): Promise<Warehouse[]> {
  // Example implementation: Replace with actual data fetching logic
  return [
    {
      warehouseId: "1",
      name: "Main Warehouse",
      location: "Lilongwe",
      capacity: 1000,
    },
    {
      warehouseId: "2",
      name: "Secondary Warehouse",
      location: "Blantyre",
      capacity: 750,
    },
    // Add more warehouse data as needed
  ]
}

export default function WarehousePage() {
  const [warehouses, setWarehouses] = React.useState<Warehouse[]>([])

  React.useEffect(() => {
    async function fetchWarehouseData() {
      const data = await getWarehouseData()
      setWarehouses(data)
    }
    fetchWarehouseData()
  }, [])

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Warehouse Management</h1>
        <WarehouseForm />
      </div>
      <DataTable columns={columns} data={warehouses} />
    </div>
  )
}
