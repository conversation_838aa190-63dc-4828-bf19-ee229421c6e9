// app/(main)/educationaleave/page.tsx

"use client"
import * as React from "react"
import { DataTable } from "@/components/leaves/data-table"
import { columns } from "@/components/leaves/columns"
import { Leave } from "@/app/types/leave"

// Example function to fetch leaves data
async function getLeaveData(): Promise<Leave[]> {
  // Example implementation: Replace with actual data fetching logic
  return [
    {
      leaveId: "1",
      employeeName: "Chimwemwe Banda",
      leaveType: "educational",
      startDate: "2024-07-15",
      endDate: "2024-07-20",
      reason: "Writing Exams",
      status: "approved",
    },
    {
      leaveId: "2",
      employeeName: "<PERSON><PERSON><PERSON>",
      leaveType: "educational",
      startDate: "2024-08-01",
      endDate: "2024-08-10",
      reason: "Continuing school",
      status: "pending",
    },
    {
      leaveId: "3",
      employeeName: "Chiso<PERSON> Phiri",
      leaveType: "educational",
      startDate: "2024-09-01",
      endDate: "2024-11-01",
      reason: "Writting ACCA certificate",
      status: "approved",
    },
    {
      leaveId: "4",
      employeeName: "<PERSON><PERSON>",
      leaveType: "educational",
      startDate: "2024-10-10",
      endDate: "2024-11-10",
      reason: "Educational Leave",
      status: "approved",
    },
    
    {
      leaveId: "8",
      employeeName: "Luka Mhone",
      leaveType: "educational",
      startDate: "2024-09-15",
      endDate: "2024-10-15",
      reason: "Further Studies",
      status: "pending",
    },
   
  ]
}

export default function EducationLeavePage() {
  const [leaves, setLeaves] = React.useState<Leave[]>([])

  React.useEffect(() => {
    async function fetchLeaveData() {
      const data = await getLeaveData()
      setLeaves(data)
    }
    fetchLeaveData()
  }, [])

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-2xl font-bold mb-4">Educational Leave </h1>
      <DataTable columns={columns} data={leaves} />
    </div>
  )
}
