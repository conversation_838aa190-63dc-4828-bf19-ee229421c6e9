import UserLogin from '@/app/auth/(auth)/UserLogin'
import UserRegistration from '@/app/auth/(auth)/UserRegistration'
import { AuthProvider } from '@/context/AuthContext'
import React from 'react'

const AddAminMember = () => {
    return (
        <>
            <AuthProvider>
                <section className="relative py-20 overflow-hidden bg-gray-800 bg-cover bg-center bg-no-repeat" style={{ backgroundImage: 'url("/bg.jpg")' }}>
                    <div className="absolute inset-0 bg-gradient-to-b from-black to-black opacity-20 z-10"></div>
                    <div className='relative z-20 h-screen'>
                        <div className='flex justify-center'>
                            <img src="/kawandamatpLogo.png" alt="Logo" className="h-40 w-40" />
                        </div>
                        <div className='flex justify-center items-center mt-10 '>
                            {/* <UserLogin /> */}
                            <UserRegistration />
                        </div>
                    </div>
                </section>
            </AuthProvider>
        </>
    )
}

export default AddAminMember