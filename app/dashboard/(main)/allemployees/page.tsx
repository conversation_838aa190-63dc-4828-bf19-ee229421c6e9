
// app/(main)/employees/page.tsx
"use client";

import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { DataTable } from "@/components/employees/data-table";
import { columns } from "@/components/employees/columns";
// import { EmployeeForm } from "@/components/employees/employee-form";
import EmployeeEditOverlay from "@/components/employees/employee-edit-overlay";
import EmployeeSuccessOverlay from "@/components/employees/employee-success-overlay";
import EmployeeErrorOverlay from "@/components/employees/employee-error-overlay";
import { Button } from "@/components/ui/button";
import { Employee } from "@/app/types/employee";
import { fetchEmployees, fetchDepartments } from "@/lib/fetchData";

export default function EmployeesPage() {
  const queryClient = useQueryClient();

  const {
    data: employeeData,
    error: employeeError,
    isError: isEmployeeError,
    isLoading: isEmployeeLoading,
    refetch,
  } = useQuery({
    queryKey: ["employees"],
    queryFn: fetchEmployees,
    refetchInterval: 30000,
    refetchIntervalInBackground: true,
  });

  const {
    data: departmentData,
    error: departmentError,
    isError: isDepartmentError,
    isLoading: isDepartmentLoading,
  } = useQuery({
    queryKey: ["departments"],
    queryFn: fetchDepartments,
  });

  const [isSuccess, setIsSuccess] = useState(false);
  const [isOverlayOpen, setIsOverlayOpen] = useState(false);
  const [isEditOverlayOpen, setIsEditOverlayOpen] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(
    null
  );
  const [showErrorOverlay, setShowErrorOverlay] = useState(false);

  // Ensure employees array is correctly extracted
  const employees: Employee[] = employeeData?.employees || [];
  const departments = departmentData ? departmentData.departments : [];

  const employeeMutation = useMutation({
    mutationFn: async ({
      method,
      url,
      body,
    }: {
      method: string;
      url: string;
      body: any;
    }) => {
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["employees"] });
      setIsSuccess(true);
      setIsOverlayOpen(false);
      setIsEditOverlayOpen(false);
    },
    onError: () => {
      setShowErrorOverlay(true);
    },
  });

  const handleAddEmployee = (employee: Omit<Employee, "_id">) => {
    employeeMutation.mutate({
      method: "POST",
      url: "/api/employees/createEmployee",
      body: employee,
    });
  };

  const handleUpdateEmployee = (
    employeeId: string,
    employee: Partial<Employee>
  ): Promise<void> => {
    const updatedFields = Object.fromEntries(
      Object.entries(employee).filter(([key, value]) => value !== undefined)
    );

    return new Promise((resolve, reject) => {
      employeeMutation.mutate({
        method: "PUT",
        url: `/api/employees/updateEmployee`,
        body: { id: employeeId, ...updatedFields },
      });
    });
  };

  const handleEditEmployee = (employee: Employee) => {
    setSelectedEmployee(employee);
    setIsEditOverlayOpen(true);
  };

  const handleDeleteEmployee = (employeeId: string) => {
    employeeMutation.mutate({
      method: "DELETE",
      url: `/api/employees/deleteEmployee`,
      body: { id: employeeId },
    });
  };

  const handleCloseSuccessOverlay = () => {
    setIsSuccess(false);
  };

  const handleCloseErrorOverlay = () => {
    setShowErrorOverlay(false);
  };

  if (isEmployeeLoading || isDepartmentLoading) {
    return <div>Loading...</div>;
  }

  if (isEmployeeError || isDepartmentError) {
    return (
      <div>
        Error loading data:{" "}
        {(employeeError as Error)?.message ||
          (departmentError as Error)?.message}
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Employee Management</h1>
        <Button onClick={() => setIsOverlayOpen(true)} className="ml-4">
          Add Employee
        </Button>
      </div>
      {employees.length > 0 ? (
        <DataTable
          columns={columns}
          data={employees}
          renderRowActions={(row: Employee) => (
            <div className="flex space-x-2">
              <Button size="sm" onClick={() => handleEditEmployee(row)}>
                Edit
              </Button>
              <Button
                size="sm"
                variant="destructive"
                onClick={() => handleDeleteEmployee(row._id)}
              >
                Delete
              </Button>
            </div>
          )}
        />
      ) : (
        <p>No employees found.</p>
      )}
      {isSuccess && (
        <EmployeeSuccessOverlay onClose={handleCloseSuccessOverlay} />
      )}
      {showErrorOverlay && (
        <EmployeeErrorOverlay onClose={handleCloseErrorOverlay} />
      )}
      {selectedEmployee && (
        <EmployeeEditOverlay
          isOpen={isEditOverlayOpen}
          onClose={() => setIsEditOverlayOpen(false)}
          employee={selectedEmployee}
          onSubmit={handleUpdateEmployee}
          departments={departments}
        />
      )}
      {/* {isOverlayOpen && (
        <EmployeeForm
          departments={departments}
          onSubmit={handleAddEmployee}
          isOpen={isOverlayOpen}
          onClose={() => setIsOverlayOpen(false)}
        />
      )} */}
    </div>
  );
}
