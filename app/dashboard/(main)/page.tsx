//app/dashboard/(main)/page.tsx

"use client";

import React from 'react';
import DashboardCard from '@/components/dashboard/DashboardCard';
import { Newspaper, Folder, Users, MessageCircle, FileText } from 'lucide-react';
import AttendancePaymentsPage from './attendancepayments/page';
import { DataTable } from '@/components/employees/data-table';
import { columns } from "@/components/employees/columns";
import { Employee } from "@/app/types/employee";
import { useEmployees, usePayments, useDepartments } from '@/lib/fetchData';
import { useAuth } from '@/context/AuthContext';
import ApprovedMonthlyPaymentsPage from './approvedMonthlyPayments/page';


export default function Home() {
  const { user } = useAuth(); // Get the logged-in user from the context

  const { data: employeeData, isLoading: isEmployeeLoading, isError: isEmployeeError } = useEmployees();
  const { data: paymentData, isLoading: isPaymentLoading, isError: isPaymentError } = usePayments();
  const { data: departmentData, isLoading: isDepartmentLoading, isError: isDepartmentError } = useDepartments();

  const employees: Employee[] = employeeData ? employeeData.employees : [];
  const payments = paymentData ? paymentData.payments : [];
  const departments = departmentData ? departmentData.departments : [];

  if (isEmployeeLoading || isPaymentLoading || isDepartmentLoading) {
    return <div>Loading...</div>;
  }

  if (isEmployeeError || isPaymentError || isDepartmentError) {
    return <div>Error loading data: {(isEmployeeError ? 'Employees' : isPaymentError ? 'Payments' : 'Departments')} data.</div>;
  }

  const canViewEmployeeData = user?.role !== 'accountant';
  const canViewAttendancePaymentPage = ['accountant', 'admin', 'ceoadmin'].includes(user?.role || '');

  return (
    <>
      <h1 className='text-2xl mb-4'>Dashboard</h1>
      <div className='flex justify-between gap-5 mb-5'>
        <DashboardCard
          title='Employees'
          count={employees.length}
          icon={<Users size={52} className='text-green-500' />}
        />
        <DashboardCard
          title='Tasks'
          count={0} // Update as needed
          icon={<Folder size={52} className='text-green-500' />}
        />
        <DashboardCard
          title='Invoices'
          count={0} // Update as needed
          icon={<FileText size={52} className='text-green-500' />}
        />
        <DashboardCard
          title='Payments'
          count={payments.length}
          icon={<MessageCircle size={52} className='text-green-500' />}
        />
      </div>

      {canViewAttendancePaymentPage && (
        <AttendancePaymentsPage />
      )}

      {canViewEmployeeData && (
        <div className="container mx-auto py-10">
          <h2 className='text-gray-700 text-2xl'>Employees</h2>
          <DataTable columns={columns} data={employees} />
        </div>
      )}

      <ApprovedMonthlyPaymentsPage />
    </>
  );
}
