// app/dashboard/(main)/layout.tsx
"use client"

import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import '../../globals.css';
import { Toaster } from '@/components/ui/toaster';
import { ThemeProvider } from '@/components/providers/ThemeProvider';
import { AuthProvider } from '@/context/AuthContext';
import Navbar from '@/components/navigation/Navbar';
import Sidebar from '@/components/navigation/Sidebar';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const inter = Inter({ subsets: ['latin'] });

const queryClient = new QueryClient();

const metadata: Metadata = {
  title: 'Kawandama hr',
  description: 'Kawandama hr system',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang='en' suppressHydrationWarning>
      <body className={inter.className}>
        <QueryClientProvider client={queryClient}>
          <AuthProvider>
            <Navbar />
            <ThemeProvider
              attribute='class'
              defaultTheme='light'
              enableSystem={false}
              storageKey='dashboard-theme'
            >
              <div className='flex'>
                <div className='hidden md:block h-[100vh]'>
                  <Sidebar />
                </div>
                <div className='p-5 w-full md:max-w-[1140px]'>{children}</div>
              </div>
              <Toaster />
            </ThemeProvider>
          </AuthProvider>
        </QueryClientProvider>
      </body>
    </html>
  );
}
