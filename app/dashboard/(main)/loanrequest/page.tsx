
// app/(main)/loanrequests/page.tsx
"use client"
import * as React from "react"
import { DataTable } from "@/components/loans/data-table"
import { columns } from "@/components/loans/columns"
import { Loan } from "@/app/types/loan"

// Example function to fetch loans data
// async function getLoanData(): Promise<Loan[]> {
//   // Example implementation: Replace with actual data fetching logic
//   return [
   
//     {
//       loanId: "2",
//       borrower: "Malombo Uchindami",
//       loanAmount: 8000,
//       interestRate: 0.0,
//       dueDate: "2024-07-20",
//       status: "pending",
//     },
   
//     {
//       loanId: "4",
//       borrower: "Kenith Ka<PERSON>za",
//       loanAmount: 8000,
//       interestRate: 0.0,
//       dueDate: "2024-07-20",
//       status: "pending",
//     },
  
//     {
//       loanId: "6",
//       borrower: "Welcome Makawa",
//       loanAmount: 8000,
//       interestRate: 0.0,
//       dueDate: "2024-07-20",
//       status: "pending",
//     },
//     // Add more loans as needed
//   ]
// }

async function getLoanData(): Promise<Loan[]> {
  return [
    {
      _id: "2",
      employee: "Malombo Uchindami",
      amount: 8000,
      interestRate: 0.0,
      startDate: "2024-07-20",
      endDate: "2024-07-20",
      status: "rejected",
      repaymentSchedule: "monthly",
    },
    {
      _id: "4",
      employee: "Kenith Kamoza",
      amount: 8000,
      interestRate: 0.0,
      startDate: "2024-07-20",
      endDate: "2024-07-20",
      status: "rejected",
      repaymentSchedule: "monthly",
    },
    {
      _id: "6",
      employee: "Welcome Makawa",
      amount: 8000,
      interestRate: 0.0,
      startDate: "2024-07-20",
      endDate: "2024-07-20",
      status: "rejected",
      repaymentSchedule: "monthly",
    },
  ]
}

export default function LoanRequestPage() {
  const [loans, setLoans] = React.useState<Loan[]>([])

  React.useEffect(() => {
    async function fetchLoanData() {
      const data = await getLoanData()
      setLoans(data)
    }
    fetchLoanData()
  }, [])

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-2xl font-bold mb-4">New and Pending Loan Requests</h1>
      <DataTable columns={columns} data={loans} />
    </div>
  )
}
