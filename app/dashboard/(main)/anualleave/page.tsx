// // app/(main)/anualleave/page.tsx

// "use client"
// import * as React from "react"
// import { DataTable } from "@/components/leaves/data-table"
// import { columns } from "@/components/leaves/columns"
// import { Leave } from "@/app/types/leave"

// // Example function to fetch leaves data
// async function getLeaveData(): Promise<Leave[]> {
//   // Example implementation: Replace with actual data fetching logic
//   return [
//     {
//       leaveId: "1",
//       employeeName: "John Doe",
//       leaveType: "annual", // Added leaveType
//       startDate: "2024-07-15",
//       endDate: "2024-07-20",
//       reason: "Vacation",
//       status: "approved",
//     },
//     {
//       leaveId: "2",
//       employeeName: "<PERSON>",
//       leaveType: "sick", // Added leaveType
//       startDate: "2024-08-01",
//       endDate: "2024-08-10",
//       reason: "Medical",
//       status: "pending",
//     },
//     // Add more leave data as needed
//   ]
// }

// export default function AnualLeavePage() {
//   const [leaves, setLeaves] = React.useState<Leave[]>([])

//   React.useEffect(() => {
//     async function fetchLeaveData() {
//       const data = await getLeaveData()
//       setLeaves(data)
//     }
//     fetchLeaveData()
//   }, [])

//   return (
//     <div className="container mx-auto py-10">
//       <h1 className="text-2xl font-bold mb-4">Annual Leave Management</h1>
//       <DataTable columns={columns} data={leaves} />
//     </div>
//   )
// }



// app/(main)/anualleave/page.tsx

"use client"
import * as React from "react"
import { DataTable } from "@/components/leaves/data-table"
import { columns } from "@/components/leaves/columns"
import { Leave } from "@/app/types/leave"

// Example function to fetch leaves data
async function getLeaveData(): Promise<Leave[]> {
  // Example implementation: Replace with actual data fetching logic
  return [
    {
      leaveId: "1",
      employeeName: "Chimwemwe Banda",
      leaveType: "annual",
      startDate: "2024-07-15",
      endDate: "2024-07-20",
      reason: "Vacation",
      status: "approved",
    },
    {
      leaveId: "2",
      employeeName: "Thoko Mvula",
      leaveType: "sick",
      startDate: "2024-08-01",
      endDate: "2024-08-10",
      reason: "Medical",
      status: "pending",
    },
    {
      leaveId: "3",
      employeeName: "Chisomo Phiri",
      leaveType: "maternity",
      startDate: "2024-09-01",
      endDate: "2024-11-01",
      reason: "Maternity Leave",
      status: "approved",
    },
    {
      leaveId: "4",
      employeeName: "Wati Kazembe",
      leaveType: "educational",
      startDate: "2024-10-10",
      endDate: "2024-11-10",
      reason: "Educational Leave",
      status: "approved",
    },
    {
      leaveId: "5",
      employeeName: "Grace Chirwa",
      leaveType: "annual",
      startDate: "2024-07-01",
      endDate: "2024-07-14",
      reason: "Annual Vacation",
      status: "approved",
    },
    {
      leaveId: "6",
      employeeName: "Yamikani Ngwira",
      leaveType: "funeral",
      startDate: "2024-08-05",
      endDate: "2024-08-07",
      reason: "Funeral",
      status: "pending",
    },
    {
      leaveId: "7",
      employeeName: "Taonga Zimba",
      leaveType: "sick",
      startDate: "2024-07-20",
      endDate: "2024-07-25",
      reason: "Medical",
      status: "approved",
    },
    {
      leaveId: "8",
      employeeName: "Luka Mhone",
      leaveType: "educational",
      startDate: "2024-09-15",
      endDate: "2024-10-15",
      reason: "Further Studies",
      status: "pending",
    },
    {
      leaveId: "9",
      employeeName: "Mwawi Mkandawire",
      leaveType: "annual",
      startDate: "2024-06-15",
      endDate: "2024-06-25",
      reason: "Annual Leave",
      status: "rejected",
    },
    {
      leaveId: "10",
      employeeName: "Zione Mhango",
      leaveType: "maternity",
      startDate: "2024-07-01",
      endDate: "2024-09-30",
      reason: "Maternity Leave",
      status: "approved",
    },
  ]
}

export default function AnualLeavePage() {
  const [leaves, setLeaves] = React.useState<Leave[]>([])

  React.useEffect(() => {
    async function fetchLeaveData() {
      const data = await getLeaveData()
      setLeaves(data)
    }
    fetchLeaveData()
  }, [])

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-2xl font-bold mb-4">Annual Leave Management</h1>
      <DataTable columns={columns} data={leaves} />
    </div>
  )
}
