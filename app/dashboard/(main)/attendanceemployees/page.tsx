// import React from 'react'

// const AttendanceEmployeesPage = () => {
//   return (
//     <div>AttendanceEmployeesPage</div>
//   )
// }

// export default AttendanceEmployeesPage





"use client";

import * as React from "react";
import { DataTable } from "@/components/employees/data-table";
import { columns } from "@/components/employees/columns";
import { EmployeeForm } from "@/components/employees/employee-form";
import { Employee } from "@/app/types/employee";
import EmployeeEditOverlay from "@/components/employees/employee-edit-overlay";
import EmployeeSuccessOverlay from "@/components/employees/employee-success-overlay";
import EmployeeErrorOverlay from "@/components/employees/employee-error-overlay";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { Button } from "@/components/ui/button";

type FetchError = {
  message: string;
};

// Fetch employees function
const fetchEmployees = async () => {
  const response = await fetch("/api/employees/getAllEmployees");
  if (!response.ok) {
    throw new Error("Failed to fetch employees");
  }
  return response.json();
};

// Fetch departments function
const fetchDepartments = async () => {
  const response = await fetch("/api/departments/getAllDepartments");
  if (!response.ok) {
    throw new Error("Failed to fetch departments");
  }
  return response.json();
};

export default function AttendanceEmployeesPage() {
  const queryClient = useQueryClient();
  const {
    data: employeeData,
    error: employeeError,
    isError: isEmployeeError,
    isLoading: isEmployeeLoading,
  } = useQuery({
    queryKey: ["employees"],
    queryFn: fetchEmployees,
  });
  const {
    data: departmentData,
    error: departmentError,
    isError: isDepartmentError,
    isLoading: isDepartmentLoading,
  } = useQuery({
    queryKey: ["departments"],
    queryFn: fetchDepartments,
  });

  const [isSuccess, setIsSuccess] = useState(false);
  const [isOverlayOpen, setIsOverlayOpen] = useState(false);
  const [isEditOverlayOpen, setIsEditOverlayOpen] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(
    null
  );
  const [showErrorOverlay, setShowErrorOverlay] = useState(false);

  const employees = employeeData ? employeeData.employees : [];
  const departments = departmentData ? departmentData.departments : [];

  const employeeMutation = useMutation({
    mutationFn: async ({
      method,
      url,
      body,
    }: {
      method: string;
      url: string;
      body: any;
    }) => {
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["employees"] });
      setIsSuccess(true);
      setIsOverlayOpen(false);
      setIsEditOverlayOpen(false);
    },
    onError: () => {
      setShowErrorOverlay(true);
    },
  });

  const handleAddEmployee = (employee: Omit<Employee, "_id">) => {
    employeeMutation.mutate({
      method: "POST",
      url: "/api/employees/createEmployee",
      body: employee,
    });
  };

  const handleUpdateEmployee = (
    employeeId: string,
    employee: Partial<Employee>
  ): Promise<void> => {
    const updatedFields = Object.fromEntries(
      Object.entries(employee).filter(([key, value]) => value !== undefined)
    );

    return new Promise((resolve, reject) => {
      employeeMutation.mutate({
        method: "PUT",
        url: `/api/employees/updateEmployee`,
        body: { id: employeeId, ...updatedFields },  // Pass the id in the body
      });
    });
  };

  const handleEditEmployee = (employee: Employee) => {
    setSelectedEmployee(employee);
    setIsEditOverlayOpen(true);
  };

  const handleDeleteEmployee = (employeeId: string) => {
    employeeMutation.mutate({
      method: "DELETE",
      url: `/api/employees/deleteEmployee`,
      body: { id: employeeId },  // Send the employeeId in the request body
    });
  };

  const handleCloseSuccessOverlay = () => {
    setIsSuccess(false);
  };

  const handleCloseErrorOverlay = () => {
    setShowErrorOverlay(false);
  };

  if (isEmployeeLoading || isDepartmentLoading) {
    return <div>Loading...</div>;
  }

  if (isEmployeeError || isDepartmentError) {
    return (
      <div>
        Error loading data: {(employeeError as FetchError)?.message || (departmentError as FetchError)?.message}
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Employee Management</h1>
        <Button onClick={() => setIsOverlayOpen(true)} className="ml-4">
          Add Employee
        </Button>
      </div>
      {isEmployeeLoading ? (
        <p>Loading...</p>
      ) : isEmployeeError ? (
        <p>Error fetching data: {(employeeError as FetchError).message}</p>
      ) : (
        <DataTable
          columns={columns}
          data={employees}
          renderRowActions={(row: Employee) => (
            <div className="flex space-x-2">
              <Button size="sm" onClick={() => handleEditEmployee(row)}>
                Edit
              </Button>
              <Button size="sm" variant="destructive" onClick={() => handleDeleteEmployee(row._id)}>
                Delete
              </Button>
            </div>
          )}
        />
      )}
      {isSuccess && (
        <EmployeeSuccessOverlay onClose={handleCloseSuccessOverlay} />
      )}
      {showErrorOverlay && (
        <EmployeeErrorOverlay onClose={handleCloseErrorOverlay} />
      )}
      {selectedEmployee && (
        <EmployeeEditOverlay
          isOpen={isEditOverlayOpen}
          onClose={() => setIsEditOverlayOpen(false)}
          employee={selectedEmployee}
          onSubmit={handleUpdateEmployee}
          departments={departments} // Pass the departments array
        />
      )}
      {isOverlayOpen && (
        <EmployeeForm
          departments={departments}
          onSubmit={handleAddEmployee}
          isOpen={isOverlayOpen}
          onClose={() => setIsOverlayOpen(false)}
        />
      )}
    </div>
  );
}
