// app/dashboard/(main)/attendance/page.tsx
"use client"
import { useEffect, useState } from "react";
import { DataTable } from "@/components/attendance/data-table";
import { columns } from "@/components/attendance/columns";
import { Attendance } from "@/app/types/attendance";

export default function AttendancePage() {
  const [attendanceData, setAttendanceData] = useState<Attendance[]>([]);

  const fetchAttendance = async () => {
    try {
      const response = await fetch('/api/attendance/getAllAttendance');
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch attendance data: ${response.status} - ${response.statusText}: ${errorText}`);
      }
      const data = await response.json();

      const transformedData = data.attendanceRecords.map((record: any) => ({
        id: record._id,
        date: new Date(record.date).toLocaleDateString(),
        status: record.isPresent ? "present" : "absent", // Assuming 'isPresent' is boolean
        employeeId: record.employee._id,
        firstname: record.employee.firstName,
        lastname: record.employee.lastName,
      }));

      setAttendanceData(transformedData);
    } catch (error) {
      if (error instanceof Error) {
        console.error("Error fetching attendance data:", error.message);
      } else {
        console.error("Unknown error fetching attendance data:", error);
      }
    }
  };

  useEffect(() => {
    // Initial fetch
    fetchAttendance();

    // Polling every 30 seconds
    const intervalId = setInterval(fetchAttendance, 30000);

    // Cleanup on unmount
    return () => clearInterval(intervalId);
  }, []);

  return (
    <div>
      <h1 className="text-2xl font-bold mb-4">Today Attendance Records</h1>
      <DataTable columns={columns} data={attendanceData} />
    </div>
  );
}
