
// app/(main)/taskmanagement/page.tsx

import { Task } from "@/app/types/task"
import { columns } from "@/components/tasks/columns"
import { DataTable } from "@/components/tasks/data-table"


// "not started" | "in progress" | "completed"
async function getTaskData(): Promise<Task[]> {
  return [
    {
      id: "1",
      title: "Plant New Trees",
      description: "Plant 500 new trees in sector A.",
      status: "not started",
      priority: "high",
      assignee: "<PERSON>",
      dueDate: "2024-07-15",
    },
    {
      id: "2",
      title: "Harvest Citron Leaves",
      description: "Harvest citron leaves from sector B.",
      status: "in progress",
      priority: "medium",
      assignee: "<PERSON>",
      dueDate: "2024-07-20",
    },
    {
      id: "3",
      title: "Inspect Timber Quality",
      description: "Inspect the quality of timber in storage.",
      status: "completed",
      priority: "high",
      assignee: "<PERSON> Johnson",
      dueDate: "2024-07-18",
    },
    {
      id: "4",
      title: "Produce Charcoal",
      description: "Produce 100 bags of charcoal.",
      status: "completed",
      priority: "low",
      assignee: "<PERSON>",
      dueDate: "2024-07-22",
    },
    {
      id: "5",
      title: "Manufacture Hand Sanitizer",
      description: "Produce 200 liters of hand sanitizer.",
      status: "completed",
      priority: "medium",
      assignee: "Eve White",
      dueDate: "2024-07-25",
    },
    {
      id: "6",
      title: "Maintain Equipment",
      description: "Perform maintenance on all harvesting equipment.",
      status: "completed",
      priority: "low",
      assignee: "Charlie Black",
      dueDate: "2024-07-30",
    },
    {
      id: "7",
      title: "Water New Plantations",
      description: "Ensure all new plantations are watered daily.",
      status: "completed",
      priority: "high",
      assignee: "Daisy Green",
      dueDate: "2024-07-28",
    },
    {
      id: "8",
      title: "Fertilize Soil",
      description: "Apply fertilizer to sector C.",
      status: "completed",
      priority: "medium",
      assignee: "Edward Blue",
      dueDate: "2024-07-27",
    },
    {
      id: "9",
      title: "Safety Inspection",
      description: "Conduct a safety inspection of the processing plant.",
      status: "completed",
      priority: "high",
      assignee: "Fiona Yellow",
      dueDate: "2024-07-29",
    },
    {
      id: "10",
      title: "Pack Timber for Shipment",
      description: "Prepare and pack timber for shipment.",
      status: "completed",
      priority: "medium",
      assignee: "George Red",
      dueDate: "2024-08-01",
    },
  ]
}

export default async function TaskManagementPage() {
  const tasks = await getTaskData()

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-2xl font-bold mb-4">All Tasks</h1>
      <DataTable columns={columns} data={tasks} />
    </div>
  )
}
