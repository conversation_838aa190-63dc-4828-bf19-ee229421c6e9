// // app/(main)/departments/page.tsx

// "use client"
// import * as React from "react"
// import { DataTable } from "@/components/departments/data-table"
// import { columns } from "@/components/departments/columns"
// import { DepartmentForm } from "@/components/departments/department-form"
// import { Department } from "@/app/types/department"
// import DepartmentEditOverlay from "@/components/departments/department-edit-overlay"
// import DepartmentSuccessOverlay from "@/components/departments/department-success-overlay"
// import DepartmentErrorOverlay from "@/components/departments/department-error-overlay"

// export default function DepartmentsPage() {
//     const [departments, setDepartments] = React.useState<Department[]>([])
//     const [selectedDepartment, setSelectedDepartment] = React.useState<Department | null>(null)
//     const [isOverlayOpen, setIsOverlayOpen] = React.useState(false)
//     const [isEditOverlayOpen, setIsEditOverlayOpen] = React.useState(false)
//     const [isSuccess, setIsSuccess] = React.useState(false)
//     const [isError, setIsError] = React.useState(false)

//     React.useEffect(() => {
//         fetchDepartments()
//     }, [])

//     const fetchDepartments = async () => {
//         try {
//             const response = await fetch('/api/departments/getAllDepartments')
//             if (!response.ok) {
//                 throw new Error('Failed to fetch departments')
//             }
//             const data = await response.json()
//             setDepartments(data.departments)
//         } catch (error) {
//             console.error('Error fetching departments:', error)
//         }
//     }

//     const handleDepartmentSubmit = async (departmentData: Omit<Department, '_id'>) => {
//         try {
//             const response = await fetch('/api/departments/createDepartment', {
//                 method: 'POST',
//                 headers: {
//                     'Content-Type': 'application/json',
//                 },
//                 body: JSON.stringify(departmentData),
//             })
//             if (!response.ok) {
//                 throw new Error(`HTTP error! status: ${response.status}`)
//             }
//             const result = await response.json()
//             console.log("Department created:", result)
//             setIsSuccess(true)
//             fetchDepartments() // Refresh the department list
//         } catch (error) {
//             console.error('There was a problem with the create operation:', error)
//             setIsError(true)
//         } finally {
//             setIsOverlayOpen(false)
//         }
//     }

//     const handleDepartmentUpdate = async (id: string, departmentData: Partial<Department>) => {
//         try {
//             const response = await fetch('/api/departments/updateDepartment', {
//                 method: 'PUT',
//                 headers: {
//                     'Content-Type': 'application/json',
//                 },
//                 body: JSON.stringify({ id, ...departmentData }),  // Send 'id' instead of '_id'
//             })
//             if (!response.ok) {
//                 throw new Error(`HTTP error! status: ${response.status}`)
//             }
//             const result = await response.json()
//             console.log("Department updated:", result)
//             setIsSuccess(true)
//             fetchDepartments() // Refresh the department list
//         } catch (error) {
//             console.error('There was a problem with the update operation:', error)
//             setIsError(true)
//         }
//     }

//     const handleDepartmentDelete = async (id: string) => {
//         try {
//             const response = await fetch('/api/departments/deleteDepartment', {
//                 method: 'DELETE',
//                 headers: {
//                     'Content-Type': 'application/json',
//                 },
//                 body: JSON.stringify({ id }),
//             })
//             if (!response.ok) {
//                 throw new Error(`HTTP error! status: ${response.status}`)
//             }
//             const result = await response.json()
//             console.log("Department deleted:", result)
//             setIsSuccess(true)
//             fetchDepartments() // Refresh the department list
//         } catch (error) {
//             console.error('There was a problem with the delete operation:', error)
//             setIsError(true)
//         }
//     }

//     const toggleOverlay = () => {
//         setIsOverlayOpen(!isOverlayOpen)
//     }

//     const toggleEditOverlay = (department?: Department) => {
//         if (department) {
//             setSelectedDepartment(department)
//         }
//         setIsEditOverlayOpen(!isEditOverlayOpen)
//     }

//     return (
//         <div className="container mx-auto py-10">
//             <div className="flex justify-between items-center mb-4">
//                 <h1 className="text-2xl font-bold">Department Management</h1>
//                 <DepartmentForm onSubmit={handleDepartmentSubmit} />
//             </div>
//             <DataTable columns={columns} data={departments} onEdit={toggleEditOverlay} onDelete={handleDepartmentDelete} />
//             {isSuccess && <DepartmentSuccessOverlay onClose={() => setIsSuccess(false)} />}
//             {isError && <DepartmentErrorOverlay onClose={() => setIsError(false)} />}
//             {selectedDepartment && (
//                 <DepartmentEditOverlay
//                     isOpen={isEditOverlayOpen}
//                     onClose={() => toggleEditOverlay()}
//                     department={selectedDepartment}
//                     onSubmit={handleDepartmentUpdate}
//                 />
//             )}
//         </div>
//     )
// }


// "use client"
// import * as React from "react";
// import { DataTable } from "@/components/departments/data-table";
// import { columns } from "@/components/departments/columns";
// import { DepartmentForm } from "@/components/departments/department-form";
// import { Department } from "@/app/types/department";
// import DepartmentEditOverlay from "@/components/departments/department-edit-overlay";
// import DepartmentSuccessOverlay from "@/components/departments/department-success-overlay";
// import DepartmentErrorOverlay from "@/components/departments/department-error-overlay";
// import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
// import { useState } from "react";

// // Fetch departments function
// const fetchDepartments = async () => {
//     const response = await fetch('/api/departments/getAllDepartments');
//     if (!response.ok) {
//         throw new Error('Failed to fetch departments');
//     }
//     return response.json();
// };

// export default function DepartmentsPage() {
//     const queryClient = useQueryClient();
//     const { data, error, isError, isLoading } = useQuery({
//         queryKey: ['departments'],
//         queryFn: fetchDepartments,
//     });
//     const [isSuccess, setIsSuccess] = useState(false);
//     const [isOverlayOpen, setIsOverlayOpen] = useState(false);
//     const [isEditOverlayOpen, setIsEditOverlayOpen] = useState(false);
//     const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
//     const [showErrorOverlay, setShowErrorOverlay] = useState(false);

//     const departments = data ? data.departments : [];

//     const departmentMutation = useMutation({
//         mutationFn: async ({ method, url, body }: { method: string, url: string, body: any }) => {
//             const response = await fetch(url, {
//                 method,
//                 headers: {
//                     'Content-Type': 'application/json',
//                 },
//                 body: JSON.stringify(body),
//             });
//             if (!response.ok) {
//                 throw new Error(`HTTP error! status: ${response.status}`);
//             }
//             return response.json();
//         },
//         onSuccess: () => {
//             queryClient.invalidateQueries({ queryKey: ['departments'] });
//             setIsSuccess(true);
//         },
//         onError: () => {
//             setShowErrorOverlay(true);
//         },
//         onSettled: () => {
//             setIsOverlayOpen(false);
//         },
//     });

//     const handleDepartmentSubmit = async (departmentData: Omit<Department, '_id'>) => {
//         departmentMutation.mutate({ method: 'POST', url: '/api/departments/createDepartment', body: departmentData });
//     };

//     const handleDepartmentUpdate = async (id: string, departmentData: Partial<Department>) => {
//         departmentMutation.mutate({ method: 'PUT', url: '/api/departments/updateDepartment', body: { id, ...departmentData } });
//     };

//     const handleDepartmentDelete = async (id: string) => {
//         departmentMutation.mutate({ method: 'DELETE', url: '/api/departments/deleteDepartment', body: { id } });
//     };

//     const toggleOverlay = () => {
//         setIsOverlayOpen(!isOverlayOpen);
//     };

//     const toggleEditOverlay = (department?: Department) => {
//         if (department) {
//             setSelectedDepartment(department);
//         }
//         setIsEditOverlayOpen(!isEditOverlayOpen);
//     };

//     return (
//         <div className="container mx-auto py-10">
//             <div className="flex justify-between items-center mb-4">
//                 <h1 className="text-2xl font-bold">Department Management</h1>
//                 <DepartmentForm onSubmit={handleDepartmentSubmit} />
//             </div>
//             {isLoading ? (
//                 <p>Loading...</p>
//             ) : isError ? (
//                 <p>Error fetching data: {error.message}</p>
//             ) : (
//                 <DataTable columns={columns} data={departments} onEdit={toggleEditOverlay} onDelete={handleDepartmentDelete} />
//             )}
//             {isSuccess && <DepartmentSuccessOverlay onClose={() => setIsSuccess(false)} />}
//             {showErrorOverlay && <DepartmentErrorOverlay onClose={() => setShowErrorOverlay(false)} />}
//             {selectedDepartment && (
//                 <DepartmentEditOverlay
//                     isOpen={isEditOverlayOpen}
//                     onClose={() => toggleEditOverlay()}
//                     department={selectedDepartment}
//                     onSubmit={handleDepartmentUpdate}
//                 />
//             )}
//         </div>
//     );
// }


// "use client"
// import * as React from "react";
// import { DataTable } from "@/components/departments/data-table";
// import { columns } from "@/components/departments/columns";
// import { DepartmentForm } from "@/components/departments/department-form";
// import { Department } from "@/app/types/department";
// import DepartmentEditOverlay from "@/components/departments/department-edit-overlay";
// import DepartmentSuccessOverlay from "@/components/departments/department-success-overlay";
// import DepartmentErrorOverlay from "@/components/departments/department-error-overlay";
// import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
// import { useState } from "react";

// // Fetch departments function
// const fetchDepartments = async () => {
//     const response = await fetch('/api/departments/getAllDepartments');
//     if (!response.ok) {
//         throw new Error('Failed to fetch departments');
//     }
//     return response.json();
// };

// export default function DepartmentsPage() {
//     const queryClient = useQueryClient();
//     const { data, error, isError, isLoading } = useQuery({
//         queryKey: ['departments'],
//         queryFn: fetchDepartments,
//     });
//     const [isSuccess, setIsSuccess] = useState(false);
//     const [isOverlayOpen, setIsOverlayOpen] = useState(false);
//     const [isEditOverlayOpen, setIsEditOverlayOpen] = useState(false);
//     const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
//     const [showErrorOverlay, setShowErrorOverlay] = useState(false);

//     const departments = data ? data.departments : [];

//     const departmentMutation = useMutation({
//         mutationFn: async ({ method, url, body }: { method: string, url: string, body: any }) => {
//             const response = await fetch(url, {
//                 method,
//                 headers: {
//                     'Content-Type': 'application/json',
//                 },
//                 body: JSON.stringify(body),
//             });
//             if (!response.ok) {
//                 throw new Error(`HTTP error! status: ${response.status}`);
//             }
//             return response.json();
//         },
//         onSuccess: () => {
//             queryClient.invalidateQueries({ queryKey: ['departments'] });
//             setIsSuccess(true);
//         },
//         onError: () => {
//             setShowErrorOverlay(true);
//         },
//         onSettled: () => {
//             setIsOverlayOpen(false);
//         },
//     });

//     const handleDepartmentSubmit = async (departmentData: Omit<Department, '_id'>) => {
//         departmentMutation.mutate({ method: 'POST', url: '/api/departments/createDepartment', body: departmentData });
//     };

//     const handleDepartmentUpdate = async (id: string, departmentData: Partial<Department>) => {
//         departmentMutation.mutate({ method: 'PUT', url: '/api/departments/updateDepartment', body: { id, ...departmentData } });
//     };

//     const handleDepartmentDelete = async (id: string) => {
//         departmentMutation.mutate({ method: 'DELETE', url: '/api/departments/deleteDepartment', body: { id } });
//     };

//     const toggleOverlay = () => {
//         setIsOverlayOpen(!isOverlayOpen);
//     };

//     const toggleEditOverlay = (department?: Department) => {
//         if (department) {
//             setSelectedDepartment(department);
//         }
//         setIsEditOverlayOpen(!isEditOverlayOpen);
//     };

//     return (
//         <div className="container mx-auto py-10">
//             <div className="flex justify-between items-center mb-4">
//                 <h1 className="text-2xl font-bold">Department Management</h1>
//                 <DepartmentForm onSubmit={handleDepartmentSubmit} />
//             </div>
//             {isLoading ? (
//                 <p>Loading...</p>
//             ) : isError ? (
//                 <p>Error fetching data: {error.message}</p>
//             ) : (
//                 <DataTable columns={columns} data={departments} onEdit={toggleEditOverlay} onDelete={handleDepartmentDelete} />
//             )}
//             {isSuccess && <DepartmentSuccessOverlay onClose={() => setIsSuccess(false)} />}
//             {showErrorOverlay && <DepartmentErrorOverlay onClose={() => setShowErrorOverlay(false)} />}
//             {selectedDepartment && (
//                 <DepartmentEditOverlay
//                     isOpen={isEditOverlayOpen}
//                     onClose={() => toggleEditOverlay()}
//                     department={selectedDepartment}
//                     onSubmit={handleDepartmentUpdate}
//                 />
//             )}
//         </div>
//     );
// }



"use client"
import * as React from "react";
import { DataTable } from "@/components/departments/data-table";
import { columns } from "@/components/departments/columns";
import { DepartmentForm } from "@/components/departments/department-form";
import { Department } from "@/app/types/department";
import DepartmentEditOverlay from "@/components/departments/department-edit-overlay";
import DepartmentSuccessOverlay from "@/components/departments/department-success-overlay";
import DepartmentErrorOverlay from "@/components/departments/department-error-overlay";
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from "react";
import { Button } from "@/components/ui/button";

// Fetch departments function
const fetchDepartments = async () => {
    const response = await fetch('/api/departments/getAllDepartments');
    if (!response.ok) {
        throw new Error('Failed to fetch departments');
    }
    return response.json();
};

export default function DepartmentsPage() {
    const queryClient = useQueryClient();
    const { data, error, isError, isLoading } = useQuery({
        queryKey: ['departments'],
        queryFn: fetchDepartments,
    });
    const [isSuccess, setIsSuccess] = useState(false);
    const [isOverlayOpen, setIsOverlayOpen] = useState(false);
    const [isEditOverlayOpen, setIsEditOverlayOpen] = useState(false);
    const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
    const [showErrorOverlay, setShowErrorOverlay] = useState(false);

    const departments = data ? data.departments : [];

    const departmentMutation = useMutation({
        mutationFn: async ({ method, url, body }: { method: string, url: string, body: any }) => {
            const response = await fetch(url, {
                method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(body),
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['departments'] });
            setIsSuccess(true);
            setTimeout(() => setIsSuccess(false), 2000); // Auto-close success overlay after 2 seconds
        },
        onError: () => {
            setShowErrorOverlay(true);
        },
        onSettled: () => {
            setIsOverlayOpen(false);
        },
    });

    const handleDepartmentSubmit = async (departmentData: Omit<Department, '_id'>) => {
        departmentMutation.mutate({ method: 'POST', url: '/api/departments/createDepartment', body: departmentData });
    };

    const handleDepartmentUpdate = async (id: string, departmentData: Partial<Department>) => {
        departmentMutation.mutate({ method: 'PUT', url: '/api/departments/updateDepartment', body: { id, ...departmentData } });
    };

    const handleDepartmentDelete = async (id: string) => {
        departmentMutation.mutate({ method: 'DELETE', url: '/api/departments/deleteDepartment', body: { id } });
    };

    const toggleOverlay = () => {
        setIsOverlayOpen(!isOverlayOpen);
    };

    const toggleEditOverlay = (department?: Department) => {
        if (department) {
            setSelectedDepartment(department);
        }
        setIsEditOverlayOpen(!isEditOverlayOpen);
    };

    return (
        <div className="container mx-auto py-10">
            <div className="flex justify-between items-center mb-4">
                <h1 className="text-2xl font-bold">Department Management</h1>
                <Button onClick={toggleOverlay} className="ml-4">Add Department</Button>
            </div>
            {isLoading ? (
                <p>Loading...</p>
            ) : isError ? (
                <p>Error fetching data: {error.message}</p>
            ) : (
                <DataTable columns={columns} data={departments} onEdit={toggleEditOverlay} onDelete={handleDepartmentDelete} />
            )}
            {isSuccess && <DepartmentSuccessOverlay onClose={() => setIsSuccess(false)} />}
            {showErrorOverlay && <DepartmentErrorOverlay onClose={() => setShowErrorOverlay(false)} />}
            {selectedDepartment && (
                <DepartmentEditOverlay
                    isOpen={isEditOverlayOpen}
                    onClose={() => toggleEditOverlay()}
                    department={selectedDepartment}
                    onSubmit={handleDepartmentUpdate}
                />
            )}
            {isOverlayOpen && (
                <DepartmentForm
                    onSubmit={handleDepartmentSubmit}
                    onClose={() => setIsOverlayOpen(false)}
                />
            )}
        </div>
    );
}
