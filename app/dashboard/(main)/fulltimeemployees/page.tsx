// import React from 'react'

// const FullTimeEmployees


// = () => {
//   return (
//     <div>FullTimeEmployees



//     </div>
//   )
// }

// export default FullTimeEmployees




// // app/(main)/employees/page.tsx
// import { DataTable } from "@/components/tables/reusables/data-table" 
// import { columns } from "@/components/employees/columns"
// import { Employee } from "@/app/types/employee"

// const employees: Employee[] = [
//   {
//     id: "1",
//     firstname: "Chisomo",
//     lastname: "Moyo",
//     email: "<EMAIL>",
//     position: "Software Engineer",
//     department: "Engineering",
//     status: "active",
//   },
//   {
//     id: "2",
//     firstname: "Thandiwe",
//     lastname: "Kamkwamba",
//     email: "<EMAIL>",
//     position: "HR Manager",
//     department: "Human Resources",
//     status: "inactive",
//   },
//   {
//     id: "3",
//     firstname: "<PERSON>",
//     lastname: "<PERSON><PERSON>",
//     email: "<EMAIL>",
//     position: "Finance Analyst",
//     department: "Finance",
//     status: "terminated",
//   },
//   {
//     id: "4",
//     firstname: "Grace",
//     lastname: "Phiri",
//     email: "<EMAIL>",
//     position: "Marketing Specialist",
//     department: "Marketing",
//     status: "active",
//   },
//   {
//     id: "5",
//     firstname: "Mike",
//     lastname: "Ngwira",
//     email: "<EMAIL>",
//     position: "Sales Executive",
//     department: "Sales",
//     status: "inactive",
//   },
//   {
//     id: "6",
//     firstname: "Enock",
//     lastname: "Chilima",
//     email: "<EMAIL>",
//     position: "Customer Support",
//     department: "Support",
//     status: "terminated",
//   },
//   {
//     id: "7",
//     firstname: "Linda",
//     lastname: "Nyirenda",
//     email: "<EMAIL>",
//     position: "Project Manager",
//     department: "Project Management",
//     status: "active",
//   },
//   {
//     id: "8",
//     firstname: "Patrick",
//     lastname: "Kumwenda",
//     email: "<EMAIL>",
//     position: "UX Designer",
//     department: "Design",
//     status: "inactive",
//   },
//   {
//     id: "9",
//     firstname: "Alice",
//     lastname: "Chirwa",
//     email: "<EMAIL>",
//     position: "DevOps Engineer",
//     department: "Engineering",
//     status: "terminated",
//   },
//   {
//     id: "10",
//     firstname: "Felix",
//     lastname: "Mphatso",
//     email: "<EMAIL>",
//     position: "Data Scientist",
//     department: "Data Science",
//     status: "active",
//   },
//   {
//     id: "11",
//     firstname: "Rose",
//     lastname: "Tembo",
//     email: "<EMAIL>",
//     position: "Accountant",
//     department: "Finance",
//     status: "inactive",
//   },
//   {
//     id: "12",
//     firstname: "Peter",
//     lastname: "Dzonzi",
//     email: "<EMAIL>",
//     position: "System Administrator",
//     department: "IT",
//     status: "terminated",
//   },
//   {
//     id: "13",
//     firstname: "Martha",
//     lastname: "Jere",
//     email: "<EMAIL>",
//     position: "Content Writer",
//     department: "Marketing",
//     status: "active",
//   },
//   {
//     id: "14",
//     firstname: "Charles",
//     lastname: "Msiska",
//     email: "<EMAIL>",
//     position: "Business Analyst",
//     department: "Business",
//     status: "inactive",
//   },
//   {
//     id: "15",
//     firstname: "Agnes",
//     lastname: "Mvula",
//     email: "<EMAIL>",
//     position: "QA Engineer",
//     department: "Engineering",
//     status: "terminated",
//   },
// ]

// export default function FullTimeEmployees() {
//   return (
//     <div className="container mx-auto py-10">
//       <DataTable columns={columns} data={employees} />
//     </div>
//   )
// }




"use client";

import * as React from "react";
import { DataTable } from "@/components/employees/data-table";
import { columns } from "@/components/employees/columns";
import { EmployeeForm } from "@/components/employees/employee-form";
import { Employee } from "@/app/types/employee";
import EmployeeEditOverlay from "@/components/employees/employee-edit-overlay";
import EmployeeSuccessOverlay from "@/components/employees/employee-success-overlay";
import EmployeeErrorOverlay from "@/components/employees/employee-error-overlay";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { Button } from "@/components/ui/button";

type FetchError = {
  message: string;
};

// Fetch employees function
const fetchEmployees = async () => {
  const response = await fetch("/api/employees/getAllEmployees");
  if (!response.ok) {
    throw new Error("Failed to fetch employees");
  }
  return response.json();
};

// Fetch departments function
const fetchDepartments = async () => {
  const response = await fetch("/api/departments/getAllDepartments");
  if (!response.ok) {
    throw new Error("Failed to fetch departments");
  }
  return response.json();
};

export default function FullTimeEmployees() {
  const queryClient = useQueryClient();
  const {
    data: employeeData,
    error: employeeError,
    isError: isEmployeeError,
    isLoading: isEmployeeLoading,
  } = useQuery({
    queryKey: ["employees"],
    queryFn: fetchEmployees,
  });
  const {
    data: departmentData,
    error: departmentError,
    isError: isDepartmentError,
    isLoading: isDepartmentLoading,
  } = useQuery({
    queryKey: ["departments"],
    queryFn: fetchDepartments,
  });

  const [isSuccess, setIsSuccess] = useState(false);
  const [isOverlayOpen, setIsOverlayOpen] = useState(false);
  const [isEditOverlayOpen, setIsEditOverlayOpen] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(
    null
  );
  const [showErrorOverlay, setShowErrorOverlay] = useState(false);

  const employees = employeeData ? employeeData.employees : [];
  const departments = departmentData ? departmentData.departments : [];

  const employeeMutation = useMutation({
    mutationFn: async ({
      method,
      url,
      body,
    }: {
      method: string;
      url: string;
      body: any;
    }) => {
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["employees"] });
      setIsSuccess(true);
      setIsOverlayOpen(false);
      setIsEditOverlayOpen(false);
    },
    onError: () => {
      setShowErrorOverlay(true);
    },
  });

  const handleAddEmployee = (employee: Omit<Employee, "_id">) => {
    employeeMutation.mutate({
      method: "POST",
      url: "/api/employees/createEmployee",
      body: employee,
    });
  };

  const handleUpdateEmployee = (
    employeeId: string,
    employee: Partial<Employee>
  ): Promise<void> => {
    const updatedFields = Object.fromEntries(
      Object.entries(employee).filter(([key, value]) => value !== undefined)
    );

    return new Promise((resolve, reject) => {
      employeeMutation.mutate({
        method: "PUT",
        url: `/api/employees/updateEmployee`,
        body: { id: employeeId, ...updatedFields },  // Pass the id in the body
      });
    });
  };

  const handleEditEmployee = (employee: Employee) => {
    setSelectedEmployee(employee);
    setIsEditOverlayOpen(true);
  };

  const handleDeleteEmployee = (employeeId: string) => {
    employeeMutation.mutate({
      method: "DELETE",
      url: `/api/employees/deleteEmployee`,
      body: { id: employeeId },  // Send the employeeId in the request body
    });
  };

  const handleCloseSuccessOverlay = () => {
    setIsSuccess(false);
  };

  const handleCloseErrorOverlay = () => {
    setShowErrorOverlay(false);
  };

  if (isEmployeeLoading || isDepartmentLoading) {
    return <div>Loading...</div>;
  }

  if (isEmployeeError || isDepartmentError) {
    return (
      <div>
        Error loading data: {(employeeError as FetchError)?.message || (departmentError as FetchError)?.message}
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Employee Management</h1>
        <Button onClick={() => setIsOverlayOpen(true)} className="ml-4">
          Add Employee
        </Button>
      </div>
      {isEmployeeLoading ? (
        <p>Loading...</p>
      ) : isEmployeeError ? (
        <p>Error fetching data: {(employeeError as FetchError).message}</p>
      ) : (
        <DataTable
          columns={columns}
          data={employees}
          renderRowActions={(row: Employee) => (
            <div className="flex space-x-2">
              <Button size="sm" onClick={() => handleEditEmployee(row)}>
                Edit
              </Button>
              <Button size="sm" variant="destructive" onClick={() => handleDeleteEmployee(row._id)}>
                Delete
              </Button>
            </div>
          )}
        />
      )}
      {isSuccess && (
        <EmployeeSuccessOverlay onClose={handleCloseSuccessOverlay} />
      )}
      {showErrorOverlay && (
        <EmployeeErrorOverlay onClose={handleCloseErrorOverlay} />
      )}
      {selectedEmployee && (
        <EmployeeEditOverlay
          isOpen={isEditOverlayOpen}
          onClose={() => setIsEditOverlayOpen(false)}
          employee={selectedEmployee}
          onSubmit={handleUpdateEmployee}
          departments={departments} // Pass the departments array
        />
      )}
      {isOverlayOpen && (
        <EmployeeForm
          departments={departments}
          onSubmit={handleAddEmployee}
          isOpen={isOverlayOpen}
          onClose={() => setIsOverlayOpen(false)}
        />
      )}
    </div>
  );
}
