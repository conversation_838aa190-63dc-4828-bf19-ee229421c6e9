// app/monthly-attendance/page.tsx

import { DataTable } from "@/components/monthlyAttendance/data-table"
import { columns } from "@/components/monthlyAttendance/columns"
import { Attendance } from "@/app/types/attendance"
import { aggregateMonthlyAttendance } from "@/lib/aggregateAttendance"

const attendanceData: Attendance[] = [
  {
    id: "1",
    date: "2023-07-01",
    status: "present",
    employeeId: "emp1",
    firstname: "<PERSON>",
    lastname: "<PERSON><PERSON>",
  },
  {
    id: "2",
    date: "2023-07-01",
    status: "absent",
    employeeId: "emp2",
    firstname: "<PERSON>",
    lastname: "<PERSON>",
  },
  {
    id: "3",
    date: "2023-07-02",
    status: "late",
    employeeId: "emp1",
    firstname: "<PERSON>",
    lastname: "<PERSON><PERSON>",
  },
  // Add more attendance records...
]

const monthlyAttendanceData = aggregateMonthlyAttendance(attendanceData)

export default function MonthlyAttendancePage() {
  return (
    <div>
      <h1 className="text-2xl font-bold mb-4">Monthly Attendance Records</h1>
      <DataTable columns={columns} data={monthlyAttendanceData} />
    </div>
  )
}
