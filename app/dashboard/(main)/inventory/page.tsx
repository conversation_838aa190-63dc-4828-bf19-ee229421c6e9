// app/pages/inventory.tsx
"use client"
import * as React from "react"
import { DataTable } from "@/components/inventory/data-table"
import { columns } from "@/components/inventory/columns"
import { InventoryItem } from "@/app/types/inventory"

// Example function to fetch inventory data
async function getInventoryData(): Promise<InventoryItem[]> {
  // Example implementation: Replace with actual data fetching logic
  return [
    {
      itemId: "1",
      itemName: "Laptop",
      quantity: 10,
      category: "Electronics",
      supplier: "Tech Supplier",
      purchaseDate: "2024-07-01",
      status: "in-stock",
    },
    {
      itemId: "2",
      itemName: "Office Chair",
      quantity: 15,
      category: "Furniture",
      supplier: "Office Supply Co.",
      purchaseDate: "2024-06-15",
      status: "in-stock",
    },
    {
      itemId: "3",
      itemName: "Printer",
      quantity: 2,
      category: "Electronics",
      supplier: "Tech Supplier",
      purchaseDate: "2024-05-20",
      status: "out-of-stock",
    },
    // Add more inventory data as needed
  ]
}

export default function InventoryPage() {
  const [inventory, setInventory] = React.useState<InventoryItem[]>([])

  React.useEffect(() => {
    async function fetchInventoryData() {
      const data = await getInventoryData()
      setInventory(data)
    }
    fetchInventoryData()
  }, [])

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-2xl font-bold mb-4">All Products </h1>
      <DataTable columns={columns} data={inventory} />
    </div>
  )
}
