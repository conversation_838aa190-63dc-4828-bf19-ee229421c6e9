//  app/api/dashboard/stats/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import mongoose from 'mongoose';

export const runtime = 'nodejs';



// No need for employeeService instance as we're using direct database access

/**
 * GET /api/dashboard/stats
 * Get dashboard statistics
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check permissions - allow most authenticated users to view dashboard stats
    if (!hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_OFFICER,
      UserRole.EMPLOYEE,
      UserRole.MANAGER
    ])) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const fromDate = searchParams.get('fromDate');
    const toDate = searchParams.get('toDate');

    // Calculate date range for "new hires" and other time-based metrics
    const currentDate = new Date();
    const lastMonthDate = new Date();
    lastMonthDate.setMonth(currentDate.getMonth() - 1);

    // Format dates for query
    const fromDateObj = fromDate ? new Date(fromDate) : lastMonthDate;
    const toDateObj = toDate ? new Date(toDate) : currentDate;

    try {
      // Get employee statistics
      // Create a mock implementation of the statistics report
      const Employee = mongoose.models.Employee;

      // Get employee counts by status
      const statusResult = await Employee.aggregate([
        {
          $group: {
            _id: '$employmentStatus',
            count: { $sum: 1 }
          }
        }
      ]);

      const totalEmployees = statusResult.reduce((sum, item) => sum + item.count, 0);
      const activeEmployees = statusResult.find(item => item._id === 'active')?.count || 0;
      const inactiveEmployees = statusResult.find(item => item._id === 'inactive')?.count || 0;
      const onLeaveEmployees = statusResult.find(item => item._id === 'on-leave')?.count || 0;
      const terminatedEmployeesCount = statusResult.find(item => item._id === 'terminated')?.count || 0;

      // Get department distribution
      const departmentResult = await Employee.aggregate([
        {
          $lookup: {
            from: 'departments',
            localField: 'departmentId',
            foreignField: '_id',
            as: 'department'
          }
        },
        {
          $unwind: {
            path: '$department',
            preserveNullAndEmptyArrays: true
          }
        },
        {
          $group: {
            _id: '$department.name',
            count: { $sum: 1 }
          }
        },
        {
          $sort: { count: -1 }
        }
      ]);

      const departmentDistribution = departmentResult.map(item => ({
        department: item._id || 'No Department',
        count: item.count
      }));

      // Get employment type distribution
      const typeResult = await Employee.aggregate([
        {
          $group: {
            _id: '$employmentType',
            count: { $sum: 1 }
          }
        },
        {
          $sort: { count: -1 }
        }
      ]);

      const employmentTypeDistribution = typeResult.map(item => ({
        type: item._id || 'Unknown',
        count: item.count
      }));

      // Create the employee stats object
      const employeeStats = {
        totalEmployees,
        activeEmployees,
        inactiveEmployees,
        onLeaveEmployees,
        terminatedEmployees: terminatedEmployeesCount,
        departmentDistribution,
        employmentTypeDistribution,
        tenureDistribution: []
      };

      // Get new hires (employees hired in the last month)
      const newHires = await Employee.countDocuments({
        hireDate: { $gte: fromDateObj, $lte: toDateObj }
      });

      // Get previous month's new hires for comparison
      const twoMonthsAgo = new Date(lastMonthDate);
      twoMonthsAgo.setMonth(lastMonthDate.getMonth() - 1);

      const previousMonthNewHires = await Employee.countDocuments({
        hireDate: { $gte: twoMonthsAgo, $lte: lastMonthDate }
      });

      // Calculate new hire change percentage
      const newHireChange = previousMonthNewHires > 0
        ? ((newHires - previousMonthNewHires) / previousMonthNewHires) * 100
        : 0;

      // Calculate turnover rate
      // Turnover rate = (Number of employees who left during period / Average number of employees) * 100
      const terminatedEmployeesInPeriod = await Employee.countDocuments({
        terminationDate: { $gte: fromDateObj, $lte: toDateObj }
      });

      const avgEmployees = employeeStats.totalEmployees;
      const turnoverRate = avgEmployees > 0 ? (terminatedEmployeesInPeriod / avgEmployees) * 100 : 0;

      // Get previous month's turnover rate for comparison
      const previousTerminatedEmployees = await Employee.countDocuments({
        terminationDate: { $gte: twoMonthsAgo, $lte: lastMonthDate }
      });

      // Get previous month's total employees (approximate)
      const previousTotalEmployees = employeeStats.totalEmployees - newHires + terminatedEmployeesInPeriod;
      const previousTurnoverRate = previousTotalEmployees > 0
        ? (previousTerminatedEmployees / previousTotalEmployees) * 100
        : 0;

      // Calculate turnover rate change
      const turnoverRateChange = previousTurnoverRate > 0
        ? ((turnoverRate - previousTurnoverRate) / previousTurnoverRate) * 100
        : 0;

      // Get attendance data (mock for now, would be replaced with real data)
      // In a real implementation, this would come from an attendance service
      const attendanceRate = 96.8;
      const previousAttendanceRate = 96.2;
      const attendanceRateChange = ((attendanceRate - previousAttendanceRate) / previousAttendanceRate) * 100;

      // Combine all stats
      const dashboardStats = {
        employees: {
          total: employeeStats.totalEmployees,
          active: employeeStats.activeEmployees,
          inactive: employeeStats.inactiveEmployees,
          onLeave: employeeStats.onLeaveEmployees,
          terminated: employeeStats.terminatedEmployees,
          // Calculate change percentage (mock for now)
          changePercentage: 12
        },
        newHires: {
          count: newHires,
          inProbation: Math.round(newHires * 0.4), // Assuming 40% of new hires are in probation
          changePercentage: newHireChange
        },
        turnover: {
          rate: turnoverRate.toFixed(1),
          changePercentage: turnoverRateChange
        },
        attendance: {
          rate: attendanceRate,
          changePercentage: attendanceRateChange
        },
        departmentDistribution: employeeStats.departmentDistribution,
        employmentTypeDistribution: employeeStats.employmentTypeDistribution
      };

      return NextResponse.json(dashboardStats);
    } catch (error: unknown) {
      // If no data is found, return empty structure
      if (error instanceof Error && error.message === 'No employee data found') {
        logger.info('No employee data found, returning empty structure', LogCategory.HR);
        return NextResponse.json({
          employees: {
            total: 0,
            active: 0,
            inactive: 0,
            onLeave: 0,
            terminated: 0,
            changePercentage: 0
          },
          newHires: {
            count: 0,
            inProbation: 0,
            changePercentage: 0
          },
          turnover: {
            rate: 0,
            changePercentage: 0
          },
          attendance: {
            rate: 0,
            changePercentage: 0
          },
          departmentDistribution: [],
          employmentTypeDistribution: []
        }, { status: 200 });
      }
      // Re-throw for other errors
      throw error;
    }
  } catch (error: unknown) {
    logger.error('Error fetching dashboard statistics:', LogCategory.API, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
