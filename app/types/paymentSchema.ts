// // import * as z from "zod";

// // export const loanDeductionSchema = z.object({
// //     social_welfare: z.string().optional().default('0'),
// //     salary_advance: z.string().optional().default('0'),
// //     medical: z.string().optional().default('0'),
// //     extra: z.array(z.object({
// //         type: z.string(),
// //         amount: z.string().default('0')
// //     })).optional().default([])
// // });

// // export const paymentSchema = z.object({
// //     amount: z.string().nonempty("Amount is required"),
// //     bonus_payment: z.string().optional().default('0'),
// //     status: z.enum(['pending', 'revised', 'review', 'approved', 'processing', 'failed', 'cancel']),
// //     employee: z.string().nonempty("Employee is required"),
// //     tax: z.string().optional().default('0'),
// //     loan_deduction: loanDeductionSchema,
// //     insurance: z.string().optional().default('0'),
// //     pension_deduction: z.string().optional().default('0'),
// //     final_amount: z.string().optional().default('0')
// // });

// // export type PaymentFormValues = z.infer<typeof paymentSchema>;



// // paymentSchema.ts
// import * as z from "zod";

// export const loanDeductionSchema = z.object({
//     social_welfare: z.string().optional().default('0'),
//     salary_advance: z.string().optional().default('0'),
//     medical: z.string().optional().default('0'),
//     extra: z.array(z.object({
//         type: z.string(),
//         amount: z.string().default('0')
//     })).optional().default([])
// });

// export const paymentSchema = z.object({
//     amount: z.string().nonempty("Amount is required"),
//     bonus_payment: z.string().optional().default('0'),
//     status: z.enum(['pending', 'revised', 'review', 'approved', 'processing', 'failed', 'cancel']),
//     employee: z.string().nonempty("Employee is required"),
//     tax: z.string().optional().default('0'),
//     loan_deduction: loanDeductionSchema,
//     insurance: z.string().optional().default('0'),
//     pension_deduction: z.string().optional().default('0'),
//     final_amount: z.string().optional().default('0')
// });

// export type PaymentFormValues = z.infer<typeof paymentSchema>;



import * as z from "zod";

export const loanDeductionSchema = z.object({
    social_welfare: z.string().optional().default('0'),
    salary_advance: z.string().optional().default('0'),
    medical: z.string().optional().default('0'),
    extra: z.array(z.object({
        type: z.string(),
        amount: z.string().default('0')
    })).optional().default([])
});

export const paymentSchema = z.object({
    amount: z.string().nonempty("Amount is required"),
    bonus_payment: z.string().optional().default('0'),
    status: z.enum(['pending', 'revised', 'review', 'approved', 'processing', 'failed', 'cancel']),
    employee: z.string().nonempty("Employee is required"),
    tax: z.string().optional().default('0'),
    loan_deduction: loanDeductionSchema,
    insurance: z.string().optional().default('0'),
    pension_deduction: z.string().optional().default('0'),
    final_amount: z.string().optional().default('0'),
    day_rate: z.string().optional().default('0'),          // New field for daily rate
    working_days: z.string().optional().default('0'),      // New field for working days
    attendance_days: z.string().optional().default('0'),   // New field for attendance days
});

export type PaymentFormValues = z.infer<typeof paymentSchema>;
