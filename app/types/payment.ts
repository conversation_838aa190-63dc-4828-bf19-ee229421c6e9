
// app/types/payment.ts
// import { Employee } from './employee';

// export interface Payment {
//   _id: string;
//   // amount: number;
//   amount: string;
//   status: 'success' | 'processing' | 'failed' | 'pending';
//   employee: string | { _id: string; firstName: string; lastName: string; phone_number: string };
// }


// // types/payment.ts
// export interface Payment {
//   _id: string;
//   amount: string;
//   status: 'success' | 'processing' | 'failed' | 'pending';
//   employee: string | { _id: string; firstName: string; lastName: string; phone_number: string };
//   tax?: string;
//   loan_deduction?: string;
//   insurance?: string;
// }




// app/types/payment.ts

// export interface Payment {
//   _id: string;
//   amount: string;
//   status: 'pending' | 'revised' | 'review' | 'approved' | 'processing' | 'failed' | 'cancel';
//   employee: string | { _id: string; firstName: string; lastName: string; phone_number: string };
//   tax?: string;
//   loan_deduction?: string;
//   insurance?: string;
//   final_amount?: string;
// }



// // types.ts

// export type PaymentStatus = 'pending' | 'revised' | 'review' | 'approved' | 'processing' | 'failed' | 'cancel';

// export interface Payment {
//   lastName: any;
//   firstName: any;
//   _id: string;
//   amount: string;
//   status: PaymentStatus;
//   employee: string | { _id: string; firstName: string; lastName: string; phone_number: string };
//   tax?: string;
//   loan_deduction?: string;
//   insurance?: string;
//   final_amount?: string;
// }



// export type PaymentStatus = 'pending' | 'revised' | 'review' | 'approved' | 'processing' | 'failed' | 'cancel';

// export interface LoanDeduction {
//   social_welfare: string;
//   salary_advance: string;
//   medical: string;
//   extra: {
//     type: string;
//     amount: string;
//   }[];
// }

// export interface Payment {
//   _id: string;
//   amount: string;
//   bonus_payment?: string;
//   status: PaymentStatus;
//   employee: string | { _id: string; firstName: string; lastName: string; phone_number: string };
//   tax?: string;
//   loan_deduction: LoanDeduction;
//   insurance?: string;
//   pension_deduction?: string;
//   final_amount?: string;
// }



// export type PaymentStatus = 'pending' | 'revised' | 'review' | 'approved' | 'processing' | 'failed' | 'cancel';

// export interface LoanDeduction {
//   social_welfare: string;
//   salary_advance: string;
//   medical: string;
//   extra: {
//     type: string;
//     amount: string;
//   }[];
// }

// export interface Payment {
//   _id: string;
//   amount: string;
//   bonus_payment?: string;
//   status: PaymentStatus;
//   employee: string | {
//     [x: string]: string |
//     // import { Employee } from './employee';
//     // export interface Payment {
//     //   _id: string;
//     //   // amount: number;
//     //   amount: string;
//     //   status: 'success' | 'processing' | 'failed' | 'pending';
//     //   employee: string | { _id: string; firstName: string; lastName: string; phone_number: string };
//     // }
//     // // types/payment.ts
//     // export interface Payment {
//     //   _id: string;
//     //   amount: string;
//     //   status: 'success' | 'processing' | 'failed' | 'pending';
//     //   employee: string | { _id: string; firstName: string; lastName: string; phone_number: string };
//     //   tax?: string;
//     //   loan_deduction?: string;
//     //   insurance?: string;
//     // }
//     // app/types/payment.ts
//     // export interface Payment {
//     //   _id: string;
//     //   amount: string;
//     //   status: 'pending' | 'revised' | 'review' | 'approved' | 'processing' | 'failed' | 'cancel';
//     //   employee: string | { _id: string; firstName: string; lastName: string; phone_number: string };
//     //   tax?: string;
//     //   loan_deduction?: string;
//     //   insurance?: string;
//     //   final_amount?: string;
//     // }
//     // // types.ts
//     // export type PaymentStatus = 'pending' | 'revised' | 'review' | 'approved' | 'processing' | 'failed' | 'cancel';
//     // export interface Payment {
//     //   lastName: any;
//     //   firstName: any;
//     //   _id: string;
//     //   amount: string;
//     //   status: PaymentStatus;
//     //   employee: string | { _id: string; firstName: string; lastName: string; phone_number: string };
//     //   tax?: string;
//     //   loan_deduction?: string;
//     //   insurance?: string;
//     //   final_amount?: string;
//     // }
//     // export type PaymentStatus = 'pending' | 'revised' | 'review' | 'approved' | 'processing' | 'failed' | 'cancel';
//     // export interface LoanDeduction {
//     //   social_welfare: string;
//     //   salary_advance: string;
//     //   medical: string;
//     //   extra: {
//     //     type: string;
//     //     amount: string;
//     //   }[];
//     // }
//     // export interface Payment {
//     //   _id: string;
//     //   amount: string;
//     //   bonus_payment?: string;
//     //   status: PaymentStatus;
//     //   employee: string | { _id: string; firstName: string; lastName: string; phone_number: string };
//     //   tax?: string;
//     //   loan_deduction: LoanDeduction;
//     //   insurance?: string;
//     //   pension_deduction?: string;
//     //   final_amount?: string;
//     // }
//     number | readonly string[] | undefined; _id: string; firstName: string; lastName: string; phone_number: string 
// };
//   tax?: string;
//   loan_deduction: LoanDeduction;
//   insurance?: string;
//   pension_deduction?: string;
//   final_amount?: string;
//   day_rate?: string;         // New field for daily rate
//   working_days?: string;     // New field for working days
//   attendance_days?: string;  // New field for attendance days
// }




// export type PaymentStatus = 
//   | 'pending' 
//   | 'revised' 
//   | 'review' 
//   | 'approved' 
//   | 'processing' 
//   | 'failed' 
//   | 'cancel';

// export interface LoanDeduction {
//   social_welfare: string;
//   salary_advance: string;
//   medical: string;
//   extra: {
//     type: string;
//     amount: string;
//   }[];
// }

// export interface Payment {
//   _id: string;
//   amount: string;
//   bonus_payment?: string;
//   status: PaymentStatus;
//   employee: 
//     | string 
//     | {
//         _id: string;
//         firstName: string;
//         lastName: string;
//         phone_number: string;
//       };
//   tax?: string;
//   loan_deduction: LoanDeduction;
//   insurance?: string;
//   pension_deduction?: string;
//   final_amount?: string;
//   day_rate?: string;         // New field for daily rate
//   working_days?: string;     // New field for working days
//   attendance_days?: string;  // New field for attendance days
// }



// export type PaymentStatus = 
//   | 'pending' 
//   | 'revised' 
//   | 'review' 
//   | 'approved' 
//   | 'processing' 
//   | 'failed' 
//   | 'cancel';

// export interface LoanDeduction {
//   social_welfare: string;
//   salary_advance: string;
//   medical: string;
//   extra: {
//     type: string;
//     amount: string;
//   }[];
// }

// export interface EmployeeDetails {
//   _id: string;
//   firstName: string;
//   lastName: string;
//   phone_number: string;
//   employeeIdentity: string;  // Add employeeIdentity here
// }

// export interface Payment {
//   _id: string;
//   amount: string;
//   bonus_payment?: string;
//   status: PaymentStatus;
//   employee: string | EmployeeDetails; // Employee can be a string or an object
//   tax?: string;
//   loan_deduction: LoanDeduction;
//   insurance?: string;
//   pension_deduction?: string;
//   final_amount?: string;
//   day_rate?: string;         // New field for daily rate
//   working_days?: string;     // New field for working days
//   attendance_days?: string;  // New field for attendance days
// }





// export type PaymentStatus = 
//   | 'pending' 
//   | 'revised' 
//   | 'review' 
//   | 'approved' 
//   | 'processing' 
//   | 'failed' 
//   | 'cancel';

// export interface LoanDeduction {
//   social_welfare: string;
//   salary_advance: string;
//   medical: string;
//   extra: {
//     type: string;
//     amount: string;
//   }[];
// }

// export interface EmployeeDetails {
//   _id: string;
//   firstName: string;
//   lastName: string;
//   phone_number: string;
//   employeeIdentity: string;  // Add employeeIdentity here
// }

// // export interface Payment {
// //   _id: string;
// //   amount: string;
// //   bonus_payment?: string;
// //   status: PaymentStatus;
// //   employee: string | EmployeeDetails; // Employee can be a string (ID) or an object
// //   tax?: string;
// //   loan_deduction: LoanDeduction;
// //   insurance?: string;
// //   pension_deduction?: string;
// //   final_amount?: string;
// //   day_rate?: string;         // Field for daily rate
// //   working_days?: string;     // Field for working days
// //   attendance_days?: string;  // Field for attendance days
// // }

// export interface Payment {
//   _id: string;
//   amount: string;
//   bonus_payment?: string;
//   status: PaymentStatus;
//   employee: string | EmployeeDetails;
//   tax?: string;
//   loan_deduction: LoanDeduction;
//   insurance?: string;
//   pension_deduction?: string;
//   final_amount?: string;
//   day_rate?: string;
//   working_days?: string;
//   attendance_days?: string;
//   employeeIdentity?: string; // Add employeeIdentity here
// }





export type PaymentStatus = 
  | 'pending' 
  | 'revised' 
  | 'review' 
  | 'approved' 
  | 'processing' 
  | 'failed' 
  | 'cancel';

export interface LoanDeduction {
  social_welfare: string;
  salary_advance: string;
  medical: string;
  extra: {
    type: string;
    amount: string;
  }[];
}

export interface EmployeeDetails {
  _id: string;
  firstName: string;
  lastName: string;
  phone_number: string;
  employeeIdentity: string;  // Add employeeIdentity here
}

export interface Payment {
  _id: string;
  amount: string;
  bonus_payment?: string;
  status: PaymentStatus;
  employee: string | EmployeeDetails;
  tax?: string;
  loan_deduction: LoanDeduction;
  insurance?: string;
  pension_deduction?: string;
  final_amount?: string;
  day_rate?: string;
  working_days?: string;
  attendance_days?: string;
  employeeIdentity?: string; // Add employeeIdentity here
  month?: number;  // Add month property here
  year?: number;   // Add year property here
}


// export interface Payment {
//   _id: string;
//   amount: string;
//   bonus_payment?: string;
//   status: PaymentStatus;
//   employee: 
//     | string 
//     | {
//         _id: string;
//         employeeIdentity: string;
//         firstName: string;
//         lastName: string;
//         phone_number: string;
//       };
//   tax?: string;
//   loan_deduction: LoanDeduction;
//   insurance?: string;
//   pension_deduction?: string;
//   final_amount?: string;
//   day_rate?: string;         // New field for daily rate
//   working_days?: string;     // New field for working days
//   attendance_days?: string;  // New field for attendance days
// }
