// app/types/leave.ts

// export type Leave = {
//     leaveId: string
//     employeeName: string
//     startDate: string
//     endDate: string
//     reason: string
//     status: "approved" | "pending" | "rejected"
//   }
  

// app/types/leave.ts

export type Leave = {
    leaveId: string
    employeeName: string
    leaveType: "annual" | "maternity" | "educational" | "sick" | "funeral"
    startDate: string
    endDate: string
    reason: string
    status: "approved" | "pending" | "rejected"
  }
  

  // app/types/leave.ts

export type LeaveRequest = {
    employeeName: string
    leaveType: string
    startDate: string
    endDate: string
    reason: string
  }
  