// app/types/task.ts

  export type Task = {
    id: string
    title: string
    description: string
    dueDate: string
    priority: "low" | "medium" | "high"
    status: "not started" | "in progress" | "completed" | "canceled"
    assignee: string
  }


  // export type Task = {
//     id: string;
//     title: string;
//     description: string;
//     assignedTo: string;
//     status: "todo" | "in_progress" | "completed" | "archived";
//     dueDate: Date;
//   };
  

// app/types/task.ts
// export type Task = {
//   id: string
//   title: string
//   description: string
//   dueDate: string
//   priority: "low" | "medium" | "high"
//   status: "not started" | "in progress" | "completed"
//   assignee: string
// }
