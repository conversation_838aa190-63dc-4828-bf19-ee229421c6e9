

// // app/types/employee.ts
// export interface Employee {
//   _id: string;
//   firstName: string;
//   lastName: string;
//   jobTitle: string;
//   salary: string;
//   hireDate: string; // Use string to be consistent with how dates are handled in forms
//   department: string;
//   email: string;
//   phone: string;
//   gender: 'male' | 'female' | 'other';
//   dateOfBirth: string; // Use string to be consistent with how dates are handled in forms
//   homeOrigin: string;
//   residence: string;
//   maritalStatus: 'single' | 'married' | 'divorced' | 'widowed';
//   nextOfKin: string;
//   nextOfKinContact: string;
//   status: 'active' | 'inactive' | 'terminated';
// }

// app/types/employee.ts
export interface Employee {
  _id: string;
  employeeIdentity: string;
  firstName: string;
  lastName: string;
  jobTitle: string;
  salary: string;
  hireDate: string; // Use string to be consistent with how dates are handled in forms
  department: string | { _id: string; name: string }; // Update to include the populated department object
  email: string;
  phone: string;
  gender: 'male' | 'female' | 'other';
  dateOfBirth: string; // Use string to be consistent with how dates are handled in forms
  homeOrigin: string;
  residence: string;
  maritalStatus: 'single' | 'married' | 'divorced' | 'widowed';
  nextOfKin: string;
  nextOfKinContact: string;
  status: 'active' | 'inactive' | 'terminated';
}
