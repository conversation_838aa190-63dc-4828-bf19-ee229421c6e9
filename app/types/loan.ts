// app/types/loan.ts

// app/app/types/loan.ts

// export type Loan = {
//     loanId: string
//     borrower: string
//     loanAmount: number
//     interestRate: number
//     dueDate: string
//     status: "approved" | "pending" | "rejected"
//   }

// app/types/loan.ts
export interface Loan {
  _id: string;
  employee: string | { _id: string; firstName: string; lastName: string };
  amount: number;
  interestRate: number;
  startDate: string; // Use string to be consistent with how dates are handled in forms
  endDate: string; // Use string to be consistent with how dates are handled in forms
  status: 'pending' | 'approved' | 'rejected';
  repaymentSchedule: 'weekly' | 'monthly';
}


// Sample data for the Loans module
//   export const sampleLoans: Loan[] = [
//     {
//       loanId: "LN001",
//       borrower: "<PERSON>",
//       loanAmount: 10000,
//       interestRate: 5,
//       dueDate: "2024-12-01",
//       status: "approved",
//     },
//     {
//       loanId: "LN002",
//       borrower: "<PERSON>",
//       loanAmount: 15000,
//       interestRate: 4.5,
//       dueDate: "2025-01-15",
//       status: "pending",
//     },
//     {
//       loanId: "LN003",
//       borrower: "<PERSON>",
//       loanAmount: 20000,
//       interestRate: 6,
//       dueDate: "2024-11-20",
//       status: "approved",
//     },
//     {
//       loanId: "LN004",
//       borrower: "Emily Davis",
//       loanAmount: 12000,
//       interestRate: 5.5,
//       dueDate: "2024-10-30",
//       status: "rejected",
//     },
//     {
//       loanId: "LN005",
//       borrower: "William Brown",
//       loanAmount: 18000,
//       interestRate: 4.8,
//       dueDate: "2025-03-10",
//       status: "approved",
//     },
//     {
//       loanId: "LN006",
//       borrower: "Jessica Wilson",
//       loanAmount: 25000,
//       interestRate: 5.2,
//       dueDate: "2025-02-20",
//       status: "pending",
//     },
//     {
//       loanId: "LN007",
//       borrower: "David Martinez",
//       loanAmount: 22000,
//       interestRate: 6.5,
//       dueDate: "2024-09-15",
//       status: "rejected",
//     },
//     {
//       loanId: "LN008",
//       borrower: "Sarah Garcia",
//       loanAmount: 14000,
//       interestRate: 5,
//       dueDate: "2024-08-25",
//       status: "approved",
//     },
//     {
//       loanId: "LN009",
//       borrower: "Daniel Rodriguez",
//       loanAmount: 16000,
//       interestRate: 4.9,
//       dueDate: "2025-04-05",
//       status: "pending",
//     },
//     {
//       loanId: "LN010",
//       borrower: "Laura Hernandez",
//       loanAmount: 13000,
//       interestRate: 5.1,
//       dueDate: "2024-07-30",
//       status: "approved",
//     },
//   ]
