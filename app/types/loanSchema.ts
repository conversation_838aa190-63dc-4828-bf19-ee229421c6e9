import * as z from "zod";

export const loanSchema = z.object({
    employee: z.string().nonempty("Employee is required"),
    amount: z.string().nonempty("Amount is required"),
    interestRate: z.string().nonempty("Interest rate is required"),
    startDate: z.string().nonempty("Start date is required"),
    endDate: z.string().nonempty("End date is required"),
    status: z.enum(['pending', 'approved', 'rejected']),
    repaymentSchedule: z.enum(['weekly', 'monthly'])
});

export type LoanFormValues = z.infer<typeof loanSchema>;
